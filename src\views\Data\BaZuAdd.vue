<template>
  <div style="display: flex; justify-content: end" class="mb-2">
    <el-dialog :visible.sync="show" title="新增巴组">
      <el-card>
        <el-form label-width="100px" style="padding-right: 50px" :model="form" :rules="rules" ref="formRef">
          <el-form-item prop="groname" label="巴组名">
            <el-input v-model="form.groname" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="巴组管理" prop="grohmname">
            <!-- <el-autocomplete
              value-key="name"
              v-model="form.grohmname"
              :fetch-suggestions="querySearchAsync"
              placeholder="请输入姓"
              @select="handleSelect"
              @focus="loadAll"
            ></el-autocomplete> -->
            <el-select
              v-model="form.groheadman"
              @change="form.grohmname = groupAdmins.find((item) => item.loginid == form.groheadman).name"
              filterable
            >
              <el-option v-for="item of groupAdmins" :label="item.name" :value="item.loginid"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="groheadman" label="巴组管理工号">
            <el-input v-model="form.groheadman" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item prop="groremarks" label="巴组简介">
            <el-input type="textarea" :row="3" v-model="form.groremarks" autocomplete="off"></el-input>
          </el-form-item>
        </el-form>
        <div>
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="save">确 定</el-button>
        </div>
      </el-card>
    </el-dialog>
    <el-button type="primary" @click="show = true" round>新增巴组</el-button>
  </div>
</template>

<script>
import service from "@/router/request";

export default {
  name: "BaZuAdd",
  data() {
    return {
      show: false,
      form: {},
      rules: {
        grohmname: [{ required: true, message: "巴长不能为空", trigger: "blur" }],
        groname: [{ required: true, message: "巴组名不能为空", trigger: "blur" }],
      },
      restaurants: [],
      groupAdmins: [],
    };
  },
  mounted() {
    this.loadAll();
  },
  methods: {
    save() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          // 表单验证通过，执行保存逻辑

          // 执行保存操作
          // 可以在这里调用接口，将表单数据传递给后端进行保存
          service.post("/Bazu/addBazu", this.form).then((res) => {
            if (res.status == "succeed") {
              // 表示成功保存
              this.$message.success("添加成功");

              this.show = false; // 关闭对话框
              this.$router.go(0);
            } else {
              this.$message.error(res.err); // 弹出错误的信息
            }
          });
        }
      });
    },
    cancel() {
      this.show = false; // 关闭对话框
      // 清空表单数据
      this.form = {};
    },
    //查询当前所有人员
    // querySearchAsync(queryString, cb) {
    //   var restaurants = this.restaurants;
    //   var results = queryString ? restaurants.filter(this.createStateFilter(queryString)) : restaurants;

    //   clearTimeout(this.timeout);
    //   this.timeout = setTimeout(() => {
    //     cb(results);
    //   }, 300);
    // },
    // createStateFilter(queryString) {
    //   return (restaurant) => {
    //     return restaurant.name.toLowerCase().indexOf(queryString.toLowerCase()) === 0;
    //   };
    // },
    // handleSelect(item) {
    //   debugger;
    //   this.form.groheadman = item.loginid;
    // },
    //查询出所有成员
    // loadAll() {
    //   service
    //     .get("/Public/Getmember")
    //     .then((res) => {
    //       if (res.status == "succeed") {
    //         this.restaurants = res.data;
    //       }
    //     })
    //     .catch((error) => {
    //       console.error("Error fetching restaurants", error);
    //     });
    // },
    loadAll() {
      service
        .post("/Public/GetSystemMember", { roleId: 1 })
        .then((res) => {
          if (res.status == "succeed") {
            this.groupAdmins = res.data || null;
          }
        })
        .catch((error) => {
          console.error("Error fetching restaurants", error);
        });
    },
  },
};
</script>

<style scoped></style>
