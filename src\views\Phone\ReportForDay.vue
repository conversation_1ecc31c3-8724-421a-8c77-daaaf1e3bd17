<template>
  <div class="container" id="container">
    <div style="padding: 20px 10px 15px; position: sticky; top: 0; z-index: 9">
      <el-button @click="$router.back()" size="medium" icon="el-icon-back" circle></el-button>
    </div>
    <h2 class="title">每日日报</h2>
    <div style="display: flex; margin: 0 10px 10px; justify-content: flex-end; align-items: center">
      <span style="white-space: nowrap">日期切换：</span>
      <el-date-picker v-model="date" @change="dateChange" placeholder="选择日期" value-format="yyyy-MM-dd">
      </el-date-picker>
      <el-button v-if="!isMobile()" @click="exportData" type="warning" class="ml-1">导出</el-button>
    </div>
    <el-timeline style="margin-right: 10px; margin-bottom: auto">
      <el-timeline-item
        v-for="(item, idx) of taskHoursList"
        :key="`timeline${idx}`"
        :timestamp="item.timestamp"
        placement="top"
      >
        <el-card style="position: relative">
          <template v-for="cItem of item.list">
            <el-collapse>
              <el-collapse-item class="task-title">
                <template slot="title">
                  <p style="margin: 0">
                    录入{{ getTaskType(cItem) }}《{{ cItem.taskName }}》{{ cItem.hours }}工时
                    <i
                      @click.stop="
                        taskHours = { ...cItem };
                        updateHoursDlg = true;
                      "
                      class="el-icon-edit"
                    ></i>
                    <i @click.stop="deleteTask(item, cItem)" class="el-icon-delete"></i>
                  </p>
                </template>
                <template v-if="cItem.taskNo.includes('TAD')">
                  <div>任务编号：{{ cItem.task.tadno }}</div>
                  <div>工程编码：{{ cItem.task.prono }}</div>
                  <div>部位{{ cItem.task.mpaname }}</div>
                  <div>组件：{{ cItem.task.sasname }}</div>
                  <div>子件：{{ cItem.task.ssuname }}</div>
                  <div>工序：{{ cItem.task.wprname }}</div>
                </template>
                <template v-else>
                  <div>任务编号：{{ cItem.task.ttdno }}</div>
                  <div>工程编码：{{ cItem.task.ttdprono }}</div>
                  <div>任务内容：{{ cItem.task.ttmcontent }}</div>
                  <div>任务备注：{{ cItem.task.ttdremarks }}</div>
                  <div>结算类型：{{ cItem.task.ttdsmtype == "P" ? "个人结算" : "团队结算" }}</div>
                </template>
              </el-collapse-item>
            </el-collapse>
            <p>任务预计工时：{{ cItem.estimatedWorkHours }} 总录入工时：{{ cItem.totalEntryWorkHours }}</p>
            <p>{{ cItem.recordDateTime }}</p>
          </template>
          <span style="color: #409eff">当天总录入工时：{{ item.todayTotalHours }}</span>
          <el-collapse class="color-blue">
            <el-collapse-item :title="`当天总打卡工时${item.todayPunchCardTotalHours}`">
              <div>上午上班打卡时间：{{ item.punchCardRecord?.am_in }}</div>
              <div>上午下班打卡时间：{{ item.punchCardRecord?.am_out }}</div>
              <div>下午上班打卡时间：{{ item.punchCardRecord?.pm_in }}</div>
              <div>下午下班打卡时间：{{ item.punchCardRecord?.pm_out }}</div>
              <template v-if="item.punchCardRecord?.em_in">
                <div>晚上上班打卡时间：{{ item.punchCardRecord?.em_in }}</div>
                <div>晚上下班打卡时间：{{ item.punchCardRecord?.em_out }}</div>
              </template>
            </el-collapse-item>
          </el-collapse>
          <template v-if="item.auditStatus == 1">
            <img
              src="../../assets/pass.png"
              alt=""
              width="50px"
              class="auditCompleteIcon"
              :class="{ auditIconAnimation: item.showAnimation }"
            />
            <el-button @click="reaudit(item)" type="warning" size="mini" class="mt-1">反审</el-button>
          </template>
          <img
            v-else-if="item.auditStatus == 2"
            src="../../assets/exception.png"
            alt=""
            width="50px"
            class="auditCompleteIcon"
            :class="{ auditIconAnimation: item.showAnimation }"
          />
          <div v-else class="mt-1">
            <el-button @click="auditPass(item)" type="success" size="mini">审核通过</el-button>
            <el-button @click="openAuditException(item)" type="danger" size="mini">审核异常</el-button>
          </div>
        </el-card>
      </el-timeline-item>
    </el-timeline>
    <!-- 审核异常 -->
    <el-dialog :visible.sync="auditExceptionDlg" title="审核异常" @close="auditExceptionCancel" width="80%">
      <el-form ref="auditExceptionFormRef" :model="auditExceptionForm" :rules="auditExceptionFormRules">
        <el-form-item label="异常原因" prop="reason">
          <el-input v-model="auditExceptionForm.reason" type="textarea" placeholder="请输入异常原因"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="auditException()">提交</el-button>
      </div>
    </el-dialog>
    <!-- 修改工时 -->
    <el-dialog :visible.sync="updateHoursDlg" title="修改工时">
      <el-input v-model="taskHours.hours" placeholder="请输入工时"></el-input>
      <p style="margin: 5px 0">
        录入小队：<span style="color: #409eff">{{ taskHours.teamName }}</span> &nbsp;<i
          @click="teamClick"
          class="el-icon-edit"
        ></i>
      </p>
      <div slot="footer">
        <el-button @click="updateTaskHours()">提交</el-button>
      </div>
    </el-dialog>
    <!-- 小队 -->
    <el-dialog title="信息" :visible.sync="teamDlg" width="50%" :close-on-click-modal="false" destroy-on-close>
      <div class="mb-2">
        <el-select v-model="teamSearchKey" @change="searchTeam" style="width: 100px" class="mr-1">
          <el-option v-for="item of teamSearchKeys" :key="item.value" v-bind="item"></el-option>
        </el-select>
        <el-input
          v-model="teamSearchValue"
          placeholder="请输入搜索关键字"
          @input="searchTeam"
          style="width: auto"
        ></el-input>
      </div>
      <el-table
        :data="filteredTeamList"
        ref="teamTableRef"
        tooltip-effect="dark"
        style="width: auto"
        max-height="300"
        @current-change="teamCurrentChange"
        highlight-current-row
      >
        <!-- <el-table-column type="selection" width="50" :reserve-selection="true"></el-table-column> -->
        <el-table-column type="index" label="序号" width="55"></el-table-column>
        <el-table-column prop="groname" label="团队名称" width="110"></el-table-column>
        <el-table-column prop="supno" label="所属巴组"></el-table-column>
        <el-table-column prop="grohmname" label="队长姓名"></el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="teamDlg = false">取 消</el-button>
        <el-button type="primary" @click="updateTeam">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import service from "@/router/request";
import { xlsx, isMobile } from "@/utils/utils";

export default {
  name: "ReportForDay",
  data() {
    return {
      taskHoursList: [],
      date: this.$moment().format("yyyy-MM-DD"),
      auditExceptionDlg: false,
      auditExceptionForm: { reason: "" },
      auditExceptionFormRules: {
        reason: [{ required: true, message: "异常原因不能为空", trigger: "blur" }],
      },
      operationData: null,
      updateHoursDlg: false,
      taskHours: { hours: 0 },

      teamDlg: false,
      teamSearchKey: "groname",
      teamSearchValue: "",
      teamSearchKeys: [
        { label: "队名", value: "groname" },
        { label: "巴组编号", value: "supno" },
        { label: "队长姓名", value: "grohmname" },
      ],
      filteredTeamList: [],
      teamList: [],
      teamSelection: null,
    };
  },

  mounted() {
    isMobile() && document.getElementById("container").setAttribute("style", "min-height: 100vh;max-height: 100vh");
    this.getTaskHoursAndTaskListForDay();
    this.getTeams();
    document.getElementById("container").addEventListener("scroll", this.setScrollTop);
  },

  activated() {
    document.getElementById("container").addEventListener("scroll", this.setScrollTop);
    const scrollTop = localStorage.getItem("RDScrollTop");
    if (scrollTop) {
      document.getElementById("container").scrollTo(0, scrollTop);
    }
  },

  deactivated() {
    document.getElementById("container")?.removeEventListener("scroll", this.setScrollTop);
  },

  beforeDestroy() {
    document.getElementById("container")?.removeEventListener("scroll", this.setScrollTop);
  },

  methods: {
    isMobile,
    getTaskHoursAndTaskListForDay() {
      const loading = this.$loading({
        lock: true,
        text: "加载中.....",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      service
        .post("/TaskHours/getTaskHoursAndTaskListForDay", {
          loginId: this.$store.state.user.loginId,
          date: this.date,
          roleId: this.$store.state.user.roleId,
        })
        .then((res) => {
          if (res.status == "succeed") {
            this.taskHoursList = res.data.list;
            loading.close();
          }
        });
    },

    dateChange(val) {
      this.getTaskHoursAndTaskListForDay();
    },

    auditPass(item) {
      this.$confirm(
        `${
          item.todayTotalHours != item.todayPunchCardTotalHours ? "当天录入工时和打卡工时不符，是否" : ""
        }确认审核通过 ${item.timestamp} ${this.date} 的工时?`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: item.todayTotalHours != item.todayPunchCardTotalHours ? "warning" : "",
          center: true,
          customClass: "phone-message-box",
        }
      )
        .then(() => {
          let cb = (data) => {
            item.auditId = +data;
            item.auditStatus = 1;
            this.$set(item, "showAnimation", true);
          };
          this.taskHoursAudit(
            {
              loginId: item.list[0].loginId,
              date: this.date,
              status: 1,
              id: item.auditId,
            },
            cb
          );
        })
        .catch(() => {});
    },

    auditException() {
      this.$refs.auditExceptionFormRef.validate((valid) => {
        if (valid) {
          let cb = (data) => {
            this.auditExceptionDlg = false;
            this.operationData.auditId = +data;
            this.operationData.auditStatus = 2;
            this.$set(this.operationData, "showAnimation", true);
          };
          this.taskHoursAudit(
            {
              loginId: this.operationData.list[0].loginId,
              date: this.date,
              status: 2,
              reason: this.auditExceptionForm.reason,
              id: this.operationData.auditId,
            },
            cb
          );
        }
      });
    },
    openAuditException(item) {
      this.operationData = item;
      this.auditExceptionDlg = true;
    },
    auditExceptionCancel() {
      this.$refs.auditExceptionFormRef.resetFields();
    },

    taskHoursAudit(data, cb = () => {}) {
      service.post("/TaskHoursAudit/taskHoursAudit", data).then((res) => {
        if (res.status == "succeed") {
          cb(res.data);
          this.$message.success({
            message: "审核成功",
            customClass: "phone-message-tip",
          });
        } else {
          this.$message.error({
            message: res.msg,
            customClass: "phone-message-tip",
          });
        }
      });
    },

    updateTaskHours() {
      service
        .post("/TaskHours/updateTaskHours", {
          hours: this.taskHours.hours,
          id: this.taskHours.id,
          teamNo: this.taskHours.teamNo,
        })
        .then((res) => {
          if (res.status == "succeed") {
            this.getTaskHoursAndTaskListForDay();
            this.updateHoursDlg = false;
            this.$message.success({ message: "修改成功", customClass: "phone-message-tip" });
          } else {
            this.$message({ message: "修改失败", type: "error", customClass: "phone-message-tip" });
          }
        });
    },

    reaudit(item) {
      this.$confirm(`确认反审 ${item.timestamp} ${this.date} 的工时?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        center: true,
        customClass: "phone-message-box",
      })
        .then(() => {
          let cb = (data) => {
            // item.auditId = +data;
            item.auditStatus = 0;
          };
          this.taskHoursAudit(
            {
              loginId: item.punchCardRecord.loginid,
              date: this.date,
              status: 0,
              id: item.auditId,
            },
            cb
          );
        })
        .catch(() => {});
    },

    getTaskType(item) {
      return item.taskNo.includes("TAD") ? "计件任务" : item.task.ttm_no.includes("TTM") ? "计时任务" : "临时任务";
    },

    exportData() {
      if (!this.taskHoursList?.length) return this.$message.warning("暂无数据");
      const loading = this.$loading({
        lock: true,
        text: "导出中.....",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let data = [];
      this.taskHoursList.forEach((item) => {
        item.list.forEach((cItem) => {
          data.push({
            name: item.timestamp,
            groname: cItem.groname,

            taskNo: cItem.taskNo,
            taskType: this.getTaskType(cItem),
            taskName: cItem.taskName,
            recordDate: cItem.recordDate,
            hours: cItem.hours,
            remarks: cItem.task.ttdremarks || cItem.task.tasdremarks || "",
          });
        });
      });
      let map = {
        name: "姓名",
        groname: "所在巴组",
        taskNo: "任务编号",
        taskType: "任务类型",
        taskName: "任务名",
        recordDate: "录入日期",
        hours: "录入工时",
        remarks: "备注",
      };

      xlsx(data, map, "每日日报");
      loading.close();
    },

    deleteTask(item, cItem) {
      if (item.auditStatus)
        return this.$message.warning({ message: "日报已审，不可删除", customClass: "phone-message-tip" });
      this.$confirm("确定要删除该日报吗?", "提示", { type: "warning" })
        .then(() => {
          service.post("/TaskHours/deleteTaskHours", { taskHoursId: cItem.id, taskNo: cItem.taskNo }).then((res) => {
            if (res.status == "succeed") {
              this.getTaskHoursAndTaskListForDay();
              this.$message({
                message: "删除成功",
                customClass: isMobile() ? "phone-message-tip" : "",
                type: "success",
              });
            } else {
              this.$message({ message: res.err, customClass: isMobile() ? "phone-message-tip" : "", type: "error" });
            }
          });
        })
        .catch(() => {});
    },

    teamClick() {
      this.teamDlg = true;
    },

    teamCurrentChange(val) {
      this.teamSelection = val;
    },

    getTeams() {
      service
        .get("/DailyReport/getGroupList")
        .then((res) => {
          if (res.status == "succeed") {
            this.teamList = res.data;
            this.filteredTeamList = res.data;
          }
        })
        .catch((error) => {
          console.error(error);
        });
    },

    searchTeam() {
      this.filteredTeamList = this.teamList.filter((item) => item[this.teamSearchKey].includes(this.teamSearchValue));
    },

    updateTeam() {
      this.taskHours.teamNo = this.teamSelection.grono;
      this.taskHours.teamName = this.teamSelection.groname;
      this.teamDlg = false;
    },

    setScrollTop() {
      localStorage.setItem("RDScrollTop", document.getElementById("container").scrollTop);
    },
  },
};
</script>

<style scoped>
.container {
  padding-bottom: 40px;
  background-color: rgb(255, 255, 255);
  /* min-height: 100vh; */
  box-sizing: border-box;

  min-height: 100%;
  max-height: 100%;
  overflow-y: auto;
}

.title {
  margin: 0;
  text-align: center;
  padding: 0 20px 20px;
}

>>> .task-title .el-collapse-item__header {
  font-weight: bold;
  font-size: 15px;
}

.task-title .el-icon-edit {
  color: #409eff;
  margin-left: 5px;
}
.task-title .el-icon-delete {
  color: #f33131;
  margin-left: 5px;
}

>>> .color-blue * {
  color: #409eff !important;
}

.el-collapse {
  border: none;
}

>>> .el-collapse-item__header {
  height: auto;
  line-height: normal;
  padding: 10px 0;
}

.auditCompleteIcon {
  position: absolute;
  top: 100px;
  right: 20px;
}

.auditIconAnimation {
  animation: fadeInTopLeft 1s ease-in-out;
}

@keyframes fadeInTopLeft {
  0% {
    /* opacity: 0; */
    -webkit-transform: translate3d(0, 0, 300%);
    transform: scale(3);
  }

  100% {
    /* opacity: 1; */
    -webkit-transform: translateZ(0);
    transform: scale(1);
  }
}
</style>
