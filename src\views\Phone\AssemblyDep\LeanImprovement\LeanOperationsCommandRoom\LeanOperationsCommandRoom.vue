<template>
  <div class="bg-gray-100 min-h-screen pb-16">
    <!-- 顶部导航栏 -->
    <TopNavigationBar title="精益作战指挥室">
      <template #tool-bar>
        <i class="el-icon-search text-xl" @click="toggleSearchPanel"></i>
      </template>
    </TopNavigationBar>

    <!-- 搜索面板 -->
    <transition name="slide-down">
      <div v-show="showSearchPanel" class="bg-white p-4 shadow-md">
        <el-input
          v-model="searchParams.title"
          placeholder="搜索提案标题"
          clearable
          class="mb-3"
          prefix-icon="el-icon-search"
        ></el-input>

        <el-select v-model="searchParams.status" placeholder="选择状态" class="w-full mb-3">
          <el-option label="全部状态" :value="''"></el-option>
          <el-option label="待处理" :value="1"></el-option>
          <el-option label="处理中" :value="2"></el-option>
          <el-option label="已通过" :value="3"></el-option>
          <el-option label="已拒绝" :value="4"></el-option>
        </el-select>

        <div class="flex justify-between">
          <el-button plain @click="resetSearch" class="flex-1 mr-2">重置</el-button>
          <el-button type="primary" @click="fetchProposals" class="flex-1">搜索</el-button>
        </div>
      </div>
    </transition>

    <!-- 提案列表 -->
    <div class="p-3 space-y-3">
      <div
        v-for="proposal in proposalList"
        :key="proposal.id"
        class="bg-white rounded-lg shadow p-4"
        @click="viewProposalDetail(proposal)"
      >
        <div class="flex justify-between items-start mb-2">
          <h3 class="text-lg font-medium text-gray-900 line-clamp-1">{{ proposal.title }}</h3>
          <el-tag :type="getStatusType(proposal.status)" size="small">
            {{ getStatusText(proposal.status) }}
          </el-tag>
        </div>

        <div class="flex items-center text-xs text-gray-500 mb-2">
          <span class="flex items-center mr-3">
            <i class="el-icon-user mr-1"></i>
            {{ proposal.proposerName }}
          </span>
          <span class="flex items-center">
            <i class="el-icon-time mr-1"></i>
            {{ formatDate(proposal.createdAt) }}
          </span>
        </div>

        <p class="text-gray-700 text-sm mb-3 truncate" :title="proposal.currentSituation">
          {{ proposal.currentSituation }}
        </p>

        <div class="flex justify-between items-center">
          <div>
            <el-tag
              v-for="tag in proposal.tags.split(',').splice(0, 2)"
              :key="tag"
              class="mr-1"
              size="mini"
              type="info"
            >
              <i class="el-icon-collection-tag mr-1"></i>
              {{ IMPROVEMENT_TAGS.find((t) => t.value == tag)?.label || tag }}
            </el-tag>
          </div>

          <el-button
            size="mini"
            type="primary"
            plain
            @click.stop="processProposal(proposal.id)"
            v-if="proposal.status === 1"
          >
            处理
          </el-button>
        </div>
      </div>

      <!-- 加载更多 -->
      <div class="text-center py-4" v-if="hasMore && proposalList.length > 0">
        <el-button :loading="loading" @click="loadMore" plain class="w-full">
          {{ loading ? "加载中..." : "加载更多" }}
        </el-button>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && proposalList.length === 0" class="text-center py-10">
        <i class="el-icon-document text-4xl text-gray-400 mb-3"></i>
        <p class="text-gray-500">暂无提案数据</p>
        <el-button type="primary" plain @click="fetchProposals" class="mt-3"> 刷新 </el-button>
      </div>
    </div>

    <!-- 提案详情弹窗 -->
    <el-dialog
      :title="selectedProposal?.title"
      :visible.sync="showProposalDetail"
      width="90%"
      top="5vh"
      custom-class="rounded-lg dialog-proposal-detail"
    >
      <div v-if="selectedProposal">
        <div class="flex flex-wrap items-center text-sm text-gray-500 mb-4">
          <span class="flex items-center mr-3 mb-1">
            <i class="el-icon-user mr-1"></i>
            {{ selectedProposal.proposerName }}
          </span>
          <span class="flex items-center mr-3 mb-1">
            <i class="el-icon-time mr-1"></i>
            {{ formatDate(selectedProposal.createdAt) }}
          </span>
          <el-tag :type="getStatusType(selectedProposal.status)" size="small" class="mb-1">
            {{ getStatusText(selectedProposal.status) }}
          </el-tag>
        </div>

        <div class="mb-4 mr-1">
          <el-tag v-for="tag in selectedProposal.tags.split(',')" :key="tag" size="mini" type="info">
            <i class="el-icon-collection-tag mr-1"></i>
            {{ IMPROVEMENT_TAGS.find((t) => t.value == tag)?.label || tag }}
          </el-tag>
        </div>

        <div class="mb-4">
          <h4 class="text-sm font-medium text-gray-700 mb-1">当前状况</h4>
          <p class="text-gray-600 text-sm">{{ selectedProposal.currentSituation }}</p>
        </div>

        <div class="mb-4" v-if="selectedProposal.suggestion">
          <h4 class="text-sm font-medium text-gray-700 mb-1">改善建议</h4>
          <p class="text-gray-600 text-sm">{{ selectedProposal.suggestion }}</p>
        </div>

        <div class="mb-4" v-if="selectedProposal.expectedEffect">
          <h4 class="text-sm font-medium text-gray-700 mb-1">预期效果</h4>
          <p class="text-gray-600 text-sm">{{ selectedProposal.expectedEffect }}</p>
        </div>

        <!-- 附件列表 -->
        <div v-if="selectedProposal.attachmentList && selectedProposal.attachmentList.length" class="mb-4">
          <h4 class="text-sm font-medium text-gray-700 mb-1">附件</h4>
          <ul class="text-sm">
            <li
              v-for="(file, idx) in selectedProposal.attachmentList"
              :key="file.id || idx"
              class="flex items-center mb-1"
            >
              <i class="el-icon-paperclip mr-2 text-gray-400"></i>
              <el-link @click="previewFile(file)" type="primary">{{ file.name }}</el-link>
            </li>
          </ul>
        </div>

        <div v-if="selectedProposal.review" class="bg-gray-50 p-3 rounded mb-4">
          <h4 class="text-sm font-medium text-gray-700 mb-1">评审意见</h4>
          <p class="text-gray-600 text-sm">{{ selectedProposal.review.comment }}</p>
          <p class="text-gray-500 text-xs mt-1">
            {{ selectedProposal.review.reviewerName }} · {{ formatDate(selectedProposal.review.reviewTime) }}
          </p>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="closeProposalDetail">关闭</el-button>
        <el-button type="primary" @click="processProposal(selectedProposal.id)" v-if="selectedProposal?.status === 1">
          处理提案
        </el-button>
      </div>
    </el-dialog>

    <!-- 处理提案表单 -->
    <el-dialog title="处理提案" :visible.sync="showProcessForm" width="90%" top="5vh" custom-class="rounded-lg">
      <el-form :model="reviewData" label-width="80px">
        <el-form-item label="处理结果">
          <el-radio-group v-model="reviewData.result">
            <el-radio :label="2">通过</el-radio>
            <el-radio :label="3">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="处理意见">
          <el-input type="textarea" v-model="reviewData.comment" :rows="3" placeholder="请输入处理意见"></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelProcess">取消</el-button>
        <el-button type="primary" @click="submitReview">提交</el-button>
      </div>
    </el-dialog>

    <!--  -->
    <el-dialog :visible.sync="previewVisible" title="文件预览" width="80%">
      <FilePreview
        :file-url="previewFileUrl"
        :file-name="previewFileName"
        @close="previewVisible = false"
      ></FilePreview>
    </el-dialog>
  </div>
</template>

<script>
import service from "@/router/request";
import { IMPROVEMENT_TAGS } from "@/utils/constants";
import { MessageUtil } from "@/utils/utils";
import TopNavigationBar from "@/components/XTopNavigationBar.vue";
import FilePreview from "@/components/XFilePreview.vue";

export default {
  components: {
    TopNavigationBar,
    FilePreview,
  },
  data() {
    return {
      IMPROVEMENT_TAGS,
      loading: false,
      proposalList: [],
      searchParams: {
        page: 1,
        pageSize: 10,
        title: "",
        status: "",
      },
      statusMap: {
        1: { text: "待处理", type: "warning" },
        2: { text: "已通过", type: "success" },
        3: { text: "已拒绝", type: "danger" },
      },
      showSearchPanel: false,
      showProposalDetail: false,
      selectedProposal: null,
      showProcessForm: false,
      reviewData: {
        result: 3,
        comment: "",
      },
      hasMore: true,

      previewVisible: false,
      previewFileUrl: "",
      previewFileName: "",
    };
  },
  created() {
    this.fetchProposals();
  },
  methods: {
    getStatusType(status) {
      return this.statusMap[status]?.type || "info";
    },
    getStatusText(status) {
      return this.statusMap[status]?.text || "未知状态";
    },
    formatDate(dateString) {
      if (!dateString) return "";
      return dateString.split(" ")[0]; // 只显示日期部分
    },
    toggleSearchPanel() {
      this.showSearchPanel = !this.showSearchPanel;
    },
    async fetchProposals() {
      this.loading = true;
      try {
        const response = await service.get("/ImprovementProposal/GetProposalList", {
          params: {
            ...this.searchParams,
            loginId: this.$store.state.user.loginId,
            roleId: this.$store.state.user.roleId,
            page: 1, // 搜索时重置为第一页
            status: 1,
          },
        });
        this.proposalList = response;
        this.proposalList.forEach((proposal) => {
          proposal.attachmentList = proposal.attachments
            ? proposal.attachments.split(",").map((url, idx) => ({
                id: idx,
                url: service.getUri({ url }).replace("/api", ""),
                name: `附件${idx + 1}${url.substring(url.lastIndexOf("."))}`,
              }))
            : [];
        });
        this.hasMore = response.length >= this.searchParams.pageSize;
      } catch (error) {
        console.error(error);
        MessageUtil.error("获取提案列表失败");
      } finally {
        this.loading = false;
      }
    },
    async loadMore() {
      this.loading = true;
      try {
        const nextPage = this.searchParams.page + 1;
        const response = await service.get("/ImprovementProposal/GetProposalList", {
          params: {
            ...this.searchParams,
            page: nextPage,
          },
        });

        if (response.length > 0) {
          this.proposalList = [...this.proposalList, ...response];
          this.searchParams.page = nextPage;
          this.hasMore = response.length >= this.searchParams.pageSize;
        } else {
          this.hasMore = false;
        }
      } catch (error) {
        console.error("加载更多失败:", error);
      } finally {
        this.loading = false;
      }
    },
    viewProposalDetail(proposal) {
      this.selectedProposal = proposal;
      this.showProposalDetail = true;
    },
    closeProposalDetail() {
      this.showProposalDetail = false;
      this.selectedProposal = null;
    },
    processProposal(id) {
      this.selectedProposal = this.proposalList.find((p) => p.id === id);
      this.showProcessForm = true;
      this.showProposalDetail = false;
    },
    cancelProcess() {
      this.showProcessForm = false;
    },
    async submitReview() {
      if (!this.reviewData.comment) {
        MessageUtil.warning("请输入处理意见");
        return;
      }

      try {
        const response = await service.put("/ImprovementProposalReview/AddReview", {
          proposalId: this.selectedProposal.id,
          result: this.reviewData.result,
          reviewer: this.$store.state.user.loginId,
          comment: this.reviewData.comment,
        });
        MessageUtil.success("处理成功");
        this.showProcessForm = false;
        this.fetchProposals(); // 刷新列表
      } catch (error) {
        MessageUtil.error("处理失败");
        console.error(error);
      }
    },
    resetSearch() {
      this.searchParams = {
        page: 1,
        pageSize: 10,
        title: "",
        status: "",
      };
      this.fetchProposals();
      this.showSearchPanel = false;
    },

    previewFile(attachment) {
      this.previewFileUrl = attachment.url;
      this.previewFileName = attachment.name;
      this.previewVisible = true;
    },
  },
};
</script>

<style scoped>
/* 自定义过渡动画 */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}
.slide-down-enter,
.slide-down-leave-to {
  transform: translateY(-20px);
  opacity: 0;
}

/* 卡片点击效果 */
.bg-white {
  transition: transform 0.2s, box-shadow 0.2s;
}
.bg-white:active {
  transform: scale(0.98);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 文本截断 */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 对话框圆角 */
:deep(.el-dialog) {
  border-radius: 12px !important;
}
:deep(.dialog-proposal-detail .el-dialog__body) {
  max-height: 450px;
  overflow-y: auto;
}
</style>
