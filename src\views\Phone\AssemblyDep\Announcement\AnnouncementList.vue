<template>
  <div class="container">
    <TopNavigationBar title="通告查看"></TopNavigationBar>
    <div class="announcement-list">
      <!-- 搜索和筛选 -->
      <div class="search-section">
        <el-input
          v-model="searchText"
          placeholder="搜索通告内容"
          prefix-icon="el-icon-search"
          clearable
          @input="handleSearch"
          class="search-input"
        ></el-input>

        <div class="filter-section">
          <el-select v-model="filterType" placeholder="类型" size="small" @change="handleFilter">
            <el-option label="全部" value=""></el-option>
            <el-option label="表彰" value="1"></el-option>
            <el-option label="公示" value="2"></el-option>
          </el-select>

          <el-select v-model="filterPriority" placeholder="重要性" size="small" @change="handleFilter">
            <el-option label="全部" value=""></el-option>
            <el-option label="普通" value="1"></el-option>
            <el-option label="重要" value="2"></el-option>
            <el-option label="紧急" value="3"></el-option>
          </el-select>
        </div>
      </div>

      <!-- 通告列表 -->
      <div class="list-container">
        <div v-if="loading" class="loading-container">
          <el-skeleton :loading="loading" count="5" animated></el-skeleton>
        </div>

        <div v-else-if="filteredList.length === 0" class="empty-state">
          <div class="empty-icon">📢</div>
          <p>暂无通告数据</p>
        </div>

        <div v-else class="announcement-items">
          <div
            v-for="item in filteredList"
            :key="item.id"
            class="announcement-item"
            :class="getPriorityClass(item.priority)"
          >
            <div class="item-header">
              <div class="header-left">
                <span class="type-badge" :class="getTypeBadgeClass(item.type)">
                  {{ getTypeText(item.type) }}
                </span>
                <span class="priority-badge" :class="getPriorityClass(item.priority)">
                  {{ getPriorityIcon(item.priority) }} {{ getPriorityText(item.priority) }}
                </span>
              </div>
              <div class="header-right">
                <el-button type="text" size="mini" @click="editAnnouncement(item)">
                  <i class="el-icon-edit"></i> 修改
                </el-button>
                <el-button type="text" size="mini" @click="deleteAnnouncement(item)" class="delete-btn">
                  <i class="el-icon-delete"></i> 删除
                </el-button>
              </div>
            </div>

            <div class="item-content">
              <p class="content-text">{{ item.content }}</p>
            </div>

            <div class="item-footer">
              <div class="time-info">
                <span v-if="item.startTime">
                  <i class="el-icon-time"></i>
                  开始: {{ formatDateTime(item.startTime) }}
                </span>
                <span v-if="item.endTime">
                  <i class="el-icon-time"></i>
                  结束: {{ formatDateTime(item.endTime) }}
                </span>
              </div>
              <div class="created-info">创建时间: {{ formatDateTime(item.createTime) }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑对话框 -->
    <el-dialog title="编辑通告" :visible.sync="editDialogVisible" width="90%" :close-on-click-modal="false">
      <el-form :model="editForm" :rules="editRules" ref="editFormRef" label-position="top" size="small">
        <el-form-item label="通告类型" prop="type">
          <el-select v-model="editForm.type" placeholder="请选择通告类型" style="width: 100%">
            <el-option label="表彰" value="1"></el-option>
            <el-option label="公示" value="2"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="重要性" prop="priority">
          <el-select v-model="editForm.priority" placeholder="请选择重要性" style="width: 100%">
            <el-option v-for="item in priorityOptions" :key="item.value" :label="item.label" :value="item.value">
              <span :style="{ color: item.color, fontWeight: item.value >= 2 ? 'bold' : 'normal' }">
                {{ item.icon }} {{ item.label }}
              </span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="通告内容" prop="content">
          <el-input type="textarea" v-model="editForm.content" placeholder="请输入通告内容" rows="5"></el-input>
        </el-form-item>

        <el-form-item label="开始时间" prop="startTime">
          <el-date-picker
            v-model="editForm.startTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择开始时间"
            style="width: 100%"
          ></el-date-picker>
        </el-form-item>

        <el-form-item label="结束时间" prop="endTime">
          <el-date-picker
            v-model="editForm.endTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择结束时间"
            style="width: 100%"
          ></el-date-picker>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveEdit">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TopNavigationBar from "@/components/XTopNavigationBar.vue";
import service from "@/router/request";
import { MessageUtil } from "@/utils/utils";

export default {
  components: { TopNavigationBar },
  name: "AnnouncementList",
  data() {
    return {
      loading: false,
      searchText: "",
      filterType: "",
      filterPriority: "",
      announcementList: [],
      filteredList: [],

      // 编辑相关
      editDialogVisible: false,
      editForm: {
        id: null,
        type: "",
        priority: 1,
        content: "",
        startTime: "",
        endTime: "",
      },
      editRules: {
        type: [{ required: true, message: "请选择通告类型", trigger: "change" }],
        priority: [{ required: true, message: "请选择重要性", trigger: "change" }],
        content: [{ required: true, message: "请输入通告内容", trigger: "blur" }],
        startTime: [{ required: true, message: "请选择开始时间", trigger: "change" }],
        endTime: [{ required: true, message: "请选择结束时间", trigger: "change" }],
      },

      priorityOptions: [
        {
          label: "普通",
          value: 1,
          color: "#409EFF",
          icon: "📢",
        },
        {
          label: "重要",
          value: 2,
          color: "#E6A23C",
          icon: "⚠️",
        },
        {
          label: "紧急",
          value: 3,
          color: "#F56C6C",
          icon: "🚨",
        },
      ],
    };
  },

  mounted() {
    this.loadAnnouncementList();
  },

  methods: {
    // 加载通告列表
    async loadAnnouncementList() {
      this.loading = true;
      try {
        const response = await service.get("/Announcement/GetAnnouncement");
        if (response.status === "succeed") {
          this.announcementList = response.data || [];
          this.applyFilters();
        } else {
          MessageUtil.error("获取通告列表失败");
        }
      } catch (error) {
        console.error("获取通告列表失败:", error);
        MessageUtil.error("获取通告列表失败");
      } finally {
        this.loading = false;
      }
    },

    // 搜索处理
    handleSearch() {
      this.applyFilters();
    },

    // 筛选处理
    handleFilter() {
      this.applyFilters();
    },

    // 应用筛选条件
    applyFilters() {
      let filtered = [...this.announcementList];

      // 文本搜索
      if (this.searchText.trim()) {
        const searchLower = this.searchText.toLowerCase();
        filtered = filtered.filter((item) => item.content.toLowerCase().includes(searchLower));
      }

      // 类型筛选
      if (this.filterType) {
        filtered = filtered.filter((item) => item.type == this.filterType);
      }

      // 重要性筛选
      if (this.filterPriority) {
        filtered = filtered.filter((item) => item.priority == this.filterPriority);
      }

      // 按创建时间倒序排列
      filtered.sort((a, b) => new Date(b.createTime) - new Date(a.createTime));

      this.filteredList = filtered;
    },

    // 获取类型文本
    getTypeText(type) {
      return type == 1 ? "表彰" : "公示";
    },

    // 获取类型样式
    getTypeBadgeClass(type) {
      return type == 1 ? "type-commendation" : "type-notice";
    },

    // 获取重要性样式类
    getPriorityClass(priority) {
      const priorityClasses = {
        1: "priority-normal",
        2: "priority-important",
        3: "priority-urgent",
      };
      return priorityClasses[priority] || "";
    },

    // 获取重要性图标
    getPriorityIcon(priority) {
      const priorityIcons = {
        1: "📢",
        2: "⚠️",
        3: "🚨",
      };
      return priorityIcons[priority] || "";
    },

    // 获取重要性文本
    getPriorityText(priority) {
      const priorityTexts = {
        1: "普通",
        2: "重要",
        3: "紧急",
      };
      return priorityTexts[priority] || "普通";
    },

    // 格式化日期时间
    formatDateTime(dateString) {
      if (!dateString) return "";
      const date = new Date(dateString);
      return date.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
      });
    },

    // 编辑通告
    editAnnouncement(item) {
      this.editForm = {
        id: item.id,
        type: item.type,
        priority: item.priority,
        content: item.content,
        startTime: item.startTime,
        endTime: item.endTime,
      };
      this.editDialogVisible = true;
    },

    // 保存编辑
    saveEdit() {
      this.$refs.editFormRef.validate(async (valid) => {
        if (valid) {
          try {
            const response = await service.post("/Announcement/Update", this.editForm);
            if (response.status === "succeed") {
              MessageUtil.success("通告更新成功！");
              this.editDialogVisible = false;
              this.loadAnnouncementList();
            } else {
              MessageUtil.error("通告更新失败，请稍后重试！");
            }
          } catch (error) {
            console.error("更新通告失败:", error);
            MessageUtil.error("通告更新失败，请稍后重试！");
          }
        } else {
          MessageUtil.error("请完善表单信息！");
        }
      });
    },

    // 删除通告
    deleteAnnouncement(item) {
      this.$confirm(`确定要删除这条${this.getTypeText(item.type)}通告吗？`, "确认删除", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          try {
            const response = await service.post("/Announcement/Delete", { id: item.id });
            if (response.status === "succeed") {
              MessageUtil.success("通告删除成功！");
              this.loadAnnouncementList();
            } else {
              MessageUtil.error("通告删除失败，请稍后重试！");
            }
          } catch (error) {
            console.error("删除通告失败:", error);
            MessageUtil.error("通告删除失败，请稍后重试！");
          }
        })
        .catch(() => {
          // 用户取消删除
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.announcement-list {
  flex: 1;
  padding: 16px;
}

/* 搜索和筛选区域 */
.search-section {
  background: white;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .search-input {
    margin-bottom: 12px;
  }

  .filter-section {
    display: flex;
    gap: 12px;

    .el-select {
      flex: 1;
    }
  }
}

/* 列表容器 */
.list-container {
  .loading-container {
    background: white;
    padding: 16px;
    border-radius: 8px;
  }

  .empty-state {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 8px;

    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }

    p {
      color: #909399;
      font-size: 16px;
      margin: 0;
    }
  }
}

/* 通告项样式 */
.announcement-items {
  .announcement-item {
    background: white;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #e4e7ed;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &.priority-normal {
      border-left-color: #409eff;
    }

    &.priority-important {
      border-left-color: #e6a23c;
      background-color: rgba(230, 162, 60, 0.02);
    }

    &.priority-urgent {
      border-left-color: #f56c6c;
      background-color: rgba(245, 108, 108, 0.02);
    }
  }
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;

  .header-left {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .header-right {
    display: flex;
    gap: 4px;

    .delete-btn {
      color: #f56c6c;

      &:hover {
        color: #f78989;
      }
    }
  }
}

/* 标签样式 */
.type-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;

  &.type-commendation {
    background-color: #f0f9ff;
    color: #1e40af;
  }

  &.type-notice {
    background-color: #f0fdf4;
    color: #166534;
  }
}

.priority-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;

  &.priority-normal {
    background-color: rgba(64, 158, 255, 0.1);
    color: #409eff;
  }

  &.priority-important {
    background-color: rgba(230, 162, 60, 0.1);
    color: #e6a23c;
  }

  &.priority-urgent {
    background-color: rgba(245, 108, 108, 0.1);
    color: #f56c6c;
  }
}

.item-content {
  margin-bottom: 12px;

  .content-text {
    color: #303133;
    line-height: 1.6;
    margin: 0;
    word-break: break-word;
  }
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #909399;

  .time-info {
    display: flex;
    flex-direction: column;
    gap: 4px;

    span {
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }

  .created-info {
    text-align: right;
  }
}

/* 对话框样式优化 */
.dialog-footer {
  text-align: center;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .announcement-list {
    padding: 12px;
  }

  .search-section {
    padding: 12px;
  }

  .announcement-item {
    padding: 12px;
  }

  .item-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;

    .header-right {
      align-self: flex-end;
    }
  }

  .item-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;

    .created-info {
      text-align: left;
    }
  }
}
</style>
