<template>
  <div class="demo-container">
    <TopNavigationBar title="公告栏设置演示"></TopNavigationBar>
    
    <div class="demo-content">
      <div class="demo-header">
        <h2>公告栏设置功能演示</h2>
        <p>这个演示展示了总装部首页底部公告栏的设置功能</p>
      </div>
      
      <div class="feature-list">
        <div class="feature-item">
          <div class="feature-icon">⚙️</div>
          <div class="feature-info">
            <h3>设置按钮</h3>
            <p>有权限的用户可以看到公告区域右上角的设置按钮</p>
          </div>
        </div>
        
        <div class="feature-item">
          <div class="feature-icon">🎛️</div>
          <div class="feature-info">
            <h3>滚动速度调节</h3>
            <p>支持5-20秒的滚动速度调节，提供极快、快速、标准、慢速、极慢五个预设</p>
          </div>
        </div>
        
        <div class="feature-item">
          <div class="feature-icon">📏</div>
          <div class="feature-info">
            <h3>高度调节</h3>
            <p>支持200-400px的高度调节，提供紧凑、标准、宽松、超大四个预设</p>
          </div>
        </div>
        
        <div class="feature-item">
          <div class="feature-icon">👁️</div>
          <div class="feature-info">
            <h3>实时预览</h3>
            <p>调整设置时可以实时看到效果，无需保存即可预览</p>
          </div>
        </div>
        
        <div class="feature-item">
          <div class="feature-icon">💾</div>
          <div class="feature-info">
            <h3>设置保存</h3>
            <p>设置会保存到本地存储，下次访问时自动加载</p>
          </div>
        </div>
        
        <div class="feature-item">
          <div class="feature-icon">🔄</div>
          <div class="feature-info">
            <h3>重置功能</h3>
            <p>支持一键重置为默认设置</p>
          </div>
        </div>
      </div>
      
      <div class="demo-actions">
        <button class="demo-btn primary" @click="goToAssemblyDep">
          <i class="el-icon-right"></i>
          前往总装部首页体验
        </button>
        
        <button class="demo-btn secondary" @click="showInstructions = !showInstructions">
          <i class="el-icon-question"></i>
          使用说明
        </button>
      </div>
      
      <div v-if="showInstructions" class="instructions">
        <h3>使用说明</h3>
        <ol>
          <li>确保您有管理员权限（角色ID为8或1）</li>
          <li>进入总装部首页，滚动到底部查看公告区域</li>
          <li>点击右上角的设置按钮（齿轮图标）</li>
          <li>在弹出的设置面板中调整滚动速度和高度</li>
          <li>实时查看调整效果</li>
          <li>点击"保存设置"按钮保存您的配置</li>
          <li>如需恢复默认，点击"重置默认"按钮</li>
        </ol>
      </div>
      
      <div class="tech-details">
        <h3>技术特性</h3>
        <div class="tech-grid">
          <div class="tech-item">
            <strong>响应式设计</strong>
            <span>适配各种屏幕尺寸</span>
          </div>
          <div class="tech-item">
            <strong>权限控制</strong>
            <span>基于用户角色的访问控制</span>
          </div>
          <div class="tech-item">
            <strong>本地存储</strong>
            <span>设置持久化保存</span>
          </div>
          <div class="tech-item">
            <strong>实时预览</strong>
            <span>即时反馈用户操作</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TopNavigationBar from "@/components/XTopNavigationBar.vue";

export default {
  name: "AnnouncementDemo",
  components: {
    TopNavigationBar,
  },
  data() {
    return {
      showInstructions: false,
    };
  },
  methods: {
    goToAssemblyDep() {
      this.$router.push({ path: "/assemblyDep", query: { loginid: this.$route.query.loginid } });
    },
  },
};
</script>

<style scoped>
.demo-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: env(safe-area-inset-bottom, 20px);
}

.demo-content {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
  
  h2 {
    color: white;
    font-size: clamp(1.5rem, 5vw, 2rem);
    margin: 0 0 10px 0;
  }
  
  p {
    color: rgba(255, 255, 255, 0.9);
    font-size: clamp(14px, 3vw, 16px);
    margin: 0;
  }
}

.feature-list {
  background: white;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 20px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.feature-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  flex-shrink: 0;
}

.feature-info {
  flex: 1;
  
  h3 {
    margin: 0 0 6px 0;
    font-size: 16px;
    color: #333;
  }
  
  p {
    margin: 0;
    font-size: 14px;
    color: #666;
    line-height: 1.4;
  }
}

.demo-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.demo-btn {
  flex: 1;
  padding: 14px 20px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  
  &.primary {
    background: white;
    color: #667eea;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(255, 255, 255, 0.3);
    }
  }
  
  &.secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    
    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
}

.instructions {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  
  h3 {
    margin: 0 0 16px 0;
    color: #333;
  }
  
  ol {
    margin: 0;
    padding-left: 20px;
    
    li {
      margin-bottom: 8px;
      color: #555;
      line-height: 1.4;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.tech-details {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 20px;
  
  h3 {
    margin: 0 0 16px 0;
    color: #333;
  }
}

.tech-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 12px;
}

.tech-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  
  strong {
    color: #333;
    font-size: 14px;
  }
  
  span {
    color: #666;
    font-size: 12px;
  }
}

@media (max-width: 768px) {
  .demo-content {
    padding: 16px;
  }
  
  .feature-list {
    padding: 20px;
  }
  
  .demo-actions {
    flex-direction: column;
  }
  
  .tech-grid {
    grid-template-columns: 1fr;
  }
}
</style>
