<template>
  <div class="training-materials-container">
    <TopNavigationBar title="培训资料"></TopNavigationBar>

    <main class="flex-grow container mx-auto px-4 py-6">
      <SearchBar v-model="searchText" placeholder="搜索会议标题或主讲人" @search="search"></SearchBar>

      <!-- 列表加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :loading="loading" count="5" animated></el-skeleton>
      </div>

      <!-- 列表内容 -->
      <div v-else class="meetings-list">
        <el-card
          v-for="item in trainingMeetingList"
          :key="item.id"
          class="meeting-card"
          :body-style="{ padding: '15px' }"
        >
          <template #header>
            <div class="card-header">
              <span class="meeting-title">{{ item.title }}</span>
              <span class="meeting-time">{{ item.planTime }}</span>
            </div>
          </template>
          <div class="card-content">
            <div class="meeting-info">
              <span class="info-item"> <i class="el-icon-user"></i> {{ item.speakerName || "未知主持人" }} </span>
              <span class="info-item">
                <i class="el-icon-document"></i> {{ item.attachments?.length || 0 }} 个附件
              </span>
            </div>
            <el-button v-if="item.attachments?.length" type="text" size="small" @click="showAttachments(item)"
              >查看附件</el-button
            >
          </div>
        </el-card>

        <!-- 空状态 -->
        <div v-if="trainingMeetingList.length === 0" class="empty-state">
          <el-empty description="暂无培训会议记录"></el-empty>
        </div>
      </div>
    </main>

    <!-- 附件对话框 -->
    <el-dialog :visible.sync="dialogVisible" title="附件列表" width="80%" :close-on-click-modal="false">
      <div v-if="currentAttachments?.length > 0" class="attachments-list">
        <el-table :data="currentAttachments" stripe>
          <el-table-column prop="name" label="文件名" min-width="200">
            <template #default="scope"> <i class="el-icon-document"></i> {{ scope.row.name }} </template>
          </el-table-column>
          <!-- <el-table-column prop="size" label="大小" min-width="100">
              <template #default="scope">
                {{ formatFileSize(scope.row.size) }}
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="上传时间" min-width="160">
              <template #default="scope">
                {{ formatDateTime(scope.row.createTime) }}
              </template>
            </el-table-column> -->
          <el-table-column label="操作" min-width="100">
            <template #default="scope">
              <el-button type="text" size="small" @click="previewFile(scope.row)">
                <i class="el-icon-view"></i> 预览
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-else class="empty-attachments">
        <el-empty description="没有附件"></el-empty>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog :visible.sync="previewVisible" title="文件预览" width="80%">
      <FilePreview
        :file-url="previewFileUrl"
        :file-name="previewFileName"
        @close="previewVisible = false"
      ></FilePreview>
    </el-dialog>
  </div>
</template>

<script>
import TopNavigationBar from "@/components/XTopNavigationBar.vue";
import service from "@/router/request";
import FilePreview from "@/components/XFilePreview.vue"; // 引入文件预览组件
import SearchBar from "@/components/XSearchBar.vue";

export default {
  name: "TrainingMaterials",
  components: { TopNavigationBar, FilePreview, SearchBar },
  data() {
    return {
      trainingMeetingList: [],
      loading: false,
      dialogVisible: false,
      currentAttachments: [],
      page: 1,
      pageSize: 20,
      searchText: "",
      previewVisible: false,
      previewFileUrl: "",
      previewFileName: "",
    };
  },
  methods: {
    // 获取培训会议列表
    async getTrainingMeetingList() {
      const res = await service.post("/Training/getTrainingMeetingMaterials", {
        key: this.searchText,
        pageNum: this.page,
        pageSize: this.pageSize,
        loginId: this.$store.state.user.loginId || "",
        roleId: this.$store.state.user.roleId || "",
      });
      return res.status == "succeed"
        ? res.data.map((item) => ({ ...item, attachments: item.attachments ? item.attachments.split(",") : [] }))
        : [];
    },

    search() {
      if (!this.searchText) return;
      this.page = 1;
      this.getTrainingMeetingList().then((data) => {
        this.trainingMeetingList = data;
      });
    },

    // 显示附件对话框
    showAttachments(meeting) {
      this.currentAttachments =
        meeting.attachments.map((url, idx) => ({
          url: service.getUri({ url }).replace("/api", ""),
          name: `附件${idx + 1}${url.substring(url.lastIndexOf("."))}`,
        })) || [];
      this.dialogVisible = true;
    },

    // 刷新会议列表
    refreshMeetings() {
      this.searchText = "";
      this.page = 1;
      this.getTrainingMeetingList();
    },

    // 预览文件
    previewFile(attachment) {
      this.previewFileUrl = attachment.url;
      this.previewFileName = attachment.name;
      this.previewVisible = true;
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return "0 B";

      const k = 1024;
      const sizes = ["B", "KB", "MB", "GB", "TB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));

      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    },
  },
  mounted() {
    this.getTrainingMeetingList().then((data) => (this.trainingMeetingList = data));
  },
};
</script>

<style scoped>
.training-materials-container {
  /* padding: 20px; */
  background-color: #f5f7fa;
  min-height: 100vh;
}

.search-container {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.search-container .el-input {
  flex: 1;
  max-width: 400px;
}

.meetings-list {
  margin-bottom: 20px;
}

.meeting-card {
  margin-bottom: 15px;
  transition: box-shadow 0.3s;
}

.meeting-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.meeting-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.meeting-time {
  font-size: 14px;
  color: #909399;
}

.meeting-info {
  display: flex;
  gap: 20px;
  margin-bottom: 10px;
  color: #606266;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item i {
  margin-right: 5px;
  color: #909399;
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 15px 0;
}

.attachments-list {
  max-height: 400px;
  overflow-y: auto;
}

.empty-state,
.empty-attachments {
  padding: 50px 0;
}
</style>
