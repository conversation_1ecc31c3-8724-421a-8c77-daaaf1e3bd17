# 公告栏设置功能说明

## 功能概述

在总装部首页底部的公告区域添加了设置功能，允许有权限的用户调整表彰栏和公示栏的滚动速度和高度，提供更好的用户体验。

## 功能特性

### 1. 权限控制

- 只有角色 ID 为 8（管理员）或 1（超级管理员）的用户才能看到设置按钮
- 设置按钮位于公告区域右上角，显示为齿轮图标

### 2. 滚动速度调节

- **调节范围**: 10-50px/s（像素每秒）
- **预设选项**:
  - 极慢 (10px/s)
  - 慢速 (16px/s)
  - 标准 (20px/s) - 默认值
  - 快速 (30px/s)
  - 极快 (50px/s)
- **智能计算**: 根据内容高度和设置速度自动计算动画持续时间
- **实时预览**: 调整时立即生效，无需保存即可查看效果

### 3. 滚动区域高度调节

- **调节范围**: 80-200px
- **预设选项**:
  - 紧凑 (80px)
  - 标准 (120px) - 默认值
  - 宽松 (160px)
  - 超大 (200px)
- **精确控制**: 只调整滚动区域高度，不影响整体容器布局

### 4. 设置持久化

- 设置通过 API 接口保存到数据库
- 支持多用户独立配置（可扩展）
- 下次访问时自动从服务器加载设置
- 支持一键重置为默认设置
- 包含参数验证和错误处理

## 使用方法

### 步骤 1: 访问设置

1. 进入总装部首页 (`/assemblyDep`)
2. 确保您有管理员权限（角色 ID 为 8 或 1）
3. 滚动到页面底部查看公告区域
4. 点击右上角的设置按钮（⚙️ 图标）

### 步骤 2: 调整设置

1. 在弹出的设置面板中，可以看到两个调节区域：
   - **滚动速度**: 使用滑块或预设按钮调整
   - **栏位高度**: 使用滑块或预设按钮调整
2. 调整过程中可以实时看到效果
3. 当前设置值会显示在标签右侧

### 步骤 3: 保存设置

1. 调整满意后，点击"保存设置"按钮
2. 系统会显示"设置已保存"的提示
3. 设置面板自动关闭

### 步骤 4: 重置设置（可选）

- 如需恢复默认设置，点击"重置默认"按钮
- 系统会显示"已重置为默认设置"的提示

## 技术实现

### 核心技术

- **Vue.js 2**: 响应式数据绑定和组件化开发
- **CSS 动画**: 使用 `@keyframes` 和 `animation` 属性实现滚动效果
- **API 集成**: 通过 RESTful API 进行设置的保存和获取
- **权限控制**: 基于 Vuex store 中的用户角色信息
- **智能计算**: 动态计算滚动动画持续时间
- **参数验证**: 前后端双重验证确保数据有效性

### 关键代码结构

```javascript
// 设置数据结构
announcementSettings: {
  scrollSpeed: 20,    // 滚动速度（px/s）
  scrollHeight: 120,  // 滚动区域高度（px）
}

// 智能计算滚动持续时间
calculateScrollDuration(contentType) {
  const contentElement = this.$refs[contentType + 'Content'];
  const contentHeight = contentElement.scrollHeight;
  const containerHeight = this.announcementSettings.scrollHeight;
  const scrollDistance = contentHeight + containerHeight;
  const duration = scrollDistance / this.announcementSettings.scrollSpeed;
  return Math.max(duration, 3);
}

// 动态样式绑定
:style="{
  animationDuration: calculateScrollDuration('commendations') + 's',
  height: announcementSettings.scrollHeight + 'px',
  '--container-height': announcementSettings.scrollHeight + 'px'
}"

// 优化的CSS动画（避免长内容空白期）
@keyframes scroll-up {
  0% { transform: translateY(var(--container-height, 120px)); }  /* 从容器底部开始 */
  100% { transform: translateY(-100%); }  /* 滚动到内容完全消失 */
}
```

### API 接口设计

```javascript
// 获取设置
GET /api/AnnouncementSettings/getSettings
Response: {
  status: "succeed",
  data: {
    scrollSpeed: 20,
    scrollHeight: 120
  }
}

// 保存设置
POST /api/AnnouncementSettings/saveSettings
Request: {
  scrollSpeed: 25,
  scrollHeight: 150
}
Response: {
  status: "succeed",
  message: "设置保存成功"
}

// 重置设置
POST /api/AnnouncementSettings/resetSettings
Response: {
  status: "succeed",
  message: "设置已重置为默认值"
}
```

### 响应式设计

- 支持移动端和桌面端
- 使用 `clamp()` 函数实现响应式字体大小
- 媒体查询优化不同屏幕尺寸的显示效果

### 性能优化

- 防抖处理避免频繁的设置更新
- 使用 CSS3 硬件加速提升动画性能
- 智能计算减少不必要的 DOM 操作
- 优化滚动动画避免长内容空白期问题
- 合理的默认值确保良好的用户体验

## 文件结构

```
src/
├── views/Phone/AssemblyDep/
│   ├── Index.vue                 # 主页面（包含设置功能）
│   ├── AnnouncementDemo.vue      # 功能演示页面
│   └── PermissionTest.vue        # 权限测试页面
├── api/
│   └── announcementSettings.js   # API接口封装
└── docs/
    ├── 公告栏设置功能说明.md      # 本说明文档
    ├── AnnouncementSettingsController.cs  # 后端控制器示例
    └── announcement_settings_table.sql    # 数据库表结构
```

## 开发和测试

### 开发环境访问

- 演示页面: `/announcementDemo`
- 权限测试: `/permissionTest`
- 主页面: `/assemblyDep`

### 测试数据

系统内置了测试用的表彰和公示数据，确保在开发环境中能够看到滚动效果。

### 权限测试

可以通过权限测试页面模拟不同角色的用户，验证设置按钮的显示逻辑。

## 注意事项

1. **权限验证**: 设置功能仅对角色 ID 为 8 或 1 的用户可见
2. **数据验证**: 前后端双重验证，确保参数在有效范围内
3. **API 集成**: 设置通过 API 保存到数据库，支持多用户配置
4. **错误处理**: 包含完整的错误处理和用户反馈机制
5. **性能优化**: 使用防抖处理和智能计算减少 DOM 操作
6. **兼容性**: CSS 使用标准属性确保跨浏览器兼容
7. **用户体验**: 提供预设选项、实时预览和物理单位显示

## 未来扩展

可以考虑添加以下功能：

- 更多动画效果选项（淡入淡出、左右滚动等）
- 字体大小调节
- 颜色主题自定义
- 公告内容的在线编辑功能
- 设置的云端同步

## 维护说明

- 定期检查本地存储的数据格式兼容性
- 监控用户反馈，优化默认设置值
- 根据实际使用情况调整预设选项
- 保持代码的可读性和可维护性
