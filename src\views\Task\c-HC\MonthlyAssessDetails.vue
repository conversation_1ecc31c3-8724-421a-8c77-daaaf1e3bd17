<template>
  <div>
    <div class="mb-2 top-bar">
      <el-button @click="$router.back()" size="medium" icon="el-icon-back" circle></el-button>
    </div>
    <el-descriptions :column="6" border class="mb-2">
      <el-descriptions-item v-for="item of taskInfoConfig" :key="item.value" :label="item.label">{{
        item.value
      }}</el-descriptions-item>
    </el-descriptions>
    <el-table :data="taskProcessTableData" row-key="tableRowKey" border>
      <el-table-column type="expand" label="点击展开评估" width="70" label-class-name="red" class-name="expand">
        <div slot-scope="scope" :class="isSettle(scope.row) ? 'settle' : 'not-settle'" style="padding: 0 20px">
          <div class="mb-1" style="display: flex; align-items: center">
            <el-button @click="assess(scope.row)" type="primary" size="medium" plain>评估完成</el-button>
            <el-button @click="openAllocationDialog(scope.row)" type="primary" size="medium" plain>分配</el-button>
            <div class="color-block" style="background-color: rgb(198, 226, 255)"></div>
            未结算
            <div class="color-block" style="background-color: rgb(198, 255, 218)"></div>
            已结算
          </div>
          <el-table :data="scope.row.rates">
            <el-table-column v-for="item of taskEvaluatedListConfig" :key="item.prop" v-bind="item" align="center">
              <template v-slot="rateScope">
                <el-date-picker
                  v-if="item.prop == 'mmonth'"
                  v-model="rateScope.row.mmonth"
                  type="month"
                  value-format="yyyy-MM"
                  :style="`width: ${item['min-width'] - 10}px`"
                  @change="getRate(scope.row)"
                  :clearable="false"
                ></el-date-picker>
                <el-input
                  v-else-if="item.prop == 'tadcomp_rate'"
                  v-model="rateScope.row.tadcomp_rate"
                  @change="tadcompRateChange(scope.row, rateScope.row)"
                  type="number"
                  :min="0"
                  :max="rateScope.row.completeRateMax"
                ></el-input>
                <el-link v-else-if="item.prop == 'tadTotalCompRate'" @click="getAllRate(scope.row)" type="primary">
                  {{ scope.row.tadcomp_rate }}</el-link
                >
                <template v-else>{{ rateScope.row[item.prop] }}</template>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-table-column>
      <el-table-column
        v-for="item of taskProcessTableConfig"
        :key="item.prop"
        v-bind="item"
        align="center"
      ></el-table-column>
    </el-table>
    <!-- 评估历史 -->
    <el-dialog :visible.sync="tadHistoryDialog" title="评估历史">
      <el-table :data="tadList">
        <el-table-column label="结算月份" prop="mmonth"></el-table-column>
        <el-table-column label="完成度%" prop="tadcomp_rate"></el-table-column>
        <el-table-column label="小队" prop="tmename"></el-table-column>
      </el-table>
    </el-dialog>
    <el-dialog :visible.sync="allocationDialog" title="分配">
      <el-button @click="openTeamDialog()" class="mb-1" type="primary" size="medium" plain>添加小队</el-button>
      <el-table :data="rates">
        <el-table-column label="小队" prop="tmename"></el-table-column>
        <el-table-column label="分配比%" prop="tadcomp_rate">
          <el-input slot-scope="scope" v-model="scope.row.distributionRatio"></el-input>
        </el-table-column>
        <!-- <el-table-column label="操作">
          <template v-slot="scope">
            <el-button type="danger" @click="deleteAllocation(scope.row, scope.$index)">删除</el-button>
          </template>
        </el-table-column> -->
      </el-table>
      <span slot="footer">
        <el-button @click="allocationDialog = false">取 消</el-button>
        <el-button type="primary" @click="allocation">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 小队 -->
    <el-dialog :visible.sync="teamDialog" title="分配小队">
      <div class="search-bar">
        <div class="search-item">
          <span class="label">团队姓名：</span>
          <el-input v-model="teamName" placeholder="请输入团队姓名" clearable></el-input>
        </div>
        <el-button @click="teamSearch" type="primary" class="search-btn">搜索</el-button>
      </div>
      <p style="color: red">点击选中行，然后点击确定完成分配</p>
      <el-table
        :data="filtrationTeams"
        :max-height="dialogHeight()"
        highlight-current-row
        @current-change="selectedTeam = $event"
      >
        <el-table-column v-for="config of teamTableConfig" v-bind="config" :key="config.prop"></el-table-column>
      </el-table>
      <span slot="footer">
        <el-button @click="teamDialog = false">取 消</el-button>
        <el-button type="primary" @click="addTeam">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import service from "@/router/request";
export default {
  name: "monthlyassessdetails",
  data() {
    return {
      taskInfo: {},
      taskInfoConfig: [
        { label: "机型型号", value: "", prop: "mmomodel" },
        { label: "工程编码", value: "", prop: "prono" },
        { label: "机型名称", value: "", prop: "proname" },
        { label: "计划工时", value: "", prop: "pwhours" },
        { label: "计划工资", value: "", prop: "pwages" },
        { label: "任务状态", value: "", prop: "adstatusn" },
        { label: "任务编号", value: "", prop: "tasno" },
        { label: "任务创建者", value: "", prop: "tascreater" },
        { label: "派单交期", value: "", prop: "deadline" },
        { label: "装配工单", value: "", prop: "apno" },
        { label: "装配交期", value: "", prop: "memname" },
        { label: "创建时间", value: "", prop: "tascreatedtime" },
        { label: "任务描述", value: "", prop: "tascontents" },
        { label: "派单备注", value: "", prop: "tasremarks" },
        { label: "装配备注", value: "", prop: "apremarks" },
      ],
      taskProcessTableData: [],
      taskProcessTableConfig: [
        { label: "部位", prop: "mpaname" },
        { label: "组件", prop: "sasname" },
        { label: "子件", prop: "ssuname" },
        { label: "工序", prop: "wprname" },
        { label: "工时", prop: "manhours", width: "60" },
        { label: "工时价", prop: "mhprice", width: "60" },
        { label: "数量", prop: "tasqty", width: "60" },
        { label: "工价", prop: "taswage", width: "60" },
        { label: "装配分队", prop: "tmename", width: "160" },
        { label: "开始日期", prop: "tasdstartdate", width: "155" },
        { label: "结束日期", prop: "tasddeldate", width: "155" },
        { label: "任务备注", prop: "tasdremarks", "min-width": "120" },
      ],
      taskEvaluatedListConfig: [
        // { label: "装配分队", prop: "tmename", "min-width": "140" },
        { label: "结算月份", prop: "mmonth", "min-width": "160" },
        { label: "累计完成%", prop: "tadTotalCompRate" },
        { label: "当月完成%", prop: "tadcomp_rate" },
        { label: "剩余待做%", prop: "remainingPart" },
        { label: "状态", prop: "tadflag" },
      ],
      noEvaluatedSelectedData: {},
      tadList: [],
      tadHistoryDialog: false,
      teams: [],
      filtrationTeams: [],
      teamDialog: false,
      teamTableConfig: [
        { label: "团队名称", prop: "groname" },
        { label: "所属巴组编号", prop: "grono" },
        { label: "巴长姓名", prop: "grohmname" },
      ],
      TADRow: null,
      selectedTeam: null,
      teamName: "",
      oldMonthValue: "",

      allocationDialog: false,
      rates: [],
      monthlySettleData: [],
    };
  },

  mounted() {
    this.getAssteam();
    this.getAssteamdet();
    this.getMonthlySettle();
  },

  methods: {
    getAssteam() {
      this.taskInfo = JSON.parse(decodeURIComponent(this.$route.query.taskInfo));
      service.get("/JiJian/GetAssteam", { params: { tasno: this.taskInfo.tasno } }).then((res) => {
        if (res.status == "succeed") {
          this.taskInfo = Object.assign(this.taskInfo, res.data || {});
          this.taskInfoConfig.forEach((item) => (item.value = this.taskInfo[item.prop]));
        }
      });
    },

    getAssteamdet() {
      service
        .get("/JiJian/getAssteamdetAndRateTotal", {
          params: { tasno: this.taskInfo.tasno },
        })
        .then((res) => {
          if (res.status == "succeed") {
            this.taskProcessTableData = res.data;
            this.taskProcessTableData.forEach((item, index) => {
              item.rates.forEach((rateItem, idx) =>
                this.$watch(
                  `taskProcessTableData.${index}.rates.${idx}.mmonth`,
                  (cur, pre) => {
                    this.oldMonthValue = pre;
                  },
                  { deep: true }
                )
              );
              item.notCurRatesSum =
                item.tadcomp_rate - item.rates.reduce((val, rateItem) => (val += +rateItem.tadcomp_rate), 0);
              this.tadcompRateChange(item, item.rates[0]);
              item.tableRowKey = index + 1;
            });
          }
        });
    },

    assess(row) {
      if (!row.rates[0].mmonth) return this.$message.warning("结算月份未选");
      if (row.tasdstartdate.substring(0, 7) > row.rates[0].mmonth)
        return this.$message.warning("评估失败，评估月份小于任务开始月份！");
      if (!row.tme_no) return this.$message.warning("评估失败，小队丢失");
      if (row.notCurRatesSum + row.rates[0].tadcomp_rate > 100)
        return this.$message.warning("评估失败，总完成度大于100！");
      const loading = this.$loading({
        lock: true,
        text: "评估中.....",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      service.post("/MonthlyAssess/assess", { ...row.rates[0], tmeno: row.tme_no }).then((res) => {
        loading.close();
        if (res.status == "succeed") {
          this.getAssteamdet();
          this.$message.success("评估成功");
        } else {
          this.$message.error(res.err);
        }
      });
    },

    tadcompRateChange(row, rateRow) {
      rateRow.tadcomp_rate = +rateRow.tadcomp_rate;
      row.rates.forEach((item) => {
        this.$set(
          item,
          "completeRateMax",
          100 -
            row.notCurRatesSum -
            row.rates.reduce((val, item) => (val += +item.tadcomp_rate), 0) +
            +item.tadcomp_rate
        );
        this.$set(item, "remainingPart", item.completeRateMax - +item.tadcomp_rate);
      });
    },

    async getRate(row) {
      service
        .post("/MonthlyAssess/getRate", {
          tadno: row.tadno,
          mmonth: row.rates[0].mmonth,
          tmeno: row.tme_no,
        })
        .then((res) => {
          if (res.status == "succeed") {
            row.notCurRatesSum = row.tadcomp_rate - res.data.tadcomp_rate;
            row.rates = [res.data];
            this.tadcompRateChange(row, row.rates[0]);
          }
        });
    },

    getAllRate(row) {
      service.get("/MonthlyAssess/getRates", { params: { tadno: row.tadno } }).then((res) => {
        if (res.status == "succeed") {
          this.tadList = res.data;
          this.tadHistoryDialog = true;
        } else {
          this.$message.error(res.err);
        }
      });
    },

    completeRateMax(row, rateRow) {
      if (rateRow.mmonth == this.$moment().format("yyyy-MM")) {
        return 100 - +row.tadcomp_rate + row.rates.reduce((val, item) => (val += +item.tadcomp_rate), 0);
      }
    },

    getTeams() {
      if (!this.teams?.length)
        service.get("/Public/GetGroups").then((res) => {
          if (res.status == "succeed") {
            this.teams = res.data;
            this.filtrationTeams = res.data;
          }
        });
    },

    getRates() {
      service
        .get("/JiJian/getRates", {
          params: {
            tadno: this.TADRow.tadno,
            mmonth: this.TADRow.rates[0].mmonth,
          },
        })
        .then((res) => {
          if ((res.status = "succeed")) {
            this.rates = res.data;
            this.rates.forEach((item) =>
              this.$set(item, "distributionRatio", (item.tadcomp_rate * 100) / this.TADRow.rates[0].tadcomp_rate)
            );
          }
        });
    },

    openAllocationDialog(row) {
      if (row.rates[0].tadcomp_rate == 0) return this.$message.warning("请先评估再分配！");
      this.TADRow = row;
      this.getRates();
      this.allocationDialog = true;
    },

    openTeamDialog() {
      this.getTeams();
      this.teamDialog = true;
    },

    addTeam() {
      if (!this.selectedTeam) return this.$message.warning("请选中小队");
      this.rates.push({
        completeRateMax: 0,
        mender: "",
        mmonth: this.TADRow.rates[0].mmonth,
        modifytime: "",
        remainingPart: 0,
        tadcomp_rate: 0,
        tadno: this.TADRow.tadno,
        tmename: this.selectedTeam.groname,
        tmeno: this.selectedTeam.grono,
        distributionRatio: 0,
      });
      this.teamDialog = false;
    },

    allocation() {
      let distributionRatioTotal = 0;
      for (let idx = 0; idx < this.rates.length; idx++) {
        // if (this.rates[idx].distributionRatio == 0)
        //   return this.$message.warning("分配比不能为0！");
        distributionRatioTotal += +this.rates[idx].distributionRatio;
        if (isNaN(distributionRatioTotal)) return this.$message.warning("分配比不符合格式！需要是数字！");
      }
      if (distributionRatioTotal != 100) return this.$message.error("分配比之和必须为一百");
      this.rates.forEach((item) => {
        item.tadcomp_rate = (this.TADRow.rates[0].tadcomp_rate * +item.distributionRatio * 0.01).toFixed(2);
      });
      const loading = this.$loading({
        lock: true,
        text: "分配中.....",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      service
        .post(
          "/MonthlyAssess/AddOrUpdateRate", //
          this.rates
        ) // InsertRate
        .then((res) => {
          loading.close();
          if (res.status == "succeed") {
            this.getAssteamdet();
            this.$message.success("分配成功");
          } else {
            this.$message.error(res.err);
          }
        });
    },

    dialogHeight() {
      return window.innerHeight * 0.6; // 如果是dialog的话应该是0.7
    },

    teamSearch() {
      this.filtrationTeams = this.teams.filter((item) => item.groname.includes(this.teamName));
    },

    getMonthlySettle() {
      service.post("/MonthlySettle/getMonthlySettle").then((res) => {
        if (res.status == "succeed") {
          this.monthlySettleData = res.data;
        }
      });
    },

    isSettle(row) {
      return this.monthlySettleData.some((item) => item.mmonth == row.rates[0].mmonth);
    },
  },

  watch: {},
};
</script>

<style scoped>
.top-bar {
  display: flex;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 1;
}
>>> .not-settle .el-table th.el-table__cell {
  background-color: rgb(198, 226, 255) !important;
}

>>> .settle .el-table th.el-table__cell {
  background-color: rgb(198, 255, 218) !important;
}

.color-block {
  width: 10px;
  height: 10px;
  margin: 0 10px;
}

>>> .red {
  color: #409eff;
}

>>> .el-dialog__body {
  padding: 0 20px 30px;
}
</style>
