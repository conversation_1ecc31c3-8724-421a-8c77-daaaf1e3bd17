<template>
  <div class="body">
    <div class="login-wrapper">
      <el-form
        :rules="rules"
        ref="loginForm"
        :model="loginForm"
        :style="{ width: loginContainerWidth }"
        class="loginContainer"
      >
        <div class="logo-container">
          <img src="../assets/ok_logo.png" alt="欧克logo" class="login-logo" />
        </div>
        <h3 class="loginTitle">欢迎登录</h3>
        <el-form-item prop="username">
          <el-input type="text" v-model="loginForm.username" placeholder="请输入用户名"></el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            type="password"
            v-model="loginForm.password"
            @keyup.enter.native="submitLogin"
            placeholder="请输入密码"
            show-password
          ></el-input>
        </el-form-item>
        <el-checkbox v-model="checked" class="loginRemember">记住我</el-checkbox>
        <el-button type="primary" style="width: 100%" @click="submitLogin" class="login-button">登录</el-button>
        <div class="extra-options">
          <el-link type="primary" @click="handleForgotPassword">忘记密码？</el-link>
          <el-link type="primary" @click="handleRegister">立即注册</el-link>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
import service from "../router/request.js";
import { LocalStorage } from "@/utils/utils.js";

export default {
  name: "login",
  data() {
    return {
      loginForm: {
        username: "",
        password: "",
      },
      checked: true,
      rules: {
        loginid: [
          { required: true, message: "请输入用户名", trigger: "blur" },
          { min: 5, max: 14, message: "长度在 5 到 14 个字符", trigger: "blur" },
        ],
        password: [
          { required: true, message: "请输入密码", trigger: "blur" },
          { min: 6, message: "密码长度要大于6", trigger: "blur" },
        ],
      },
      loginContainerWidth: "200px",
    };
  },

  mounted() {
    setTimeout(() => {
      this.loginContainerWidth = "350px"; // 设置宽度为350px
    }, 100);
  },

  methods: {
    submitLogin() {
      this.$refs.loginForm.validate(async (valid) => {
        if (valid) {
          // service
          //   .post("/Login", this.loginForm)
          //   .then((response) => {
          //     // 检查登录是否成功
          //     if (response.status == "succeed") {
          //       this.$message.success("登录成功");
          //       this.$store.state.user.token = response.data.token;
          //       this.$store.state.user = response.data;
          //       this.$store.state.menudata = response.data.info.menutree || [];
          //       this.$store.state.menudata.unshift({
          //         id: 0,
          //         name: "首页",
          //         path: "/default",
          //         icon: "el-icon-s-home",
          //       });
          //       const stateToSave = { ...this.$store.state, indexViewInstance: null };
          //       LocalStorage.setItem("store", JSON.stringify(stateToSave));
          //       this.$router.push("/");
          //     } else {
          //       this.$message.error("用户名或密码错误");
          //     }
          //   })
          //   .catch(() => {});
          const res = await service.post("/auth/login", this.loginForm);

          if (res.status == "succeed") {
            this.$message.success("登录成功");
            this.$store.state.user = res.data;
            const stateToSave = { ...this.$store.state, indexViewInstance: null };
            LocalStorage.setItem("store", JSON.stringify(stateToSave));
            this.$router.push("/");
          } else {
            this.$message.error(res.message || "登录失败");
          }
        }
      });
    },

    handleForgotPassword() {
      this.$message.warning("请联系管理员修改密码");
    },

    handleRegister() {
      this.$message.warning("请联系管理员注册账号");
    },
  },
};
</script>

<style lang="scss" scoped>
/* db */
.login-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  height: 100%;
}

.loginContainer {
  border-radius: 20px;
  background-clip: padding-box;
  text-align: left;
  width: 350px;
  padding: 30px 40px 30px 40px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%); /* 渐变背景 */
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
  transition: all 0.7s ease; /* 整体过渡动画 */

  &:hover {
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.2); /* 鼠标悬停时加深阴影 */
  }
}

.logo-container {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.login-logo {
  width: 80px;
  height: auto;
  animation: float 2s infinite ease-in-out; /* 让 logo 有轻微浮动动画 */
}

@keyframes float {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0);
  }
}

.loginTitle {
  margin: 0px auto 30px auto;
  text-align: center;
  font-size: 26px;
  color: #333;
  letter-spacing: 1px; /* 增加字间距，让标题更舒展 */
  position: relative;

  &::after {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 2px;
    background: linear-gradient(
      to right,
      rgba(0, 123, 255, 0.3) 0%,
      rgba(0, 123, 255, 0.6) 20%,
      #007bff 40%,
      rgba(0, 123, 255, 0.6) 60%,
      rgba(0, 123, 255, 0.3) 80%,
      rgba(0, 123, 255, 0) 100%
    );
    background-size: 200% 100%; /* 设置背景大小为原来的 200% */
    // animation: gradientMove 5s linear infinite; /* 应用动画 */
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
  }

  @keyframes gradientMove {
    0% {
      background-position: 0 0;
    }
    100% {
      background-position: -100% 0; /* 移动渐变位置 */
    }
  }
}

.loginRemember {
  text-align: left;
  margin: 0px 0px 15px 0px;
}

.extra-options {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
  font-size: 14px;
}

.login-button {
  background-color: #007bff;
  border-color: #007bff;
  transition: background-color 0.3s ease; /* 按钮背景色过渡动画 */

  &:hover {
    background-color: #0056b3; /* 鼠标悬停时按钮变色 */
    border-color: #0056b3;
  }
}

:deep(.el-input__inner) {
  border: 1px solid #ccc; /* 浅灰色边框 */
  border-radius: 5px;
  height: 40px;
  padding: 0 12px;
  font-size: 14px;
  transition: border-color 0.3s ease; /* 过渡动画 */

  &:focus {
    border-color: #007bff; /* 聚焦时边框变色 */
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.2); /* 聚焦时添加轻微阴影 */
    outline: none;
  }
}

:deep(.el-input__placeholder) {
  color: #999; /* 占位符颜色 */
}

.body {
  width: 100%;
  height: 100%;
  min-width: 1000px;
  background-image: url("../assets/login.jpg");
  background-size: cover;
  background-position: center center;
  overflow: auto;
  background-repeat: no-repeat;
  position: fixed;
  line-height: 100%;
}
</style>
