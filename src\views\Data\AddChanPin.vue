<template>
  <div style="display: flex;justify-content: end;" class="mb-2">
    <el-dialog :visible.sync="show" title="新增机型">
      <el-form label-width="100px" style="padding-right: 50px" :model="form" :rules="rules" ref="formRef">
        <el-form-item prop="mmomodel" label="机型型号">
          <el-select v-model="form.mmomodel" filterable remote reserve-keyword placeholder="请输入机型型号"
            :remote-method="queryDepartments" :loading="loading" @change="getModelNames()">
            <el-option v-for="item in options" :key="item" :label="item" :value="item"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="mmoname" label="机型名称">
          <el-select v-model="form.mmoname" filterable remote reserve-keyword placeholder="请输入机型名称"
            :remote-method="getModelNames" :loading="namesLoading">
            <el-option v-for="item in mmonames" :key="item" :label="item" :value="item"></el-option>
          </el-select>
          <br><span style="color: red;">请先选择机型型号再进行选择机型名称</span>
        </el-form-item>
        <el-form-item prop="mmoremarks" label="机型备注">
          <el-input type="textarea" :row="3" v-model="form.mmoremarks" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="所属巴组" prop="grono">
          <el-select style="float: left" v-model="form.grono" placeholder="请选择巴组">
            <el-option v-for="item in option" :key="item.grono" :label="item.groname" :value="item.grono"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div>
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="save">确 定</el-button>
      </div>
    </el-dialog>
    <el-tooltip content="暂无权限" :disabled="havePermissionRole([2, 8])">
      <el-button type="primary" @click="show = true" round :disabled="!havePermissionRole([2, 8])">新增机型</el-button>
    </el-tooltip>
  </div>
</template>

<script>
import service from "@/router/request";
import { havePermissionRole } from "@/utils/permission";

export default {
  name: "AddChanPin",
  data() {
    return {
      loading: false,
      show: false,
      form: {},
      tabledata: [],
      rules: {
        // 表单验证规则
        mmoname: [
          { required: true, message: "请输入机型名称", trigger: "blur" }
        ],
        mmomodel: [
          { required: true, message: "请输入机型型号", trigger: "blur" }
        ],
        grono: [{ required: true, message: "请选择巴组", trigger: "blur" }]
      },
      option: [],
      options: [],
      mmonames: [],
      namesLoading: false,
    };
  },
  mounted() {
    this.getBuzu();
    this.queryDepartments();
  },
  methods: {
    havePermissionRole,
    //  handleSelect(item) {
    //     console.log(item);
    //     this.form.groheadman = item.loginid;
    //   },
    queryDepartments(query) {
      this.loading = true;
      service
        .post("/Public/Getmmodelnumber", { fuzzymodel: query })
        .then(res => {
          if (res.status == "succeed") {
            this.loading = false;
            this.options = res.data;
          }
        })
        .catch(error => {
          console.error(error);
        });
    },
    // querySearchAsync(key) {
    //   service
    //     .get("/Public/Getmmodelnumber",{fuzzymodel:key })
    //     .then((res) => {
    //       if (res.status == "succeed") {
    //         this.tabledata = res.data;
    //       }
    //     });
    // },

    save() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          // 表单验证通过，执行保存逻辑

          // 执行保存操作
          // 可以在这里调用接口，将表单数据传递给后端进行保存
          service.post("/Chanpin/Addmmodetails", this.form).then(res => {
            if (res.status == "succeed") {
              // 表示成功保存
              this.$message.success("添加成功");

              this.show = false; // 关闭对话框
              this.$router.go(0);
            } else {
              this.$message.error(res.err); // 弹出错误的信息
            }
          });
        }
      });
    },

    cancel() {
      this.show = false; // 关闭对话框
      // 清空表单数据
      this.form = {};
    },

    getBuzu() {
      service.get("/Bazu/bazulist").then(res => {
        this.option = res.data;
      });
    },

    getModelNames(val = '') {
      this.namesLoading = true;
      service.get('/Model/getModelName', { params: { mmomodel: this.form.mmomodel, mmoname: val } }).then(res => {
        if (res.status == 'succeed') {
          this.mmonames = res.data;
          this.namesLoading = false;
        }
      })
    }
  }
};
</script>

<style scoped></style>
