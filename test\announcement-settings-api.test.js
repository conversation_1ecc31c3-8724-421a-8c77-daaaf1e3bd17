/**
 * 公告栏设置功能API测试
 * 测试新的API集成和滚动速度计算功能
 */

// 模拟 API 响应
const mockAPI = {
  async getAnnouncementSettings() {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          status: "succeed",
          data: {
            scrollSpeed: 20,
            scrollHeight: 120,
          },
        });
      }, 100);
    });
  },

  async saveAnnouncementSettings(settings) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 模拟验证
        if (settings.scrollSpeed < 10 || settings.scrollSpeed > 50) {
          reject(new Error("滚动速度必须在10-50px/s之间"));
          return;
        }
        if (settings.scrollHeight < 80 || settings.scrollHeight > 200) {
          reject(new Error("滚动区域高度必须在80-200px之间"));
          return;
        }

        resolve({
          status: "succeed",
          message: "设置保存成功",
        });
      }, 100);
    });
  },

  async resetAnnouncementSettings() {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          status: "succeed",
          message: "设置已重置为默认值",
        });
      }, 100);
    });
  },
};

// 验证函数
function validateSettings(settings) {
  const errors = [];

  if (!settings.scrollSpeed || settings.scrollSpeed < 10 || settings.scrollSpeed > 50) {
    errors.push("滚动速度必须在10-50px/s之间");
  }

  if (!settings.scrollHeight || settings.scrollHeight < 80 || settings.scrollHeight > 200) {
    errors.push("滚动区域高度必须在80-200px之间");
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}

// 默认设置
function getDefaultSettings() {
  return {
    scrollSpeed: 20,
    scrollHeight: 120,
  };
}

// 模拟 Vue 组件实例
class AnnouncementComponent {
  constructor() {
    this.announcementSettings = getDefaultSettings();
    this.commendations = [
      "张三同志在项目开发中表现突出，获得团队表彰",
      "李四同志积极参与技术分享，推动团队进步",
      "王五同志在客户服务方面表现优异，获得客户好评",
      "赵六同志在代码审查中发现重要问题，避免了生产事故",
      "钱七同志在系统优化方面贡献突出，提升了整体性能",
    ];
    this.notices = [
      "关于系统维护的通知：本周六晚上10点-12点进行系统维护",
      "新版本发布公告：v2.1.0版本已发布，包含多项功能优化",
      "培训通知：下周三下午2点进行Vue.js技术培训",
      "安全提醒：请及时更新密码，确保账户安全",
      "会议通知：下周一上午9点召开项目进度会议",
    ];
  }

  // 计算滚动动画持续时间
  calculateScrollDuration(contentType) {
    // 模拟内容高度计算
    const itemHeight = 40; // 每个条目约40px
    const contentItems = this[contentType];
    const contentHeight = contentItems.length * itemHeight;
    const containerHeight = this.announcementSettings.scrollHeight;

    // 新的动画逻辑：从容器高度位置滚动到-100%（内容高度）
    // 这样避免了长内容导致的空白期问题
    const scrollDistance = containerHeight + contentHeight;

    // 根据设置的速度（px/s）计算持续时间
    const duration = scrollDistance / this.announcementSettings.scrollSpeed;

    return Math.max(duration, 3); // 最小3秒
  }

  // 加载设置
  async loadSettings() {
    try {
      const response = await mockAPI.getAnnouncementSettings();
      if (response.status === "succeed" && response.data) {
        const validation = validateSettings(response.data);
        if (validation.valid) {
          this.announcementSettings = { ...this.announcementSettings, ...response.data };
          return { success: true, data: this.announcementSettings };
        } else {
          console.warn("获取的设置无效，使用默认设置:", validation.errors);
          this.announcementSettings = getDefaultSettings();
          return { success: false, error: "设置无效", useDefault: true };
        }
      } else {
        this.announcementSettings = getDefaultSettings();
        return { success: false, error: "API调用失败", useDefault: true };
      }
    } catch (error) {
      console.error("加载设置失败:", error);
      this.announcementSettings = getDefaultSettings();
      return { success: false, error: error.message, useDefault: true };
    }
  }

  // 保存设置
  async saveSettings() {
    try {
      const validation = validateSettings(this.announcementSettings);
      if (!validation.valid) {
        return { success: false, error: `设置无效: ${validation.errors.join(", ")}` };
      }

      const response = await mockAPI.saveAnnouncementSettings(this.announcementSettings);
      if (response.status === "succeed") {
        return { success: true, message: response.message };
      } else {
        throw new Error(response.message || "保存失败");
      }
    } catch (error) {
      console.error("保存设置失败:", error);
      return { success: false, error: error.message };
    }
  }

  // 重置设置
  resetSettings() {
    this.announcementSettings = getDefaultSettings();
    return { success: true, message: "已重置为默认设置" };
  }
}

// 测试函数
async function runTests() {
  console.log("🚀 开始公告栏设置API测试...\n");

  const component = new AnnouncementComponent();

  // 测试1: 加载设置
  console.log("📋 测试1: 加载设置");
  const loadResult = await component.loadSettings();
  console.log("结果:", loadResult);
  console.log("当前设置:", component.announcementSettings);
  console.log("");

  // 测试2: 滚动速度计算
  console.log("📋 测试2: 滚动速度计算");
  const commendationsDuration = component.calculateScrollDuration("commendations");
  const noticesDuration = component.calculateScrollDuration("notices");
  console.log(`表彰栏滚动时间: ${commendationsDuration.toFixed(2)}秒`);
  console.log(`公示栏滚动时间: ${noticesDuration.toFixed(2)}秒`);
  console.log("");

  // 测试3: 保存有效设置
  console.log("📋 测试3: 保存有效设置");
  component.announcementSettings = { scrollSpeed: 25, scrollHeight: 150 };
  const saveResult1 = await component.saveSettings();
  console.log("结果:", saveResult1);
  console.log("");

  // 测试4: 保存无效设置
  console.log("📋 测试4: 保存无效设置");
  component.announcementSettings = { scrollSpeed: 5, scrollHeight: 300 }; // 超出范围
  const saveResult2 = await component.saveSettings();
  console.log("结果:", saveResult2);
  console.log("");

  // 测试5: 重置设置
  console.log("📋 测试5: 重置设置");
  const resetResult = component.resetSettings();
  console.log("结果:", resetResult);
  console.log("重置后设置:", component.announcementSettings);
  console.log("");

  // 测试6: 参数验证
  console.log("📋 测试6: 参数验证");
  const validSettings = { scrollSpeed: 20, scrollHeight: 120 };
  const invalidSettings = { scrollSpeed: 100, scrollHeight: 50 };

  console.log("有效设置验证:", validateSettings(validSettings));
  console.log("无效设置验证:", validateSettings(invalidSettings));
  console.log("");

  console.log("✅ 所有测试完成!");
}

// 运行测试
if (typeof window === "undefined") {
  // Node.js 环境
  runTests();
} else {
  // 浏览器环境
  window.runAnnouncementSettingsTests = runTests;
  console.log("在浏览器控制台中运行: runAnnouncementSettingsTests()");
}
