<template>
  <div class="department-transfer-container">
    <TopNavigationBar title="部门调动"></TopNavigationBar>

    <el-main class="department-transfer-main">
      <!-- 搜索栏 -->
      <div class="search-section">
        <div class="search-wrapper">
          <i class="el-icon-search search-icon"></i>
          <el-input
            v-model="searchKeyword"
            placeholder="搜索员工姓名或工号"
            class="search-input"
            clearable
            @input="handleSearch"
          ></el-input>
        </div>
      </div>

      <!-- 部门列表 -->
      <div class="departments-section">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-state">
          <div class="loading-spinner"></div>
          <p>加载中...</p>
        </div>

        <!-- 部门树形数据 -->
        <div v-else-if="departmentTree.length" class="departments-tree">
          <div v-for="rootDepartment in departmentTree" :key="rootDepartment.id" class="department-tree-node">
            <!-- 根部门 -->
            <div class="department-card root-department">
              <div class="department-header" @click="toggleDepartment(rootDepartment.id)">
                <div class="department-info">
                  <h3 class="department-name">
                    <i class="el-icon-office-building department-icon"></i>
                    {{ rootDepartment.name }}
                  </h3>
                  <span class="member-count">{{ getTotalMemberCount(rootDepartment) }}人</span>
                </div>
                <i
                  class="el-icon-arrow-down toggle-icon"
                  :class="{ expanded: expandedDepartments.includes(rootDepartment.id) }"
                ></i>
              </div>

              <!-- 根部门内容 -->
              <div v-show="expandedDepartments.includes(rootDepartment.id)" class="department-content">
                <!-- 直属成员 -->
                <div v-if="getFilteredMembers(rootDepartment.members).length" class="members-section">
                  <div class="members-header">
                    <i class="el-icon-user"></i>
                    <span>直属成员</span>
                  </div>
                  <div class="members-list">
                    <div
                      v-for="member in getFilteredMembers(rootDepartment.members)"
                      :key="member.loginId"
                      class="member-item"
                      @click="selectMember(member, rootDepartment)"
                    >
                      <div class="member-info">
                        <div class="member-name">{{ member.name }}</div>
                        <div class="member-id">工号: {{ member.loginId }}</div>
                      </div>
                      <div class="member-actions">
                        <el-button type="primary" size="mini" @click.stop="openTransferDialog(member, rootDepartment)">
                          调动
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 子部门 -->
                <div v-if="rootDepartment.children && rootDepartment.children.length" class="sub-departments">
                  <div
                    v-for="subDepartment in rootDepartment.children"
                    :key="subDepartment.id"
                    class="department-card sub-department"
                  >
                    <div class="department-header" @click="toggleDepartment(subDepartment.id)">
                      <div class="department-info">
                        <h4 class="department-name">
                          <i class="el-icon-s-grid sub-department-icon"></i>
                          {{ subDepartment.name }}
                        </h4>
                        <span class="member-count">{{ subDepartment.members.length }}人</span>
                      </div>
                      <i
                        class="el-icon-arrow-down toggle-icon"
                        :class="{ expanded: expandedDepartments.includes(subDepartment.id) }"
                      ></i>
                    </div>

                    <!-- 子部门成员列表 -->
                    <div v-show="expandedDepartments.includes(subDepartment.id)" class="members-list sub-members">
                      <div
                        v-for="member in getFilteredMembers(subDepartment.members)"
                        :key="member.loginId"
                        class="member-item"
                        @click="selectMember(member, subDepartment)"
                      >
                        <div class="member-info">
                          <div class="member-name">{{ member.name }}</div>
                          <div class="member-id">工号: {{ member.loginId }}</div>
                        </div>
                        <div class="member-actions">
                          <el-button type="primary" size="mini" @click.stop="openTransferDialog(member, subDepartment)">
                            调动
                          </el-button>
                        </div>
                      </div>

                      <!-- 无成员提示 -->
                      <div v-if="!getFilteredMembers(subDepartment.members).length" class="no-members">
                        <i class="el-icon-user"></i>
                        <span>暂无符合条件的成员</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 无数据状态 -->
        <div v-else class="empty-state">
          <div class="empty-icon">🏢</div>
          <h3>暂无部门数据</h3>
          <p>请联系管理员配置部门信息</p>
        </div>
      </div>
    </el-main>

    <!-- 调动对话框 -->
    <el-dialog
      :visible.sync="transferDialogVisible"
      title="部门调动"
      width="90%"
      :close-on-click-modal="false"
      class="transfer-dialog"
    >
      <div v-if="selectedMember" class="transfer-content">
        <!-- 员工信息 -->
        <div class="employee-info">
          <h4>调动员工</h4>
          <div class="info-card">
            <div class="info-item">
              <span class="label">姓名:</span>
              <span class="value">{{ selectedMember.name }}</span>
            </div>
            <div class="info-item">
              <span class="label">工号:</span>
              <span class="value">{{ selectedMember.loginId }}</span>
            </div>
            <div class="info-item">
              <span class="label">当前部门:</span>
              <span class="value">{{ currentDepartment?.name }}</span>
            </div>
          </div>
        </div>

        <!-- 目标部门选择 -->
        <div class="target-department">
          <h4>选择目标部门</h4>
          <el-select v-model="targetDepartmentId" placeholder="请选择目标部门" class="department-select" filterable>
            <template slot="prefix">
              <i class="el-icon-search"></i>
            </template>
            <el-option
              v-for="dept in availableDepartments"
              :key="dept.id"
              :label="dept.name"
              :value="dept.id"
            ></el-option>
          </el-select>
        </div>

        <!-- 调动原因 -->
        <div class="transfer-reason">
          <h4>调动原因 <span class="optional">(可选)</span></h4>
          <el-input
            v-model="transferReason"
            type="textarea"
            placeholder="请输入调动原因..."
            :rows="3"
            maxlength="200"
            show-word-limit
          ></el-input>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="transferDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmTransfer" :loading="transferring" :disabled="!targetDepartmentId">
          {{ transferring ? "调动中..." : "确认调动" }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TopNavigationBar from "@/components/XTopNavigationBar.vue";
import service from "@/router/request";
import { MessageUtil } from "@/utils/utils";

export default {
  name: "DepartmentTransfer",
  components: {
    TopNavigationBar,
  },
  data() {
    return {
      loading: false,
      transferring: false,
      searchKeyword: "",
      departments: [],
      expandedDepartments: [],

      // 调动对话框相关
      transferDialogVisible: false,
      selectedMember: null,
      currentDepartment: null,
      targetDepartmentId: null,
      transferReason: "",
    };
  },
  computed: {
    // 构建部门树形结构
    departmentTree() {
      if (!this.departments.length) return [];

      // 创建部门映射
      const departmentMap = {};
      const rootDepartments = [];

      // 先创建所有部门的映射，并添加 children 属性
      this.departments.forEach((dept) => {
        departmentMap[dept.id] = {
          ...dept,
          children: [],
        };
      });

      // 构建树形结构
      this.departments.forEach((dept) => {
        if (dept.supDepId && departmentMap[dept.supDepId]) {
          // 有上级部门，添加到上级部门的 children 中
          departmentMap[dept.supDepId].children.push(departmentMap[dept.id]);
        } else {
          // 没有上级部门或上级部门不在当前列表中，作为根部门
          rootDepartments.push(departmentMap[dept.id]);
        }
      });

      // 如果有搜索关键词，过滤树形结构
      if (this.searchKeyword.trim()) {
        return this.filterDepartmentTree(rootDepartments);
      }

      return rootDepartments;
    },

    // 可选择的目标部门（排除当前部门）- 扁平化所有部门
    availableDepartments() {
      return this.departments.filter((dept) => dept.id !== this.currentDepartment?.id);
    },
  },
  mounted() {
    this.loadDepartments();
  },
  methods: {
    // 加载部门数据
    async loadDepartments() {
      this.loading = true;
      try {
        const response = await service.get("/Department/GetDepartmentsAndEmployees");
        if (response && Array.isArray(response)) {
          this.departments = response;
          // 默认展开第一个根部门
          this.$nextTick(() => {
            if (this.departmentTree.length > 0) {
              this.expandedDepartments = [this.departmentTree[0].id];
            }
          });
        }
      } catch (error) {
        console.error("加载部门数据失败:", error);
        MessageUtil.error("加载部门数据失败，请稍后重试");
      } finally {
        this.loading = false;
      }
    },

    // 切换部门展开状态
    toggleDepartment(departmentId) {
      const index = this.expandedDepartments.indexOf(departmentId);
      if (index > -1) {
        this.expandedDepartments.splice(index, 1);
      } else {
        this.expandedDepartments.push(departmentId);
      }
    },

    // 搜索处理
    handleSearch() {
      // 如果有搜索关键词，展开所有匹配的部门
      if (this.searchKeyword.trim()) {
        this.expandedDepartments = this.getAllDepartmentIds(this.departmentTree);
      }
    },

    // 过滤部门树（用于搜索）
    filterDepartmentTree(departments) {
      const keyword = this.searchKeyword.toLowerCase();
      const filteredDepartments = [];

      departments.forEach((dept) => {
        const deptCopy = { ...dept, children: [] };
        let shouldInclude = false;

        // 检查部门名称是否匹配
        if (dept.name.toLowerCase().includes(keyword)) {
          shouldInclude = true;
          deptCopy.children = dept.children; // 包含所有子部门
        }

        // 检查成员是否匹配
        if (
          dept.members.some(
            (member) => member.name.toLowerCase().includes(keyword) || member.loginId.toLowerCase().includes(keyword)
          )
        ) {
          shouldInclude = true;
        }

        // 递归检查子部门
        if (dept.children && dept.children.length) {
          const filteredChildren = this.filterDepartmentTree(dept.children);
          if (filteredChildren.length) {
            shouldInclude = true;
            deptCopy.children = filteredChildren;
          }
        }

        if (shouldInclude) {
          filteredDepartments.push(deptCopy);
        }
      });

      return filteredDepartments;
    },

    // 获取所有部门ID（用于展开）
    getAllDepartmentIds(departments) {
      let ids = [];
      departments.forEach((dept) => {
        ids.push(dept.id);
        if (dept.children && dept.children.length) {
          ids = ids.concat(this.getAllDepartmentIds(dept.children));
        }
      });
      return ids;
    },

    // 计算部门总人数（包括子部门）
    getTotalMemberCount(department) {
      let count = department.members.length;
      if (department.children && department.children.length) {
        department.children.forEach((child) => {
          count += this.getTotalMemberCount(child);
        });
      }
      return count;
    },

    // 获取过滤后的成员列表
    getFilteredMembers(members) {
      if (!this.searchKeyword.trim()) {
        return members.filter((member) => member.loginId && member.name);
      }

      const keyword = this.searchKeyword.toLowerCase();
      return members.filter(
        (member) =>
          member.loginId &&
          member.name &&
          (member.name.toLowerCase().includes(keyword) || member.loginId.toLowerCase().includes(keyword))
      );
    },

    // 选择成员
    selectMember(member, department) {
      // 可以在这里添加选择成员的逻辑
      console.log("选择成员:", member, "部门:", department);
    },

    // 打开调动对话框
    openTransferDialog(member, department) {
      this.selectedMember = member;
      this.currentDepartment = department;
      this.targetDepartmentId = null;
      this.transferReason = "";
      this.transferDialogVisible = true;
    },

    // 确认调动
    async confirmTransfer() {
      if (!this.targetDepartmentId) {
        MessageUtil.warning("请选择目标部门");
        return;
      }

      // 确认对话框
      try {
        await this.$confirm(
          `确定要将 ${this.selectedMember.name}（工号：${
            this.selectedMember.loginId
          }）调动到 ${this.getTargetDepartmentName()} 吗？`,
          "确认调动",
          {
            confirmButtonText: "确定调动",
            cancelButtonText: "取消",
            type: "warning",
            customClass: "phone-message-box",
          }
        );
      } catch {
        return; // 用户取消
      }

      this.transferring = true;
      try {
        // 注意：这里使用 LoginId 作为 resourceId，如果API需要其他ID，请相应调整
        await service.put(
          `/Department/TransferEmployee/${this.selectedMember.loginId}/transfer?newDepartmentId=${this.targetDepartmentId}`
        );

        MessageUtil.success("调动成功");
        this.transferDialogVisible = false;

        // 重新加载数据
        await this.loadDepartments();
      } catch (error) {
        console.error("调动失败:", error);
        const errorMsg = error.response?.data?.message || error.message || "调动失败，请稍后重试";
        MessageUtil.error(errorMsg);
      } finally {
        this.transferring = false;
      }
    },

    // 获取目标部门名称
    getTargetDepartmentName() {
      const targetDept = this.departments.find((dept) => dept.id === this.targetDepartmentId);
      return targetDept ? targetDept.name : "未知部门";
    },
  },
};
</script>

<style scoped>
.department-transfer-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.department-transfer-main {
  margin: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: env(safe-area-inset-bottom, 20px);
}

/* 搜索区域 */
.search-section {
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.search-wrapper {
  position: relative;
  max-width: 400px;
  margin: 0 auto;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  font-size: 16px;
  z-index: 1;
}

.search-input {
  width: 100%;
}

.search-input >>> .el-input__inner {
  padding-left: 40px;
  border-radius: 25px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  font-size: 16px;
  height: 50px;
  line-height: 50px;
}

/* 部门区域 */
.departments-section {
  padding: 0 clamp(12px, 3vw, 20px);
  max-width: 800px;
  margin: 10px auto;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: white;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 部门树形结构 */
.departments-tree {
  padding: 0;
}

.department-tree-node {
  margin-bottom: 20px;
}

/* 部门卡片 */
.department-card {
  background: white;
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 根部门样式 */
.root-department {
  border: 2px solid #409eff;
  box-shadow: 0 6px 25px rgba(64, 158, 255, 0.15);
}

.root-department .department-header {
  background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
  color: white;
}

.root-department .department-name {
  color: white;
  display: flex;
  align-items: center;
  gap: 8px;
}

.root-department .member-count {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.root-department .toggle-icon {
  color: white;
}

/* 子部门样式 */
.sub-department {
  margin: 12px 0 12px 20px;
  border-left: 3px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.sub-department .department-header {
  /* background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); */
  padding: 12px 16px;
}

.sub-department .department-name {
  font-size: clamp(14px, 3.5vw, 18px);
  display: flex;
  align-items: center;
  gap: 6px;
}

.sub-department-icon {
  color: #6c757d;
  font-size: 14px;
}

/* 部门内容区域 */
.department-content {
  padding: 0;
}

/* 成员区域 */
.members-section {
  border-bottom: 1px solid #f0f0f0;
}

.members-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: #f8f9fa;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  border-bottom: 1px solid #e9ecef;
}

.members-header i {
  color: #409eff;
}

/* 子部门区域 */
.sub-departments {
  padding: 16px;
  background: #fafbfc;
}

.department-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #e9ecef;
  cursor: pointer;
  transition: all 0.3s ease;
}

.department-header:hover {
  /* background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%); */
}

.department-info {
  flex: 1;
}

.department-name {
  margin: 0 0 4px 0;
  font-size: clamp(16px, 4vw, 20px);
  font-weight: 600;
  color: #333;
}

.member-count {
  font-size: clamp(12px, 3vw, 14px);
  color: #666;
  background: #e3f2fd;
  padding: 2px 8px;
  border-radius: 12px;
}

.toggle-icon {
  font-size: 16px;
  color: #666;
  transition: transform 0.3s ease;
}

.toggle-icon.expanded {
  transform: rotate(180deg);
}

/* 成员列表 */
.members-list {
  padding: 0;
}

.member-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.member-item:hover {
  background-color: #f8f9fa;
}

.member-item:last-child {
  border-bottom: none;
}

/* 子部门成员列表 */
.sub-members {
  background: white;
}

.sub-members .member-item {
  padding: 10px 16px;
  margin-left: 0;
}

.member-info {
  flex: 1;
}

.member-name {
  font-size: clamp(14px, 3.5vw, 16px);
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.member-id {
  font-size: clamp(12px, 3vw, 14px);
  color: #666;
}

.member-actions {
  margin-left: 12px;
}

.member-actions .el-button {
  border-radius: 20px;
  padding: 6px 16px;
  font-size: 12px;
}

/* 无成员提示 */
.no-members {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #999;
  font-size: 14px;
}

.no-members i {
  margin-right: 8px;
  font-size: 18px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: white;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.8;
}

.empty-state h3 {
  margin: 0 0 12px 0;
  font-size: clamp(18px, 4.5vw, 24px);
  font-weight: 600;
}

.empty-state p {
  margin: 0;
  font-size: clamp(14px, 3.5vw, 16px);
  opacity: 0.8;
  line-height: 1.5;
}

/* 调动对话框样式 */
.transfer-dialog >>> .el-dialog {
  border-radius: 12px;
  overflow: hidden;
}

.transfer-dialog >>> .el-dialog__header {
  background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
  color: white;
  padding: 20px;
}

.transfer-dialog >>> .el-dialog__title {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

.transfer-dialog >>> .el-dialog__headerbtn .el-dialog__close {
  color: white;
  font-size: 20px;
}

.transfer-dialog >>> .el-dialog__body {
  padding: 24px;
}

.transfer-content h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
}

.transfer-content h4::before {
  content: "";
  width: 4px;
  height: 16px;
  background: #409eff;
  margin-right: 8px;
  border-radius: 2px;
}

/* 员工信息卡片 */
.employee-info {
  margin-bottom: 24px;
}

.info-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e9ecef;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item .label {
  font-weight: 500;
  color: #666;
  font-size: 14px;
}

.info-item .value {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

/* 目标部门选择 */
.target-department {
  margin-bottom: 24px;
}

.department-select {
  width: 100%;
}

.department-select >>> .el-input__inner {
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  padding: 12px 16px;
  font-size: 14px;
  height: 44px;
  line-height: 20px;
}

.department-select >>> .el-input__inner:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* 调动原因 */
.transfer-reason {
  margin-bottom: 24px;
}

.optional {
  font-size: 12px;
  color: #999;
  font-weight: normal;
}

.transfer-reason >>> .el-textarea__inner {
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  padding: 12px 16px;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
}

.transfer-reason >>> .el-textarea__inner:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* 对话框底部 */
.dialog-footer {
  text-align: right;
  padding: 16px 0 0;
  border-top: 1px solid #e9ecef;
}

.dialog-footer .el-button {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  margin-left: 12px;
}

.dialog-footer .el-button--primary {
  background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
  border: none;
}

.dialog-footer .el-button--primary:hover {
  background: linear-gradient(135deg, #66b1ff 0%, #409eff 100%);
}

/* 移动端优化 */
@media (max-width: 768px) {
  .search-section {
    padding: 16px;
  }

  .departments-section {
    padding: 0 12px;
  }

  .department-card {
    margin-bottom: 12px;
    border-radius: 8px;
  }

  .root-department {
    border-width: 1px;
  }

  .sub-department {
    margin: 8px 0 8px 16px;
    border-radius: 6px;
  }

  .department-header {
    padding: 12px 16px;
  }

  .members-header {
    padding: 10px 16px;
    font-size: 13px;
  }

  .sub-departments {
    padding: 12px;
  }

  .member-item {
    padding: 10px 16px;
  }

  .sub-members .member-item {
    padding: 8px 12px;
  }

  .transfer-dialog >>> .el-dialog {
    width: 95% !important;
    margin: 5vh auto !important;
  }

  .transfer-dialog >>> .el-dialog__body {
    padding: 16px;
  }

  .info-card {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .search-section {
    padding: 12px;
  }

  .search-input >>> .el-input__inner {
    height: 44px;
    line-height: 44px;
    font-size: 14px;
  }

  .department-tree-node {
    margin-bottom: 16px;
  }

  .root-department .department-name {
    font-size: clamp(14px, 4vw, 18px);
    gap: 6px;
  }

  .sub-department {
    margin: 6px 0 6px 12px;
  }

  .sub-department .department-name {
    font-size: clamp(12px, 3vw, 16px);
    gap: 4px;
  }

  .department-header {
    padding: 10px 12px;
  }

  .members-header {
    padding: 8px 12px;
    font-size: 12px;
    gap: 6px;
  }

  .sub-departments {
    padding: 8px;
  }

  .member-item {
    padding: 8px 12px;
    /* flex-direction: column; */
    align-items: flex-start;
    gap: 8px;
  }

  .sub-members .member-item {
    padding: 6px 10px;
  }

  .member-actions {
    margin-left: 0;
    align-self: flex-end;
  }

  .member-actions .el-button {
    font-size: 11px;
    padding: 4px 12px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .dialog-footer {
    text-align: center;
  }

  .dialog-footer .el-button {
    width: 100%;
    margin: 8px 0 0 0;
  }
}

/* 搜索图标样式 */
.el-select .el-icon-search {
  color: #c0c4cc;
  font-size: 14px;
}
</style>
