<template>
  <el-carousel :interval="5000" arrow="always" :autoplay="false" height="300">
    <el-carousel-item v-for="item in carouselList" :key="item.id">
      <!-- <h3>机器产品{{ item }}</h3> -->
      <img :src="item.url" :alt="'Image ' + item.id" />
    </el-carousel-item>
  </el-carousel>
</template>

<script>
import service from "@/router/request";

export default {
  name: "default",
  data() {
    return {
      carouselList: [],
    };
  },
  mounted() {
    this.GetCarouselList();
  },
  methods: {
    async GetCarouselList() {
      try {
        const response = await service.get("/Carousel/GetCarouselList");
        this.carouselList = response.data.map((item) => ({
          ...item,
          url: service.getUri({ url: item.url }).replace("/api", ""),
        }));
      } catch (error) {
        this.$message.error("获取配置数据失败！");
      }
    },
  },
};
</script>

<style scoped>
.el-carousel {
  height: 100%;
}

div >>> .el-carousel__container {
  height: 100%;
}

.el-carousel__item h3 {
  text-align: center;
}

.el-carousel__item img {
  width: 100%;
  height: 100%;
  object-fit: fill;
}
</style>
