<template>
  <div>
    <div>
      <div class="search-bar">
        <div class="search-item">
          <span class="label">查询：</span>
          <el-input v-model="keyword" placeholder="请输入姓名、工号或部门" />
        </div>
        <div class="search-item">
          <span class="label">月份：</span>
          <el-date-picker v-model="month" type="month" placeholder="选择月" value-format="yyyy-MM" @change="search">
          </el-date-picker>
        </div>
        <el-button type="primary" class="search-btn" @click="search">搜索</el-button>
        <el-button type="warning" class="search-btn" @click="exportData()">导出</el-button>
        <el-button type="warning" class="search-btn" @click="exportAllData()">导出所有</el-button>
      </div>
      <xin-chou-add></xin-chou-add>
      <div>
        <el-table :data="tableData" border style="width: 100%">
          <el-table-column prop="lastname" label="姓名" min-width="70"></el-table-column>
          <el-table-column prop="loginid" label="工号"></el-table-column>
          <el-table-column prop="sex" label="性别" min-width="40"></el-table-column>
          <el-table-column prop="departmentname" label="部门"></el-table-column>
          <el-table-column prop="jobtitlename" label="职务"></el-table-column>
          <el-table-column prop="hourlyWage" label="工时价"></el-table-column>
          <el-table-column prop="basicPerformance" label="绩效金额"></el-table-column>
          <el-table-column prop="createdate" label="创建时间" min-width="160"></el-table-column>
          <el-table-column label="操作" width="100" fixed="right">
            <template v-slot:default="scope">
              <el-tooltip content="暂无权限" :disabled="havePermissionRole([2, 8])">
                <el-button size="mini" type="primary" plain @click="handleEdit(scope.row)"
                  :disabled="!havePermissionRole([2, 8])">编辑</el-button>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination style="width: 75%" @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="currentPage" :page-sizes="[5, 10, 20, 40]" :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper" :total="totalData"></el-pagination>
      </div>

      <el-dialog title="信息" :visible.sync="fromVisible" width="40%" :close-on-click-modal="false" destroy-on-close>
        <el-form label-width="100px" style="padding-right: 50px" :model="form" :rules="rules" ref="formRef">
          <el-form-item prop="loginid" label="工号">
            <el-input :value="form.loginid" :disabled="true"></el-input>
          </el-form-item>

          <el-form-item prop="lastname" label="姓名">
            <el-input :value="form.lastname" :disabled="true"></el-input>
          </el-form-item>

          <el-form-item prop="hourlyWage" label="工时价">
            <el-input v-model="form.hourlyWage"></el-input>
          </el-form-item>
          <el-form-item prop="basicPerformance" label="绩效金额">
            <el-input v-model="form.basicPerformance" type="number"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="fromVisible = false">取 消</el-button>
          <el-button type="primary" @click="savehouwage">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import service from "@/router/request";
import XinChouAdd from "./XinChouAdd.vue";
import { havePermissionRole } from "@/utils/permission";
import { xlsx } from "@/utils/utils";

export default {
  name: "xinchou",
  components: { XinChouAdd },
  data() {
    return {
      keyword: "",
      month: "",
      tableData: [],
      totalData: 0,
      currentPage: 1, // 初始页
      pageSize: 10, // 每页的数据
      fromVisible: false,
      form: {},
      rules: {
        houwage: [
          { required: true, message: "工时价不能为空", trigger: "blur" },
        ],
      },
    };
  },

  mounted() {
    this.month = this.$moment().format('yyyy-MM')
    this.selectplan();
  },
  methods: {
    havePermissionRole,

    handleSizeChange(size) {
      this.pageSize = size;
      this.selectplan();
    },

    handleCurrentChange(page) {
      this.currentPage = page;
      this.selectplan();
    },
    search() {
      this.currentPage = 1;
      this.selectplan();
    },
    selectplan() {
      service
        .post("/XinChou/getXCMemberList", {
          fuzzy: this.keyword,
          pageNum: this.currentPage,
          pageSize: this.pageSize,
          month: this.month
        })
        .then((res) => {
          if (res.status == "succeed") {
            this.tableData = res.data?.list;
            this.totalData = res.data?.total;
          }
        });
    },

    handleEdit(row) {
      this.form = JSON.parse(JSON.stringify(row)); // 给form对象赋值  注意要深拷贝数据
      this.fromVisible = true; // 打开弹窗
    },

    savehouwage() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          service.post("/XinChou/UpdateXCMember", { 
            loginId: this.form.loginid, 
            month: this.month, 
            hourlyWage: this.form.hourlyWage, 
            basicPerformance: this.form.basicPerformance 
          }).then((res) => {
            if (res.status == "succeed") {
              // 表示成功保存
              this.$message.success("更新成功");
              this.selectplan();
              this.fromVisible = false;
            } else {
              this.$message.error("更新失败"); // 弹出错误的信息
            }
          });
        }
      });
    },

    exportData() {
      xlsx(this.tableData, {
        loginid: "工号",
        lastname: "姓名",
        sex: "性别",
        departmentname: "部门名",
        jobtitlename: "职务",
        hourlyWage: "工时价",
        basicPerformance: "绩效金额",
        createdate: "创建时间",
      }, '薪酬资料');
    },

    exportAllData() {
      service
        .post("/XinChou/getXCMemberList", {
          pageNum: 1,
          pageSize: 9999999,
          month: this.month
        })
        .then((res) => {
          if (res.status == "succeed") {
            xlsx(res.data?.list, {
              loginid: "工号",
              lastname: "姓名",
              sex: "性别",
              departmentname: "部门名",
              jobtitlename: "职务",
              hourlyWage: "工时价",
              basicPerformance: "绩效金额",
              createdate: "创建时间",
            }, '薪酬资料');
          }
        });
    }
  },
};
</script>
<style>
.el-row {
  margin-bottom: 20px;
}

.el-col {
  border-radius: 4px;
}

.bg-purple-dark {
  background: #99a9bf;
}

.bg-purple {
  background: #d3dce6;
}

.bg-purple-light {
  background: #e5e9f2;
}

.grid-content {
  border-radius: 4px;
  min-height: 36px;
}

.row-bg {
  padding: 10px 0;
  background-color: #f9fafc;
}
</style>
