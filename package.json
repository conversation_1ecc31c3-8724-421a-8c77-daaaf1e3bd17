{"name": "ok.taskmanagement", "version": "1.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@babel/plugin-transform-optional-chaining": "^7.24.7", "@tailwindcss/postcss7-compat": "^2.2.17", "autoprefixer": "^9.8.8", "axios": "^1.6.2", "core-js": "^3.37.1", "echarts": "^5.6.0", "element-ui": "^2.15.14", "font-awesome": "^4.7.0", "moment": "^2.30.1", "postcss": "^7.0.39", "vue": "^2.6.14", "vue-router": "^3.5.1", "vuex": "^3.6.2", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "sass": "^1.86.3", "sass-loader": "^10.5.2", "tailwindcss": "npm:@tailwindcss/postcss7-compat@^2.2.17", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}