<template>
  <div class="edit-requisition">
    <div class="header">
      <el-button icon="el-icon-arrow-left" @click="$router.go(-1)" circle size="small"></el-button>
      <h2>编辑领用单</h2>
    </div>

    <div class="form-container">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="领用类型" prop="requisitionType">
          <el-radio-group v-model="form.requisitionType" @change="onTypeChange">
            <el-radio :label="1">新领</el-radio>
            <el-radio :label="2">以旧换新</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="选择物品" prop="materialId">
          <el-select
            v-model="form.materialId"
            placeholder="请选择物品"
            filterable
            remote
            :remote-method="searchMaterials"
            :loading="materialLoading"
            @change="onMaterialChange"
            style="width: 100%"
          >
            <template slot="prefix">
              <i class="el-icon-search"></i>
            </template>
            <el-option v-for="item in materialList" :key="item.id" :label="item.name" :value="item.id">
              <div class="material-option">
                <span class="material-name">{{ item.name }}</span>
                <span class="material-unit">{{ item.unit }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="数量" prop="quantity">
          <el-input-number
            v-model="form.quantity"
            :min="1"
            :max="999"
            controls-position="right"
            style="width: 100%"
          ></el-input-number>
          <span v-if="selectedMaterial" class="unit-text">{{ selectedMaterial.unit }}</span>
        </el-form-item>

        <!-- 以旧换新相关字段 -->
        <div v-if="form.requisitionType === 2" class="old-material-section">
          <el-form-item label="旧物品状态" prop="oldMaterialCondition">
            <el-select v-model="form.oldMaterialCondition" placeholder="请选择旧物品状态" style="width: 100%">
              <el-option label="损坏" :value="1"></el-option>
              <el-option label="磨损" :value="2"></el-option>
              <el-option label="过期" :value="3"></el-option>
              <el-option label="其他" :value="0"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="旧物品描述" prop="oldMaterialDescription">
            <el-input
              type="textarea"
              v-model="form.oldMaterialDescription"
              placeholder="请描述旧物品的具体情况（如损坏程度、使用时间等）"
              rows="3"
            ></el-input>
          </el-form-item>
        </div>

        <el-form-item label="用途说明" prop="purpose">
          <el-input type="textarea" v-model="form.purpose" placeholder="请详细说明物品用途" rows="4"></el-input>
        </el-form-item>

        <el-form-item label="备注">
          <el-input type="textarea" v-model="form.remark" placeholder="其他需要说明的信息（选填）" rows="3"></el-input>
        </el-form-item>
      </el-form>

      <div class="form-actions">
        <el-button @click="$router.go(-1)">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting"> 保存修改 </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import service from "@/router/request";
import { MessageUtil } from "@/utils/utils";

export default {
  name: "EditRequisition",
  data() {
    return {
      submitting: false,
      materialList: [], // 物品列表
      selectedMaterial: null, // 选中的物品
      form: {
        id: null,
        requisitionType: 1, // 1=新领, 2=以旧换新
        materialId: "",
        quantity: 1,
        purpose: "",
        oldMaterialCondition: null, // 旧物品状态
        oldMaterialDescription: "", // 旧物品描述
        remark: "",
      },
      rules: {
        requisitionType: [{ required: true, message: "请选择领用类型", trigger: "change" }],
        materialId: [{ required: true, message: "请选择物品", trigger: "change" }],
        quantity: [
          { required: true, message: "请输入数量", trigger: "blur" },
          { type: "number", min: 1, message: "数量必须大于0", trigger: "blur" },
        ],
        purpose: [
          { required: true, message: "请输入用途说明", trigger: "blur" },
          { min: 5, max: 200, message: "用途说明长度在5-200个字符", trigger: "blur" },
        ],
        oldMaterialCondition: [
          {
            validator: (_, value, callback) => {
              if (this.form.requisitionType === 2 && !value) {
                callback(new Error("请选择旧物品状态"));
              } else {
                callback();
              }
            },
            trigger: "change",
          },
        ],
        oldMaterialDescription: [
          {
            validator: (_, value, callback) => {
              if (this.form.requisitionType === 2) {
                if (!value) {
                  callback(new Error("请描述旧物品情况"));
                } else if (value.length < 10 || value.length > 500) {
                  callback(new Error("旧物品描述长度在10-500个字符"));
                } else {
                  callback();
                }
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
      },
      materialLoading: false,
    };
  },
  async created() {
    await this.loadRequisitionDetail();
    await this.loadMaterials();
  },
  methods: {
    // 加载领用单详情
    async loadRequisitionDetail() {
      try {
        const id = this.$route.params.id;
        const response = await service.get(`/MaterialRequisition/GetDetail/${id}`);
        if (response.status === "succeed") {
          const data = response.data;
          this.form = {
            id: data.id,
            requisitionType: data.requisitionType,
            materialId: data.materialId,
            quantity: data.quantity,
            purpose: data.purpose,
            oldMaterialCondition: data.oldMaterialCondition,
            oldMaterialDescription: data.oldMaterialDescription || "",
            remark: data.remark || "",
          };
        } else {
          MessageUtil.error("加载领用单详情失败");
          this.$router.go(-1);
        }
      } catch (error) {
        console.error("加载领用单详情失败:", error);
        MessageUtil.error("加载领用单详情失败");
        this.$router.go(-1);
      }
    },

    // 加载物品列表
    async loadMaterials(search = "") {
      try {
        this.materialLoading = true;
        const response = await service.get("/Material", { params: { search, isActive: 1 } });
        if (response.status === "succeed") {
          this.materialList = response.data.data;
          // 设置当前选中的物品
          if (this.form.materialId) {
            this.selectedMaterial = this.materialList.find((item) => item.id === this.form.materialId);
          }
        }
      } catch (error) {
        console.error("加载物品列表失败:", error);
        MessageUtil.error("加载物品列表失败");
      } finally {
        this.materialLoading = false;
      }
    },

    // 搜索物品
    async searchMaterials(query) {
      if (query !== "") {
        await this.loadMaterials(query);
      } else {
        this.materialList = [];
      }
    },

    // 物品选择变化
    onMaterialChange(materialId) {
      this.selectedMaterial = this.materialList.find((item) => item.id === materialId);
    },

    // 领用类型变化
    onTypeChange() {
      if (this.form.requisitionType === 1) {
        // 新领时清空以旧换新相关字段
        this.form.oldMaterialCondition = null;
        this.form.oldMaterialDescription = "";
      }
    },

    // 提交表单
    async submitForm() {
      try {
        const valid = await this.$refs.formRef.validate();
        if (!valid) return;

        this.submitting = true;
        const response = await service.post("/MaterialRequisition/Update", this.form);
        if (response.status === "succeed") {
          MessageUtil.success("领用单修改成功！");
          this.$router.go(-1);
        } else {
          MessageUtil.error(response.message || "修改失败，请重试！");
        }
      } catch (error) {
        console.error("提交失败:", error);
        MessageUtil.error("提交失败，请重试！");
      } finally {
        this.submitting = false;
      }
    },
  },
};
</script>

<style scoped>
.edit-requisition {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  align-items: center;
  padding: 16px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;

  h2 {
    margin: 0 0 0 16px;
    font-size: 18px;
    font-weight: 600;
    color: #303133;
  }
}

.form-container {
  padding: 16px;
}

.el-form {
  background: white;
  /* padding: 20px; */
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.old-material-section {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin: 16px 0;
  border-left: 4px solid #409eff;
}

.unit-text {
  margin-left: 8px;
  color: #909399;
  font-size: 14px;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 24px;
  gap: 16px;

  .el-button {
    flex: 1;
    height: 44px;
    font-size: 16px;
  }
}

/* 物品选择相关样式 */
.material-option {
  display: flex;
  justify-content: space-between;
  .material-name {
    font-weight: 500;
  }
  .material-unit {
    color: #909399;
    font-size: 12px;
  }
}

/* 搜索图标样式 */
.el-select .el-icon-search {
  color: #c0c4cc;
  font-size: 14px;
}
</style>
