<template>
  <div class="grouped-multiselect">
    <el-select
      v-model="selectedValues"
      multiple
      collapse-tags
      placeholder="请选择"
      @change="handleChange"
      filterable
      size="mini"
      :key="renderKey"
    >
      <div class="custom-option-group" v-for="(group, groupIndex) in processedOptions" :key="groupIndex">
        <!-- 分组标题行 -->
        <div class="group-header" @click.stop="toggleGroup(group)" :class="{ expanded: group.expanded }">
          <el-checkbox
            :indeterminate="isGroupIndeterminate(group)"
            :checked="isGroupSelected(group)"
            @change="toggleSelectGroup(group, $event)"
            @click.native.stop
            :key="`groupCheckBox-${groupIndex}-${isGroupSelected(group)}`"
          ></el-checkbox>
          <span class="group-label">{{ group.label }}</span>
          <i
            class="group-icon"
            :class="{ 'el-icon-arrow-down': group.expanded, 'el-icon-arrow-right': !group.expanded }"
          ></i>
        </div>

        <!-- 选项内容区域 -->
        <transition name="el-zoom-in-top">
          <div class="group-options" v-show="group.expanded">
            <el-option
              v-for="(item, itemIndex) in group.children"
              :key="`opt-${itemIndex}`"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </div>
        </transition>
      </div>
    </el-select>
  </div>
</template>

<script>
export default {
  name: "GroupedMultiselect",
  props: {
    options: {
      type: Array,
      required: true,
    },
    value: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      selectedValues: [...this.value],
      processedOptions: [],
      isUpdating: false, // 防止循环更新的标志
      renderKey: 0,
    };
  },
  created() {
    this.processedOptions = this.processOptions(this.options);
  },
  watch: {
    options: {
      handler(newVal) {
        this.processedOptions = this.processOptions(newVal);
      },
      deep: true,
    },
    value: {
      handler(newVal) {
        if (!this.isUpdating) {
          this.selectedValues = [...newVal];
        }
      },
      deep: true,
    },
    selectedValues: {
      handler(newVal) {
        this.isUpdating = true;
        this.$emit("input", [...newVal]);
        this.$nextTick(() => {
          this.isUpdating = false;
        });
      },
      deep: true,
    },
  },
  methods: {
    // 重置组件状态
    resetComponent() {
      // 增加渲染key，强制重新渲染组件
      this.renderKey++;

      // 重置所有分组为折叠状态
      this.processedOptions.forEach((group) => {
        group.expanded = false;
      });
    },

    processOptions(options) {
      return options.map((group) => ({
        ...group,
        expanded: false, // 默认折叠
      }));
    },

    toggleGroup(group) {
      group.expanded = !group.expanded;
    },

    isGroupIndeterminate(group) {
      const groupValues = group.children.map((item) => item.value);
      const selectedCount = groupValues.filter((value) => this.selectedValues.includes(value)).length;
      return selectedCount > 0 && selectedCount < groupValues.length;
    },

    isGroupSelected(group) {
      if (!this.selectedValues.length) return false;
      const groupValues = group.children.map((item) => item.value);
      let isSelected = groupValues.every((value) => this.selectedValues.includes(value));
      return isSelected;
    },

    toggleSelectGroup(group, checked) {
      const groupValues = group.children.map((item) => item.value);

      this.$nextTick(() => {
        if (checked) {
          this.selectedValues = [...new Set([...this.selectedValues, ...groupValues])];
        } else {
          this.selectedValues = this.selectedValues.filter((value) => !groupValues.includes(value));
        }
      });
    },

    handleChange(val) {
      this.$emit("change", [...val]);
    },
  },
};
</script>

<style scoped>
.grouped-multiselect {
  width: 100%;
}

.custom-option-group {
  padding: 0;
}

.group-header {
  display: flex;
  align-items: center;
  padding: 8px 10px;
  cursor: pointer;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.group-header:hover {
  background-color: #e4e7ed;
}

.group-label {
  margin: 0 8px;
  font-weight: 500;
  flex: 1;
}

.group-icon {
  font-size: 12px;
  transition: transform 0.3s;
}

.group-options {
  padding: 5px 0;
}
</style>
