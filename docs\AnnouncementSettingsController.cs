using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;

namespace TaskManagement.Controllers
{
    /// <summary>
    /// 公告栏设置控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class AnnouncementSettingsController : ControllerBase
    {
        private readonly IAnnouncementSettingsService _settingsService;
        private readonly ILogger<AnnouncementSettingsController> _logger;

        public AnnouncementSettingsController(
            IAnnouncementSettingsService settingsService,
            ILogger<AnnouncementSettingsController> logger)
        {
            _settingsService = settingsService;
            _logger = logger;
        }

        /// <summary>
        /// 获取公告栏设置
        /// </summary>
        /// <returns>设置配置</returns>
        [HttpGet("getSettings")]
        public async Task<IActionResult> GetSettings()
        {
            try
            {
                var settings = await _settingsService.GetSettingsAsync();
                return Ok(new ApiResponse
                {
                    Status = "succeed",
                    Data = settings,
                    Message = "获取设置成功"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取公告栏设置失败");
                return Ok(new ApiResponse
                {
                    Status = "failed",
                    Message = "获取设置失败"
                });
            }
        }

        /// <summary>
        /// 保存公告栏设置
        /// </summary>
        /// <param name="request">设置请求</param>
        /// <returns>保存结果</returns>
        [HttpPost("saveSettings")]
        public async Task<IActionResult> SaveSettings([FromBody] SaveSettingsRequest request)
        {
            try
            {
                // 验证请求参数
                if (!ModelState.IsValid)
                {
                    return Ok(new ApiResponse
                    {
                        Status = "failed",
                        Message = "参数验证失败: " + string.Join(", ", ModelState.Values
                            .SelectMany(v => v.Errors)
                            .Select(e => e.ErrorMessage))
                    });
                }

                var settings = new AnnouncementSettings
                {
                    ScrollSpeed = request.ScrollSpeed,
                    ScrollHeight = request.ScrollHeight,
                    UpdatedAt = DateTime.Now,
                    UpdatedBy = GetCurrentUserId() // 从当前用户上下文获取
                };

                await _settingsService.SaveSettingsAsync(settings);

                return Ok(new ApiResponse
                {
                    Status = "succeed",
                    Message = "设置保存成功"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存公告栏设置失败");
                return Ok(new ApiResponse
                {
                    Status = "failed",
                    Message = "保存设置失败"
                });
            }
        }

        /// <summary>
        /// 重置公告栏设置为默认值
        /// </summary>
        /// <returns>重置结果</returns>
        [HttpPost("resetSettings")]
        public async Task<IActionResult> ResetSettings()
        {
            try
            {
                await _settingsService.ResetToDefaultAsync();
                return Ok(new ApiResponse
                {
                    Status = "succeed",
                    Message = "设置已重置为默认值"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置公告栏设置失败");
                return Ok(new ApiResponse
                {
                    Status = "failed",
                    Message = "重置设置失败"
                });
            }
        }

        /// <summary>
        /// 获取当前用户ID（示例方法）
        /// </summary>
        /// <returns>用户ID</returns>
        private int GetCurrentUserId()
        {
            // 这里应该从JWT token或session中获取当前用户ID
            // 示例实现
            return 1;
        }
    }

    /// <summary>
    /// 保存设置请求模型
    /// </summary>
    public class SaveSettingsRequest
    {
        /// <summary>
        /// 滚动速度（px/s）
        /// </summary>
        [Required(ErrorMessage = "滚动速度不能为空")]
        [Range(10, 50, ErrorMessage = "滚动速度必须在10-50px/s之间")]
        public int ScrollSpeed { get; set; }

        /// <summary>
        /// 滚动区域高度（px）
        /// </summary>
        [Required(ErrorMessage = "滚动区域高度不能为空")]
        [Range(80, 200, ErrorMessage = "滚动区域高度必须在80-200px之间")]
        public int ScrollHeight { get; set; }
    }

    /// <summary>
    /// 公告栏设置实体
    /// </summary>
    public class AnnouncementSettings
    {
        public int Id { get; set; }
        public int ScrollSpeed { get; set; }
        public int ScrollHeight { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public int UpdatedBy { get; set; }
    }

    /// <summary>
    /// API响应模型
    /// </summary>
    public class ApiResponse
    {
        public string Status { get; set; }
        public object Data { get; set; }
        public string Message { get; set; }
    }

    /// <summary>
    /// 公告栏设置服务接口
    /// </summary>
    public interface IAnnouncementSettingsService
    {
        Task<AnnouncementSettings> GetSettingsAsync();
        Task SaveSettingsAsync(AnnouncementSettings settings);
        Task ResetToDefaultAsync();
    }

    /// <summary>
    /// 公告栏设置服务实现
    /// </summary>
    public class AnnouncementSettingsService : IAnnouncementSettingsService
    {
        private readonly IRepository<AnnouncementSettings> _repository;

        public AnnouncementSettingsService(IRepository<AnnouncementSettings> repository)
        {
            _repository = repository;
        }

        public async Task<AnnouncementSettings> GetSettingsAsync()
        {
            var settings = await _repository.GetFirstOrDefaultAsync();
            if (settings == null)
            {
                // 如果没有设置记录，返回默认设置
                settings = GetDefaultSettings();
                await _repository.InsertAsync(settings);
            }
            return settings;
        }

        public async Task SaveSettingsAsync(AnnouncementSettings settings)
        {
            var existingSettings = await _repository.GetFirstOrDefaultAsync();
            if (existingSettings != null)
            {
                existingSettings.ScrollSpeed = settings.ScrollSpeed;
                existingSettings.ScrollHeight = settings.ScrollHeight;
                existingSettings.UpdatedAt = settings.UpdatedAt;
                existingSettings.UpdatedBy = settings.UpdatedBy;
                await _repository.UpdateAsync(existingSettings);
            }
            else
            {
                settings.CreatedAt = DateTime.Now;
                await _repository.InsertAsync(settings);
            }
        }

        public async Task ResetToDefaultAsync()
        {
            var defaultSettings = GetDefaultSettings();
            defaultSettings.UpdatedAt = DateTime.Now;
            
            var existingSettings = await _repository.GetFirstOrDefaultAsync();
            if (existingSettings != null)
            {
                existingSettings.ScrollSpeed = defaultSettings.ScrollSpeed;
                existingSettings.ScrollHeight = defaultSettings.ScrollHeight;
                existingSettings.UpdatedAt = defaultSettings.UpdatedAt;
                await _repository.UpdateAsync(existingSettings);
            }
            else
            {
                defaultSettings.CreatedAt = DateTime.Now;
                await _repository.InsertAsync(defaultSettings);
            }
        }

        private AnnouncementSettings GetDefaultSettings()
        {
            return new AnnouncementSettings
            {
                ScrollSpeed = 20,
                ScrollHeight = 120
            };
        }
    }

    /// <summary>
    /// 通用仓储接口（示例）
    /// </summary>
    public interface IRepository<T> where T : class
    {
        Task<T> GetFirstOrDefaultAsync();
        Task InsertAsync(T entity);
        Task UpdateAsync(T entity);
    }
}
