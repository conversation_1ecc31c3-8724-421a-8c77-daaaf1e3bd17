<template>
  <div>
    <div ref="searchBarRef" class="search-bar">
      <div class="search-item">
        <span class="label">工程号：</span>
        <el-input v-model="prono" placeholder="请输入工程号"></el-input>
      </div>
      <el-button class="search-btn" type="primary" @click="serach">搜索</el-button>
      <el-button @click="exportData" type="warning" class="search-btn">导出</el-button>
    </div>

    <!-- 计件 -->
    <el-table v-if="tableMaxHeight" :data="tableData" border :max-height="tableMaxHeight">
      <el-table-column v-for="(config, idx) of tableConfig" :key="idx" v-bind="config"></el-table-column>
    </el-table>
    <el-pagination
      ref="pageRef"
      @size-change="onSizeChange"
      @current-change="onCurrentChange"
      :page-sizes="[10, 20, 30, 40]"
      :page-size="paging.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="paging.total"
    >
    </el-pagination>
  </div>
</template>

<script>
import service from "@/router/request";
import { xlsx } from "@/utils/utils";

export default {
  name: "MachineTaskOverview",

  data() {
    return {
      prono: "",
      tableData: [],
      tableConfig: [
        // { label: "工程号", prop: "proNo" },
        { label: "任务类型", prop: "typeName", width: "60" },
        { label: "任务内容", prop: "taskName", "min-width": 160 },
        { label: "参与者", prop: "participant" },
        { label: "录入工时", prop: "hours" },
        { label: "开始日期", prop: "startDate", width: "120" },
        { label: "结束日期", prop: "endDate", width: "120" },
      ],
      paging: {
        pageNum: 1,
        pageSize: 40,
        total: 0,
      },
      tableMaxHeight: 0,
    };
  },

  mounted() {
    this.getPieceTask();
    this.computeTableMaxHeight();
  },

  methods: {
    serach() {
      this.paging.pageNum = 1;
      this.getPieceTask();
    },

    getPieceTask() {
      service.post("/DataSummary/getProNoAllTask", { prono: this.prono, ...this.paging }).then((res) => {
        if (res.status == "succeed") {
          this.tableData = res.data.list || [];
          this.tableData.forEach(
            (item) =>
              item.typeName == "计时" && (item.participant = `${item.participant}（工时价：${item.hourlyWage}）`)
          );
          this.paging.total = res.data.total;
        }
      });
    },

    onSizeChange(val) {
      this.paging.pageSize = val;
      this.serach();
    },

    onCurrentChange(val) {
      this.paging.pageNum = val;
      this.getPieceTask();
    },

    computeTableMaxHeight() {
      let main = document.getElementById("main");
      this.tableMaxHeight =
        main.clientHeight - this.$refs.searchBarRef.clientHeight - this.$refs.pageRef.$el.clientHeight - 20;
    },

    exportData() {
      let map = {
        proNo: "工程号",
        typeName: "任务类型",
        taskName: "任务内容",
        participant: "参与者",
        hours: "录入工时",
        startDate: "开始日期",
        endDate: "结束日期",
      };
      const loading = this.$loading({
        lock: true,
        text: "导出中.....",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      service.post("/DataSummary/getProNoAllTask", { prono: this.prono, pageNum: 1, pageSize: 9999999 }).then((res) => {
        if (res.status == "succeed") {
          res.data.list.forEach(
            (item) =>
              item.typeName == "计时" && (item.participant = `${item.participant}（工时价：${item.hourlyWage}）`)
          );
          xlsx(res.data.list, map, `${this.prono}机器任务汇总`);
          loading.close();
        }
      });
    },
  },
};
</script>

<style></style>
