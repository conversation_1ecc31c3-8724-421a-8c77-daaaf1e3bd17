<template>
  <div class="container" id="container">
    <div style="padding: 20px 10px 15px; position: sticky; top: 0; z-index: 1">
      <el-button @click="$router.back()" size="medium" icon="el-icon-back" circle></el-button>
    </div>
    <h2 class="title">成员列表</h2>
    <div style="display: flex; margin: 0 10px 10px; justify-content: flex-end; align-items: center">
      <span style="white-space: nowrap">月份切换：</span>
      <el-date-picker v-model="month" type="month" placeholder="选择月" value-format="yyyy-MM"> </el-date-picker>
      <el-button v-if="!isMobile()" @click="exportData()" class="ml-1" type="warning"> 导出全部数据 </el-button>
    </div>
    <el-tree
      :data="groupMembers"
      :props="{ children: 'list' }"
      @node-click="treeNodeClick"
      node-key="id"
      :default-expanded-keys="['GRO1']"
    >
      <div
        class="custom-tree-node"
        :class="{ active: activeMember && activeMember.memno == data.memno }"
        slot-scope="{ node, data }"
      >
        <span>{{ node.level == 1 ? data.groname : data.memname }}</span>
        <span v-if="node.level == 1 && !isMobile() && data.grono?.includes('GRO')">
          <el-link @click.stop="exportData(data)" :underline="false" type="warning">导出数据</el-link>
        </span>
        <span v-else-if="node.level == 2">
          <el-button type="text" size="mini" @click="toDailyOverview(data)"> 查看日报 </el-button>
        </span>
      </div>
    </el-tree>
  </div>
</template>

<script>
import service from "@/router/request";
import { xlsx, isMobile } from "@/utils/utils";

export default {
  name: "groupMembers",
  props: { loginid: "" },

  data() {
    return {
      groupMembers: [],
      month: this.$moment().format("yyyy-MM"),
      activeMember: null,
    };
  },

  mounted() {
    isMobile() && document.getElementById("container").setAttribute("style", "min-height: 100vh;max-height: 100vh");
    this.getGroupMembers();
    document.getElementById("container").addEventListener("scroll", this.setScrollTop);
  },

  activated() {
    document.getElementById("container").addEventListener("scroll", this.setScrollTop);
    const scrollTop = localStorage.getItem("GMScrollTop");
    if (scrollTop) {
      document.getElementById("container").scrollTo(0, scrollTop);
    }
  },

  deactivated() {
    document.getElementById("container")?.removeEventListener("scroll", this.setScrollTop);
  },

  beforeDestroy() {
    document.getElementById("container")?.removeEventListener("scroll", this.setScrollTop);
  },

  methods: {
    isMobile,
    getGroupMembers() {
      service
        .get("/DailyReport/getGroupMembers", {
          params: {
            loginid: this.$store.state.user.loginId,
            roleId: this.$store.state.user.roleId,
          },
        })
        .then((res) => {
          if (res.status == "succeed") {
            this.groupMembers = res.data;
            this.groupMembers.forEach((item, idx) => (item.id = `GRO${idx + 1}`));
          }
        });
    },

    toDailyOverview(data) {
      this.activeMember = data;
      this.$router.push({
        path: "dailyOverview",
        query: {
          loginid: data.memno,
          lastname: data.memname,
          month: this.month,
        },
      });
    },

    treeNodeClick(data, node, e) {
      node.level != 1 && this.toDailyOverview(data);
    },

    exportData(data = null) {
      const loading = this.$loading({
        lock: true,
        text: "导出中.....",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      service
        .post("/TaskHours/getTaskHoursOfGroup", {
          groNos: data ? [data.grono] : this.groupMembers.map((item) => item.grono),
          date: this.month,
        })
        .then((res) => {
          if (res.status == "succeed") {
            let exportData = res.data.map((item) => ({
              memname: item.memname,
              recordDate: item.recordDate,
              hours: item.hours,
              taskType: item.taskNo.includes("TAD")
                ? "计件"
                : item.task.ttm_no.includes("TTM")
                ? "计时"
                : item.task.ttm_no.includes("TPT")
                ? "临时"
                : "任务不存在",
              prono: item.task.prono || item.task.ttdprono,
              taskName: item.taskName,
            }));
            xlsx(
              exportData,
              {
                memname: "姓名",
                recordDate: "录入时间",
                hours: "录入工时",
                taskType: "任务类型",
                prono: "工程号",
                taskName: "任务内容",
              },
              `月度日报-${data ? data.groname : ""}`
            );
            loading.close();
          }
        });
    },

    setScrollTop() {
      localStorage.setItem("GMScrollTop", document.getElementById("container").scrollTop);
    },
  },
};
</script>

<style scoped>
.container {
  position: relative;
  padding-bottom: 40px;
  background-color: rgb(255, 255, 255);
  min-height: 100vh;
  box-sizing: border-box;

  min-height: 100%;
  max-height: 100%;
  overflow-y: auto;
}

.title {
  margin: 0;
  text-align: center;
  padding: 0 20px 20px;
}

:deep(.el-tree-node__content) {
  padding: 10px 0;
  height: auto;
  border-bottom: 1px solid #d7d7d7;
}

.custom-tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex: 1;
  padding-right: 20px;
}

.active {
  font-weight: bold;
  color: #409eff;
}
</style>
