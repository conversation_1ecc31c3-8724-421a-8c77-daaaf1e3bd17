<template>
  <div>
    <div ref="searchBarRef" class="search-bar">
      <div class="search-item">
        <span class="label">任务类型：</span>
        <el-select v-model="searchParam.taskType" @change="taskTypeChange" placeholder="请选择任务类型">
          <el-option v-for="item in taskTypeOptions" :key="item.value" v-bind="item"></el-option>
        </el-select>
      </div>
      <div class="search-item" style="width: 450px">
        <span class="label">搜索内容：</span>
        <el-select v-model="searchParam.fieldName" @change="serach" class="mr-1">
          <el-option
            v-for="opt of searchParam.taskType == 'TAC' ? pieceFieldOptions : timeFieldOptions"
            :key="opt.value"
            v-bind="opt"
          ></el-option>
        </el-select>
        <el-input v-model="searchParam.fieldValue" placeholder="请输入" clearable></el-input>
      </div>
      <div class="search-item" style="width: 500px">
        <span class="label">日期类型：</span>
        <el-select v-model="searchParam.dateFieldName" @change="serach" class="mr-1">
          <el-option
            v-for="item of searchParam.taskType == 'TAC' ? pieceDateTypeOptions : timeDateTypeOptions"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          ></el-option>
        </el-select>
        <span class="label">日期：</span>
        <el-date-picker
          v-model="searchParam.date"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          :clearable="false"
        ></el-date-picker>
      </div>
      <el-button class="search-btn" type="primary" @click="serach">搜索</el-button>
      <el-button class="search-btn" type="warning" @click="exportData">导出</el-button>
      <el-button class="search-btn" type="warning" @click="exportAllData">导出全部</el-button>
    </div>
    <el-table v-if="tableMaxHeight" :data="tableData" border :max-height="tableMaxHeight">
      <template v-for="(config, index) of searchParam.taskType == 'TAC' ? pieceTaskTableConfig : timeTaskTableConfig">
        <el-table-column v-if="!config.disabled" :key="config.prop + index" v-bind="config">
          <template v-slot="scope">
            <template v-if="config.type == 'tag' && scope.row[config.prop]">
              <el-tag v-for="(item, index) of scope.row[config.prop].split(',')" :key="item + index" class="mb-1">{{
                item
              }}</el-tag>
            </template>
            <template v-else>{{
              config.options
                ? config.options.find((item) => item.value == scope.row[config.prop])?.label || ""
                : scope.row[config.prop]
            }}</template>
          </template>
        </el-table-column>
      </template>

      <el-table-column v-if="searchParam.taskType != 'TAC'" label="操作" fixed="right" width="150">
        <div slot-scope="scope" style="display: flex; justify-content: space-around">
          <el-link @click.capture="openUpdateDialog(scope.row)" type="primary">修改</el-link>
          <el-link @click="deleteTimeTask(scope.row)" type="danger">删除</el-link>
        </div>
      </el-table-column>
    </el-table>
    <el-pagination
      ref="paginationRef"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="paging.pageNum"
      :page-sizes="[5, 10, 20, 40]"
      :page-size="paging.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="paging.total"
    ></el-pagination>
    <el-dialog :visible.sync="updateDialogVisible" title="修改任务">
      <el-form v-if="taskForm" :model="taskForm" :inline="true" label-position="top">
        <el-form-item v-for="config of timeTaskFormConfig" :key="config.prop" :label="config.label">
          <el-select v-if="config.type == 'select'" v-model="taskForm[config.prop]" v-bind="config.attrs || {}">
            <el-option v-for="(item, index) of config.options" :key="item.value + index" v-bind="item"></el-option>
          </el-select>
          <el-input v-else v-model="taskForm[config.prop]" v-bind="config.attrs"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button @click="updateDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="updateTask">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import service from "../../../router/request";
import { xlsx } from "../../../utils/utils";
export default {
  name: "taskDetails",
  data() {
    const currentDate = this.$moment().format("yyyy-MM-DD");
    const startDate = this.$moment().subtract(12, "months").format("yyyy-MM-DD");
    return {
      searchParam: {
        taskType: "TTM",
        fieldName: "",
        fieldValue: "",
        dateFieldName: "",
        date: [startDate, currentDate],
      },
      taskTypeOptions: [
        { value: "TTM", label: "计时任务" },
        { value: "TPT", label: "临时任务" },
        { value: "TAC", label: "计件任务" },
      ],
      timeDateTypeOptions: [
        { value: "ttmcreated", label: "创建日期" },
        { value: "ttmsdate", label: "开始日期" },
        { value: "ttmedate", label: "结束日期" },
      ],
      pieceDateTypeOptions: [{ value: "tadcreatedtime", label: "创建日期" }],
      timeFieldOptions: [
        { label: "任务内容", value: "ttmcontent" },
        { label: "任务创建者", value: "lastname" },
        { label: "任务成员名", value: "ttdextername" },
        { label: "任务成员工号", value: "ttdexter" },
        { label: "工程编码", value: "ttdprono" },
      ],
      pieceFieldOptions: [
        { label: "工程编码", value: "prono" },
        { label: "任务成员名", value: "memname" },
        { label: "任务成员工号", value: "memno" },
      ],

      tableData: [],
      tableMaxHeight: 0,
      timeTaskTableConfig: [
        { label: "任务成员", prop: "ttdextername", width: 65 },
        { label: "工号", prop: "ttdexter", width: 80 },
        { label: "所属巴组", prop: "groname", "min-width": 100 },
        { label: "所属团队", prop: "tmename", "min-width": 100 },
        { label: "任务工单编号", prop: "ttm_no", "min-width": 120 },
        { label: "任务编号", prop: "ttdno", "min-width": 120 },
        { label: "任务内容", prop: "ttmcontent", "min-width": 200 },
        { label: "工程编码", prop: "ttdprono", "min-width": 130, type: "tag" },
        { label: "工时", prop: "ttdhour", "min-width": 50 },
        // { label: "工时价", prop: "ttdprice", "min-width": 50 },
        // { label: "总工价", prop: "ttdwage", "min-width": 50 },
        { label: "单价", prop: "ttdprice", "min-width": 50, disabled: true },
        { label: "总工价", prop: "ttdwage", "min-width": 50, disabled: true },
        {
          label: "结算类型",
          prop: "ttdsmtype",
          width: 50,
          options: [
            { label: "个人", value: "P" },
            { label: "团队", value: "T" },
          ],
        },
        { label: "任务开始时间", prop: "ttmsdate", width: 105 },
        { label: "任务结束时间", prop: "ttmedate", width: 105 },
        { label: "任务创建者", prop: "lastname", width: 65 },
        { label: "任务创建日期", prop: "ttmcreated", width: 140 },
        { label: "任务备注", prop: "ttdremarks", "min-width": 200 },
      ],
      pieceTaskTableConfig: [
        { label: "任务成员", prop: "memname", width: 65 },
        { label: "工号", prop: "memno", width: 80 },
        { label: "所属巴组", prop: "departmentname", "min-width": 100 },
        { label: "所属团队", prop: "tmename", "min-width": 100 },
        { label: "任务工单编号", prop: "tas_no", "min-width": 120 },
        { label: "任务编号", prop: "tadno", "min-width": 120 },
        { label: "工程编码", prop: "prono", "min-width": 130, type: "tag" },
        { label: "部位", prop: "mpaname", "min-width": 100 },
        { label: "组件", prop: "sasname", "min-width": 100 },
        { label: "子件", prop: "ssuname", "min-width": 100 },
        { label: "工时", prop: "manhours", "min-width": 50 },
        { label: "工时价", prop: "mhprice", "min-width": 60 },
        { label: "数量", prop: "tasqty", "min-width": 40 },
        { label: "工价", prop: "taswage", "min-width": 60 },
        { label: "任务交期", prop: "tasddeldate", width: 155 },
        { label: "任务创建者", prop: "creater", width: 65 },
        { label: "任务创建日期", prop: "tadcreatedtime", width: 155 },
        { label: "任务备注", prop: "tasdremarks", "min-width": 200 },
      ],
      paging: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      updateDialogVisible: false,
      taskForm: null,
      timeTaskFormConfig: [
        { label: "任务成员", prop: "ttdextername", attrs: { disabled: true } },
        { label: "任务内容", prop: "ttmcontent", attrs: { disabled: true } },
        {
          label: "工程编码",
          prop: "ttdprono",
          type: "select",
          options: [],
          attrs: {
            filterable: true,
            remote: true,
            "reserve-keyword": true,
            multiple: true,
            placeholder: "请输入工程编码",
            "remote-method": () => {},
            loading: false,
          },
        },
        { label: "工时", prop: "ttdhour" },
        {
          label: "结算类型",
          prop: "ttdsmtype",
          type: "select",
          options: [
            { label: "个人", value: "P" },
            { label: "团队", value: "T" },
          ],
        },
        { label: "任务备注", prop: "ttdremarks" },
      ],
      pieceTaskFormConfig: [],
    };
  },

  mounted() {
    this.taskTypeChange(this.searchParam.taskType);
    this.computeTableMaxHeight();
    let pronoItem = this.timeTaskFormConfig.find((item) => item.prop == "ttdprono");
    pronoItem.attrs["remote-method"] = this.queryDepartments;
    this.queryDepartments();
  },

  methods: {
    handleSizeChange(size) {
      this.paging.pageSize = size;
      this.getTask();
    },

    handleCurrentChange(page) {
      this.paging.pageNum = page;
      this.getTask();
    },

    serach() {
      this.paging.pageNum = 1;
      this.getTask();
    },

    getTask() {
      service
        .post(this.searchParam.taskType == "TAC" ? "/TaskCheck/getAllAsstemdet" : "/TaskCheck/getAllTimeTask", {
          ...this.searchParam,
          ...this.paging,
          startDate: this.searchParam.date[0],
          endDate: this.searchParam.date[1],
        })
        .then((res) => {
          if (res.status == "succeed") {
            this.tableData = res.data?.list || [];
            this.paging.total = res.data?.total || 0;
          }
        });
    },

    taskTypeChange(value) {
      this.searchParam.fieldName = value == "TAC" ? this.pieceFieldOptions[0].value : this.timeFieldOptions[0].value;
      this.searchParam.dateFieldName =
        value == "TAC" ? this.pieceDateTypeOptions[0].value : this.timeDateTypeOptions[0].value;
      if (value == "TPT") {
        this.timeTaskTableConfig.find((item) => item.prop == "ttdhour").label = "数量";
        this.timeTaskTableConfig.find((item) => item.prop == "ttdprice").disabled = false;
        this.timeTaskTableConfig.find((item) => item.prop == "ttdwage").disabled = false;
      }
      if (value == "TTM") {
        this.timeTaskTableConfig.find((item) => item.prop == "ttdhour").label = "工时";
        this.timeTaskTableConfig.find((item) => item.prop == "ttdprice").disabled = true;
        this.timeTaskTableConfig.find((item) => item.prop == "ttdprice").disabled = true;
      }
      this.serach();
    },

    computeTableMaxHeight() {
      let main = document.getElementById("main");
      this.tableMaxHeight =
        main.clientHeight - this.$refs.searchBarRef.clientHeight - this.$refs.paginationRef.$el.clientHeight - 20 - 40;
    },

    openUpdateDialog(row) {
      this.taskForm = { ...row, ttdprono: row.ttdprono.split(",") };
      this.updateDialogVisible = true;
    },

    //模糊查询工程编码
    queryDepartments(query) {
      let attrs = this.timeTaskFormConfig.find((item) => item.prop == "ttdprono").attrs;
      attrs.loading = true;
      service
        .post("/JiShi/select_ttdprono", { fuzzy: query })
        .then((res) => {
          if (res.status == "succeed") {
            attrs.loading = false;
            this.timeTaskFormConfig.find((item) => item.prop == "ttdprono").options = res.data.list.map((item) => ({
              label: item,
              value: item,
            }));
          }
        })
        .catch((error) => {
          console.error(error);
        });
    },

    updateTask() {
      service
        .post("/JiShi/updateTimeTask", {
          ...this.taskForm,
          ttdprono: this.taskForm.ttdprono.join(","),
          ttdwage: this.taskForm.ttdhour * this.taskForm.ttdprice,
        })
        .then((res) => {
          if (res.status == "succeed") {
            this.getTask();
            this.taskForm = null;
            this.updateDialogVisible = false;
            this.$message.success("修改成功");
          } else {
            this.$message.error(`修改失败：${res.message}`);
          }
        });
    },

    deleteTimeTask(row) {
      service.post("/TaskHours/getTaskHoursList", { taskNos: [row.ttdno] }).then((res) => {
        if (res.status == "succeed") {
          this.$confirm(
            `${res.data?.length ? "任务已产生日报，删除任务的同时会删除其所关联日报，" : ""}确定删除该任务?`,
            "提示",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }
          )
            .then(() => {
              service.post("/JiShi/deleteJiShi", [row.ttdno]).then((delRes) => {
                if (delRes.status == "succeed") {
                  this.getTask();
                  this.$message.success("删除成功");
                } else {
                  this.$message.error(`删除失败：${delRes.message}`);
                }
              });
            })
            .catch(() => {
              this.$message({
                type: "info",
                message: "已取消删除",
              });
            });
        } else {
          this.$message.error(res.err);
        }
      });
    },

    exportData() {
      let exportConfig = {};
      (this.searchParam.taskType == "TAC" ? this.pieceTaskTableConfig : this.timeTaskTableConfig).forEach((config) => {
        exportConfig[config.prop] = config.label;
      });
      xlsx(
        this.tableData,
        exportConfig,
        this.taskTypeOptions.find((item) => item.value == this.searchParam.taskType).label + ".xlsx"
      );
    },

    exportAllData() {
      let exportConfig = {};
      (this.searchParam.taskType == "TAC" ? this.pieceTaskTableConfig : this.timeTaskTableConfig).forEach((config) => {
        exportConfig[config.prop] = config.label;
      });
      service
        .post(this.searchParam.taskType == "TAC" ? "/TaskCheck/getAllAsstemdet" : "/TaskCheck/getAllTimeTask", {
          ...this.searchParam,
          pageNum: 1,
          pageSize: 9999999,
          startDate: this.searchParam.date[0],
          endDate: this.searchParam.date[1],
        })
        .then((res) => {
          if (res.status == "succeed") {
            xlsx(
              res.data.list,
              exportConfig,
              this.taskTypeOptions.find((item) => item.value == this.searchParam.taskType).label + ".xlsx"
            );
          }
        });
    },
  },
};
</script>

<style></style>
