<template>
  <div class="mb-6 bg-white rounded-xl shadow-card p-4">
    <div class="flex items-center space-x-3">
      <div class="relative flex-grow">
        <input
          type="text"
          :placeholder="placeholder"
          v-model="inputValue"
          class="w-full pl-10 pr-4 py-2 border border-base-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all"
          @keyup.enter="search"
        />
        <i @click="search" class="fa fa-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 transform"></i>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "XSearchBar",
  props: {
    value: {
      type: String,
      default: "",
    },
    placeholder: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      inputValue: this.value,
    };
  },
  watch: {
    value(val) {
      this.inputValue = val;
    },
    inputValue(val) {
      this.$emit("input", val);
    },
  },
  methods: {
    search() {
      this.$emit("search", this.inputValue);
    },
  },
};
</script>

<style scoped>
/* 可根据需要添加样式 */
</style>
