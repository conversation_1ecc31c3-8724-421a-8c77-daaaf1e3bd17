<template>
  <div class="bg-gray-100 min-h-screen pb-16">
    <!-- 顶部导航栏 -->
    <TopNavigationBar title="我的提案">
      <template #tool-bar>
        <i class="el-icon-search text-xl" @click="toggleSearchPanel"></i>
      </template>
    </TopNavigationBar>

    <!-- 搜索面板 -->
    <transition name="slide-down">
      <div v-show="showSearchPanel" class="bg-white p-4 shadow-md">
        <el-input
          v-model="searchParams.title"
          placeholder="搜索提案标题"
          clearable
          class="mb-3"
          prefix-icon="el-icon-search"
        ></el-input>

        <el-select v-model="searchParams.status" placeholder="选择状态" class="w-full mb-3">
          <el-option label="全部状态" :value="''"></el-option>
          <el-option label="待审核" :value="1"></el-option>
          <el-option label="审核中" :value="2"></el-option>
          <el-option label="已通过" :value="3"></el-option>
          <el-option label="已拒绝" :value="4"></el-option>
        </el-select>

        <el-date-picker
          v-model="searchParams.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="w-full mb-3"
        ></el-date-picker>

        <div class="flex justify-between">
          <el-button plain @click="resetSearch" class="flex-1 mr-2">重置</el-button>
          <el-button type="primary" @click="fetchProposals" class="flex-1">搜索</el-button>
        </div>
      </div>
    </transition>

    <!-- 提案列表 -->
    <div class="p-3 space-y-3">
      <div
        v-for="proposal in proposalList"
        :key="proposal.id"
        class="bg-white rounded-lg shadow p-4"
        @click="viewProposalDetail(proposal)"
      >
        <div class="flex justify-between items-start mb-2">
          <h3 class="text-lg font-medium text-gray-900 line-clamp-1">{{ proposal.title }}</h3>
          <el-tag :type="getStatusType(proposal.status)" size="small">
            {{ getStatusText(proposal.status) }}
          </el-tag>
        </div>

        <div class="flex items-center text-xs text-gray-500 mb-2">
          <span class="flex items-center mr-3">
            <i class="el-icon-user mr-1"></i>
            {{ proposal.proposerName }}
          </span>
          <span class="flex items-center mr-3">
            <i class="el-icon-time mr-1"></i>
            {{ formatDate(proposal.submitTime) }}
          </span>
          <span v-if="proposal.implementationDepartment" class="flex items-center">
            <i class="el-icon-office-building mr-1"></i>
            {{ proposal.implementationDepartment }}
          </span>
        </div>

        <div v-if="proposal.finishDate" class="flex items-center text-xs text-gray-500 mb-2">
          <span class="flex items-center">
            <i class="el-icon-date mr-1"></i>
            预期完成：{{ formatDate(proposal.finishDate) }}
          </span>
        </div>

        <p class="text-gray-700 text-sm mb-3 truncate" :title="proposal.currentSituation">
          {{ proposal.currentSituation }}
        </p>

        <div class="flex justify-between items-center">
          <div>
            <el-tag
              v-for="tag in proposal.tags.split(',').splice(0, 2)"
              :key="tag"
              class="mr-1"
              size="mini"
              type="info"
            >
              <i class="el-icon-collection-tag mr-1"></i>
              {{ IMPROVEMENT_TAGS.find((t) => t.value == tag)?.label || tag }}
            </el-tag>
          </div>

          <el-button size="mini" type="primary" plain @click.stop="editProposal(proposal)" v-if="proposal.status === 1">
            修改
          </el-button>
        </div>
      </div>

      <!-- 加载更多 -->
      <div class="text-center py-4" v-if="hasMore && proposalList.length > 0">
        <el-button :loading="loading" @click="loadMore" plain class="w-full">
          {{ loading ? "加载中..." : "加载更多" }}
        </el-button>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && proposalList.length === 0" class="text-center py-10">
        <i class="el-icon-document text-4xl text-gray-400 mb-3"></i>
        <p class="text-gray-500">暂无提案数据</p>
        <el-button type="primary" plain @click="fetchProposals" class="mt-3"> 刷新 </el-button>
      </div>
    </div>

    <!-- 提案详情模态框 (默认隐藏) -->
    <div v-if="showProposalDetail" class="fixed inset-0 flex items-center justify-center z-50">
      <div class="absolute inset-0 bg-black bg-opacity-50"></div>
      <div class="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 relative z-10 shadow-2xl max-h-[90vh] overflow-y-auto">
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-lg font-medium text-gray-900">提案详情</h3>
          <button @click="closeProposalDetail" class="text-gray-400 hover:text-gray-500 transition duration-200">
            <i class="fa fa-times"></i>
          </button>
        </div>

        <div v-if="selectedProposal" class="space-y-6 edit-form">
          <div class="border-b border-gray-200 pb-4">
            <h4 class="text-xl font-semibold text-gray-800 mb-2">{{ selectedProposal.title }}</h4>
            <div class="flex flex-wrap items-center text-sm text-gray-500 mb-4">
              <span class="mr-4"><i class="fa fa-user-o mr-1"></i>{{ selectedProposal.proposerName }}</span>
              <span class="mr-4"><i class="fa fa-clock-o mr-1"></i>{{ selectedProposal.createdAt }}</span>
              <span v-if="selectedProposal.implementationDepartment" class="mr-4">
                <i class="fa fa-building-o mr-1"></i>{{ selectedProposal.implementationDepartment }}
              </span>
              <span v-if="selectedProposal.finishDate" class="mr-4">
                <i class="fa fa-calendar-o mr-1"></i>{{ formatDate(selectedProposal.finishDate) }}
              </span>
              <div class="mt-1">
                <el-tag v-for="tag in selectedProposal.tags.split(',')" :key="tag" class="mr-1" size="mini" type="info">
                  <i class="el-icon-collection-tag mr-1"></i>
                  {{ IMPROVEMENT_TAGS.find((t) => t.value == tag)?.label || tag }}
                </el-tag>
              </div>
            </div>
          </div>

          <div>
            <h5 class="text-md font-semibold text-gray-800 mb-2">现状描述</h5>
            <p class="text-gray-700">{{ selectedProposal.currentSituation }}</p>
          </div>

          <div>
            <h5 class="text-md font-semibold text-gray-800 mb-2">改善建议</h5>
            <p class="text-gray-700">{{ selectedProposal.suggestion }}</p>
          </div>

          <div>
            <h5 class="text-md font-semibold text-gray-800 mb-2">预期效果</h5>
            <p class="text-gray-700">{{ selectedProposal.expectedEffect }}</p>
          </div>

          <div v-if="selectedProposal.status !== 1">
            <h5 class="text-md font-semibold text-gray-800 mb-2">处理信息</h5>
            <p class="text-gray-700 mb-2">
              <span class="font-medium">处理人:</span> {{ selectedProposal.review.reviewer }}
            </p>
            <p class="text-gray-700 mb-2">
              <span class="font-medium">处理时间:</span> {{ formatDate(selectedProposal.review.reviewTime) }}
            </p>
            <p class="text-gray-700 mb-2">
              <span class="font-medium">处理结果:</span>
              <span
                :class="{
                  'text-green-600': selectedProposal.status === 2,
                  'text-red-600': selectedProposal.status === 3,
                }"
              >
                {{ selectedProposal.status === 2 ? "通过" : "拒绝" }}
              </span>
            </p>
            <p class="text-gray-700"><span class="font-medium">意见:</span> {{ selectedProposal.review.comment }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 修改提案模态框 (默认隐藏) -->
    <div v-if="showEditProposal" class="fixed inset-0 flex items-center justify-center z-50">
      <div class="absolute inset-0 bg-black bg-opacity-50"></div>
      <div class="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 relative z-10 shadow-2xl max-h-[90vh] overflow-y-auto">
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-lg font-medium text-gray-900">修改提案</h3>
          <button @click="closeEditProposal" class="text-gray-400 hover:text-gray-500 transition duration-200">
            <i class="fa fa-times"></i>
          </button>
        </div>

        <el-form :model="editFormData" :rules="rules" ref="editProposalForm" label-width="80px" class="edit-form">
          <!-- 基本信息 -->
          <div class="mb-8">
            <h2 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <i class="fa fa-user-circle-o text-blue-500 mr-2"></i>
              基本信息
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2">
              <el-form-item label="姓名" prop="proposer">
                <el-input
                  v-model="editFormData.proposerName"
                  placeholder="请输入您的姓名"
                  clearable
                  @blur="validateField('proposer')"
                  disabled
                ></el-input>
              </el-form-item>

              <el-form-item label="部门" prop="department">
                <el-input
                  v-model="editFormData.departmentName"
                  placeholder="请输入您的部门"
                  clearable
                  @blur="validateField('department')"
                  disabled
                ></el-input>
              </el-form-item>

              <el-form-item label="职位" prop="jobTitle">
                <el-input
                  v-model="editFormData.jobTitleName"
                  placeholder="请输入您的职位"
                  clearable
                  disabled
                ></el-input>
              </el-form-item>

              <el-form-item label="联系方式" prop="contact">
                <el-input
                  v-model="editFormData.contact"
                  placeholder="请输入您的联系方式"
                  clearable
                  @blur="validateField('contact')"
                ></el-input>
                <div class="text-xs text-gray-500 mt-1">支持手机号码或邮箱</div>
              </el-form-item>
            </div>
          </div>

          <!-- 提案信息 -->
          <div class="mb-8">
            <h2 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <i class="fa fa-lightbulb-o text-yellow-500 mr-2"></i>
              提案信息
            </h2>

            <el-form-item label="提案标题" prop="title">
              <el-input
                v-model="editFormData.title"
                placeholder="请输入提案标题"
                clearable
                @blur="validateField('title')"
              ></el-input>
            </el-form-item>

            <el-form-item label="提案标签" prop="tags">
              <el-select
                v-model="editFormData.tags"
                multiple
                placeholder="请选择提案类型"
                @change="validateField('tags')"
              >
                <el-option v-for="opt in IMPROVEMENT_TAGS" v-bind="opt"></el-option>
              </el-select>
              <div class="text-xs text-gray-500 mt-1">可多选，最多选择3个标签</div>
            </el-form-item>

            <el-form-item label="紧急程度" prop="urgency">
              <el-select v-model="editFormData.urgency" placeholder="请选择紧急程度" @change="validateField('urgency')">
                <el-option label="正常" :value="1"></el-option>
                <el-option label="重要" :value="2"></el-option>
                <el-option label="紧急" :value="3"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="现状描述" prop="currentSituation">
              <el-input
                type="textarea"
                v-model="editFormData.currentSituation"
                placeholder="请描述当前存在的问题或不足"
                :rows="4"
                show-word-limit
                maxlength="1000"
                @blur="validateField('currentSituation')"
              ></el-input>
              <div class="text-xs text-gray-500 mt-1">请详细描述现状，至少10个字符</div>
            </el-form-item>

            <el-form-item label="改善建议" prop="suggestion">
              <el-input
                type="textarea"
                v-model="editFormData.suggestion"
                placeholder="请详细描述您的改善建议"
                :rows="6"
                show-word-limit
                maxlength="2000"
                @blur="validateField('suggestion')"
              ></el-input>
              <div class="text-xs text-gray-500 mt-1">请提供具体的改善措施和方案，至少20个字符</div>
            </el-form-item>

            <el-form-item label="预期效果" prop="expectedEffect">
              <el-input
                type="textarea"
                v-model="editFormData.expectedEffect"
                placeholder="请描述实施该建议后的预期效果"
                :rows="4"
                show-word-limit
                maxlength="1000"
                @blur="validateField('expectedEffect')"
              ></el-input>
            </el-form-item>

            <el-form-item label="实施难度" prop="difficulty">
              <el-radio-group v-model="editFormData.difficulty">
                <el-radio :label="1">容易</el-radio>
                <el-radio :label="2">中等</el-radio>
                <el-radio :label="3">困难</el-radio>
              </el-radio-group>
            </el-form-item>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <el-form-item label="实施部门" prop="implementationDepartment">
                <el-select
                  v-model="editFormData.implementationDepartment"
                  placeholder="请选择实施部门"
                  filterable
                  class="w-full"
                >
                  <el-option v-for="dept in departmentList" :key="dept.value" :label="dept.label" :value="dept.value" />
                </el-select>
                <div class="text-xs text-gray-500 mt-1">选择负责实施的部门</div>
              </el-form-item>

              <el-form-item label="预期完成日期" prop="finishDate">
                <el-date-picker
                  v-model="editFormData.finishDate"
                  type="date"
                  placeholder="请选择完成日期"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  class="w-full"
                />
                <div class="text-xs text-gray-500 mt-1">预计完成改善的日期</div>
              </el-form-item>
            </div>
          </div>

          <!-- 附件上传 -->
          <div class="mb-8">
            <h2 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <i class="fa fa-paperclip text-green-500 mr-2"></i>
              附件上传
            </h2>
            <el-form-item label="上传附件">
              <el-upload
                ref="uploadRef"
                action="#"
                accept=".jpg,.jpeg,.png,.pdf,.doc,.docx,.xls,.xlsx"
                multiple
                :auto-upload="false"
                class="upload-demo"
                :before-remove="beforeRemove"
                :on-change="handleFileChange"
                :on-preview="handlePreview"
                :file-list="editFormData.attachments"
              >
                <el-button size="small" type="primary"> <i class="fa fa-plus mr-1"></i>选择文件 </el-button>
                <div slot="tip" class="el-upload__tip text-xs text-gray-500 mt-2">
                  支持 JPG, PNG, PDF, Word, Excel 等格式，单个文件不超过10MB
                </div>
              </el-upload>
            </el-form-item>
          </div>

          <!-- 提交按钮 -->
          <div class="flex justify-center pt-4 border-t border-gray-100">
            <el-button
              type="primary"
              size="large"
              @click="submitEditForm"
              class="w-full md:w-auto flex items-center justify-center"
              :loading="submitting"
            >
              <i class="fa fa-paper-plane mr-2"></i>
              <span>{{ submitting ? "提交中..." : "提交提案" }}</span>
            </el-button>
          </div>
        </el-form>
      </div>
    </div>

    <!--  -->
    <el-dialog :visible.sync="previewDialogVisible" title="文件预览" width="80%">
      <FilePreview
        :file-url="previewFileUrl"
        :file-name="previewFileName"
        @close="previewDialogVisible = false"
      ></FilePreview>
    </el-dialog>
  </div>
</template>

<script>
import TopNavigationBar from "@/components/XTopNavigationBar.vue";
import { debounce, MessageUtil } from "@/utils/utils";
import FilePreview from "@/components/XFilePreview.vue";
import service from "@/router/request";
import { IMPROVEMENT_TAGS } from "@/utils/constants";

export default {
  components: { TopNavigationBar, FilePreview },
  data() {
    return {
      IMPROVEMENT_TAGS,
      loading: false,
      proposalList: [],
      searchParams: {
        page: 1,
        pageSize: 10,
        title: "",
        status: "",
        dateRange: [],
        proposer: this.$store.state.user.loginId, // 当前用户ID或姓名
      },
      // 状态映射
      statusMap: {
        1: { text: "待审核", type: "warning" },
        2: { text: "已通过", type: "success" },
        3: { text: "已拒绝", type: "danger" },
      },
      // 紧急程度映射
      urgencyMap: {
        1: "正常",
        2: "重要",
        3: "紧急",
      },
      showSearchPanel: false,
      showProposalDetail: false,
      selectedProposal: null,
      showEditProposal: false,
      editFormData: {
        id: null,
        proposer: "",
        department: "",
        jobTitle: "",
        contact: "",
        title: "",
        tags: [],
        urgency: 1,
        currentSituation: "",
        suggestion: "",
        expectedEffect: "",
        difficulty: 1,
        implementationDepartment: "",
        finishDate: "",
        attachments: [],
      },
      rules: {
        proposer: [{ required: true, message: "提案人不能为空", trigger: "blur" }],
        contact: [
          { required: true, message: "联系方式不能为空", trigger: "blur" },
          {
            pattern: /^1[3-9]\d{9}$|^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
            message: "请输入正确的手机号码或邮箱",
            trigger: "blur",
          },
        ],
        title: [
          { required: true, message: "提案标题不能为空", trigger: "blur" },
          { max: 255, message: "提案标题不能超过255个字符", trigger: "blur" },
        ],
        tags: [{ type: "array", max: 3, message: "最多选择3个标签", trigger: "change" }],
        currentSituation: [
          { required: true, message: "现状描述不能为空", trigger: "blur" },
          { min: 10, message: "现状描述至少10个字符", trigger: "blur" },
        ],
        suggestion: [
          { required: true, message: "改善建议不能为空", trigger: "blur" },
          { min: 20, message: "改善建议至少20个字符", trigger: "blur" },
        ],
        expectedEffect: [
          { required: true, message: "预期效果不能为空", trigger: "blur" },
          { min: 10, message: "预期效果至少10个字符", trigger: "blur" },
        ],
        difficulty: [{ required: true, message: "请选择实施难度", trigger: "change" }],
      },
      submitting: false,
      previewDialogVisible: false,
      previewFileUrl: "",
      previewFileName: "",
      hasMore: true,

      departmentList: [
        { value: "生产部", label: "生产部" },
        { value: "质量部", label: "质量部" },
        { value: "技术部", label: "技术部" },
        { value: "设备部", label: "设备部" },
        { value: "安全部", label: "安全部" },
        { value: "物流部", label: "物流部" },
        { value: "采购部", label: "采购部" },
        { value: "人事部", label: "人事部" },
      ],
    };
  },
  created() {
    // 初始化时加载当前用户的提案
    this.fetchProposals();
    this.getDepartments();
  },
  methods: {
    getStatusType(status) {
      return this.statusMap[status]?.type || "info";
    },
    getStatusText(status) {
      return this.statusMap[status]?.text || "未知状态";
    },
    getUrgencyText(urgency) {
      return this.urgencyMap[urgency] || "未知";
    },
    formatDate(dateString) {
      if (!dateString) return "";
      return dateString.split(" ")[0]; // 只显示日期部分
    },
    toggleSearchPanel() {
      this.showSearchPanel = !this.showSearchPanel;
    },
    getDepartments() {
      service.get("/Department/GetDepartments").then((res) => {
        if (res.status == "succeed") {
          this.departmentList = res.data?.map((item) => ({ value: item.name, label: item.name }));
        }
      });
    },
    async fetchProposals() {
      this.loading = true;
      // 格式化日期参数
      const dateParams = this.formatDateParams();
      try {
        const response = await service.get("/ImprovementProposal/GetMyProposal", {
          params: {
            page: 1, // 搜索时重置为第一页
            pageSize: this.searchParams.pageSize,
            title: this.searchParams.title,
            status: this.searchParams.status,
            proposer: this.searchParams.proposer,
            ...dateParams,
          },
        });
        this.proposalList = response.map((item) => ({
          ...item,
          statusText: this.getStatusText(item.status),
          urgencyText: this.getUrgencyText(item.urgency),
          submitTime: this.formatDateTime(item.submitTime),
        }));
        this.hasMore = response.length >= this.searchParams.pageSize;
        this.searchParams.page = 1;
      } catch (error) {
        MessageUtil.error("获取提案列表失败");
        console.error(error);
      } finally {
        this.loading = false;
      }
    },
    async loadMore() {
      this.loading = true;
      try {
        const nextPage = this.searchParams.page + 1;
        const dateParams = this.formatDateParams();
        const response = await this.$api.getProposalList({
          page: nextPage,
          pageSize: this.searchParams.pageSize,
          title: this.searchParams.title,
          status: this.searchParams.status,
          proposer: this.searchParams.proposer,
          ...dateParams,
        });
        if (response.data.list.length > 0) {
          this.proposalList = [
            ...this.proposalList,
            ...response.data.list.map((item) => ({
              ...item,
              statusText: this.getStatusText(item.status),
              urgencyText: this.getUrgencyText(item.urgency),
              submitTime: this.formatDateTime(item.submitTime),
            })),
          ];
          this.searchParams.page = nextPage;
          this.hasMore = response.data.list.length >= this.searchParams.pageSize;
        } else {
          this.hasMore = false;
        }
      } catch (error) {
        console.error("加载更多失败:", error);
      } finally {
        this.loading = false;
      }
    },
    // 格式化日期参数
    formatDateParams() {
      if (this.searchParams.dateRange && this.searchParams.dateRange.length === 2) {
        return {
          startDate: this.searchParams.dateRange[0].toISOString().split("T")[0],
          endDate: this.searchParams.dateRange[1].toISOString().split("T")[0],
        };
      }
      return {};
    },
    // 格式化日期时间
    formatDateTime(dateString) {
      if (!dateString) return "";
      const date = new Date(dateString);
      return date.toLocaleString();
    },
    resetSearch() {
      this.searchParams = {
        page: 1,
        pageSize: 10,
        title: "",
        status: "",
        dateRange: [],
        proposer: this.$store.state.user.loginId,
      };
      this.fetchProposals();
      this.showSearchPanel = false;
    },
    viewProposalDetail(row) {
      this.selectedProposal = row;
      this.showProposalDetail = true;
    },
    closeProposalDetail() {
      this.showProposalDetail = false;
      this.selectedProposal = null;
    },
    async editProposal(proposal) {
      this.editFormData = {
        ...proposal,
        tags: proposal.tags ? proposal.tags.split(",").map(Number) : [],
        departmentName: this.$store.state.user.departmentName || "",
        jobTitleName: this.$store.state.user.jobTitleName || "",
        attachments: proposal.attachments
          ? proposal.attachments.split(",").map((url, idx) => {
              const ext = url.split(".").pop();
              return { url: service.getUri({ url }).replace("/api", ""), name: `附件${idx + 1}.${ext}` };
            })
          : [],
      };
      this.showEditProposal = true;
    },
    closeEditProposal() {
      this.showEditProposal = false;
      this.editFormData = {
        id: null,
        proposer: "",
        department: "",
        jobTitle: "",
        contact: "",
        title: "",
        tags: [],
        urgency: 1,
        currentSituation: "",
        suggestion: "",
        expectedEffect: "",
        difficulty: 1,
        implementationDepartment: "",
        finishDate: "",
        attachments: [],
      };
    },
    async submitEditForm() {
      this.$refs.editProposalForm.validate(async (valid) => {
        if (valid) {
          this.submitting = true;
          let attachments = "";
          try {
            attachments = await this.submitUpload();
          } catch (e) {
            this.submitting = false;
            return e.message && MessageUtil.warning(`文件上传失败: ${e.message}`);
          }
          let result = {
            ...this.editFormData,
            tags: this.editFormData.tags.join(","),
            attachments,
          };
          console.log("提交的提案数据:", result);
          let url = "/ImprovementProposal/UpdateProposal";
          try {
            const response = await service.put(url, result);
            // 清除草稿
            this.clearDraft();
            MessageUtil.success("提案修改成功");
            // 关闭模态框
            this.closeEditProposal();
            // 刷新提案列表
            this.fetchProposals();
          } catch (error) {
            MessageUtil.error("提案修改失败");
            console.error(error);
          } finally {
            this.submitting = false;
          }
        } else {
          MessageUtil.error("表单填写不完整，请检查");
          return false;
        }
      });
    },
    validateField(field) {
      this.$refs.editProposalForm.validateField(field);
    },
    clearDraft() {
      localStorage.removeItem("proposal_draft");
    },
    async submitUpload() {
      // 筛选已上传和未上传的文件
      const uploadedFiles = this.editFormData.attachments.filter((f) => f.url);
      const unuploadedFiles = this.editFormData.attachments.filter((f) => !f.url);

      let domain = service.getUri().replace("/api", "");
      let urls = uploadedFiles.map((f) => f.url.replace(domain, ""));

      if (unuploadedFiles.length > 0) {
        const formData = new FormData();

        for (const file of unuploadedFiles) {
          if (file.raw && this.beforeUpload(file.raw)) formData.append("files", file.raw || file);
          else throw new Exception();
        }

        formData.append("type", "proposal"); // 假设上传类型为提案

        const res = await service.post("/Upload/UploadFile", formData);

        if (res.status === "succeed" && Array.isArray(res.data)) {
          urls = urls.concat(res.data);
        } else {
          throw new Exception(res.message);
        }
      }

      return urls.join(",");
    },
    // 上传前检查
    beforeUpload(file) {
      const isJPG = file.type === "image/jpeg";
      const isPNG = file.type === "image/png";
      const isPDF = file.type === "application/pdf";
      const isDOC = file.type === "application/msword";
      const isDOCX = file.type === "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
      const isXLS = file.type === "application/vnd.ms-excel";
      const isXLSX = file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";

      const isAllowed = isJPG || isPNG || isPDF || isDOC || isDOCX || isXLS || isXLSX;

      if (!isAllowed) {
        MessageUtil.warning("上传文件格式不正确，仅支持 JPG, PNG, PDF, Word, Excel 格式");
        return false;
      }

      const isLt10M = file.size / 1024 / 1024 < 50;
      if (!isLt10M) {
        MessageUtil.warning("上传文件大小不能超过 50MB");
        return false;
      }

      return true;
    },
    handlePreview(file) {
      this.previewFileUrl = file.url || URL.createObjectURL(file.raw);
      this.previewFileName = file.name;
      this.previewDialogVisible = true;
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`, "", { customClass: "phone-message-box" });
    },
    handleFileChange(file, fileList) {
      this.editFormData.attachments = fileList;
    },
  },
};
</script>

<style scoped>
/* 自定义过渡动画 */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}
.slide-down-enter,
.slide-down-leave-to {
  transform: translateY(-20px);
  opacity: 0;
}

/* 卡片点击效果 */
.bg-white {
  transition: transform 0.2s, box-shadow 0.2s;
}
.bg-white:active {
  transform: scale(0.98);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 文本截断 */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 对话框圆角 */
:deep(.el-dialog) {
  border-radius: 12px !important;
}
:deep(.dialog-proposal-detail .el-dialog__body) {
  max-height: 450px;
  overflow-y: auto;
}
.edit-form {
  max-height: 450px;
  overflow-y: auto;
}

.el-radio {
  margin-right: 15px;
}
</style>
