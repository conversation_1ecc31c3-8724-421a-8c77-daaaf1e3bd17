<template>
  <div class="container">
    <TopNavigationBar title="日报"></TopNavigationBar>
    <el-main class="main">
      <div class="option-box">
        <template v-for="opt of options">
          <div v-if="opt.show" @click="$router.push({ path: opt.path })" class="option">
            <i :class="opt.icon"></i>
            <span class="label">{{ opt.label }}</span>
          </div>
        </template>
      </div>
    </el-main>
  </div>
</template>

<script>
import TopNavigationBar from "@/components/XTopNavigationBar.vue";
import { isMobile } from "@/utils/utils";

export default {
  components: { TopNavigationBar },
  data() {
    return {
      options: [
        { label: "每日日报", icon: "el-icon-tickets", path: "/reportForDay", show: true },
        { label: "月度日报", icon: "el-icon-tickets", path: "/groupMembers", show: true },
        { label: "工时录入", icon: "el-icon-edit", path: "/entryHours", show: false },
      ],
    };
  },

  mounted() {
    this.options.find((item) => item.label == "工时录入").show =
      !this.$store.state.user.roleId || this.$store.state.user.loginId == "802521";
  },

  methods: {},
};
</script>

<style scoped>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding-bottom: env(safe-area-inset-bottom, 20px);
}

.main {
  margin: 0;
  padding: 20px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.title {
  margin: 0;
  text-align: center;
  padding: 30px 20px 20px;
  color: white;
  font-size: clamp(1.5rem, 4vw, 2rem);
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.option-box {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: clamp(16px, 4vw, 24px);
  padding: 0 clamp(16px, 4vw, 24px);
  max-width: 600px;
  margin: 0 auto;
}

.option {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  aspect-ratio: 1;
  min-height: 120px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 16px;
}

.option:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 1);
}

.option:active {
  transform: translateY(-2px);
  transition: transform 0.1s ease;
  background-color: rgba(64, 158, 255, 0.1);
}

.option i {
  font-size: clamp(28px, 6vw, 40px);
  color: #409eff;
  margin-bottom: clamp(8px, 2vw, 12px);
  transition: all 0.3s ease;
}

.option:hover i {
  color: #66b1ff;
  transform: scale(1.1);
}

.option .label {
  font-size: clamp(12px, 3vw, 16px);
  color: #333;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
  width: auto;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .title {
    padding: 24px 16px 16px;
  }

  .option-box {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px;
    padding: 0 16px;
  }

  .option {
    min-height: 100px;
    border-radius: 12px;
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .title {
    padding: 20px 12px 12px;
  }

  .option-box {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    padding: 0 12px;
  }

  .option {
    min-height: 90px;
    border-radius: 8px;
    padding: 10px;
  }
}

/* 确保在小屏幕上也有合适的最小尺寸 */
@media (max-width: 320px) {
  .option-box {
    gap: 8px;
    padding: 0 8px;
  }

  .option {
    min-height: 80px;
    padding: 8px;
  }
}
</style>
