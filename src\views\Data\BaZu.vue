<template>
  <div>
    <el-breadcrumb separator="/">
      <el-breadcrumb-item> <i class="el-icon-date"></i> 巴组列表 </el-breadcrumb-item>
    </el-breadcrumb>

    <div>
      <BaZuAdd></BaZuAdd>
    </div>

    <div class="cantainer">
      <el-table :data="currentPageData" border>
        <el-table-column type="index" label="序号" width="35"></el-table-column>
        <el-table-column label="巴组名称" prop="groname"></el-table-column>
        <el-table-column label="巴组编号" prop="grono" min-width="125"></el-table-column>

        <el-table-column label="管理者" prop="grohmname"></el-table-column>
        <el-table-column label="管理者工号" prop="groheadman"></el-table-column>
        <!-- <el-table-column label="当前状态" prop="groSFlag" width="80">
            <template v-slot="scope">
              <el-tag size="medium">
                {{ scope.row.groSFlag == 0 ? "停用" : "正常" }}
              </el-tag>
            </template>
          </el-table-column> -->
        <el-table-column label="备注" prop="groremarks" min-width="300"></el-table-column>
        <el-table-column label="操作" fixed="right" width="150">
          <template v-slot:default="scope">
            <el-button @click="toGroupDetail(scope.row)" type="primary" size="small">详情</el-button>
            <el-button @click="openGroupDlg(scope.row)" type="warning" size="small">修改</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        style="width: 75%"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[5, 10, 20, 40]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalData"
      ></el-pagination>
    </div>

    <el-dialog title="修改巴组" :visible.sync="groupDlg" width="80%" :close-on-click-modal="false">
      <el-form :model="groupForm" ref="groupFormRef" :rules="groupFormRules">
        <el-form-item label="巴组名称" prop="groname">
          <el-input v-model="groupForm.groname"></el-input>
        </el-form-item>
        <el-form-item prop="groheadman" label="管理者">
          <el-select
            v-model="groupForm.groheadman"
            @change="groupForm.grohmname = groupAdmins.find((item) => item.loginid == groupForm.groheadman).name"
            filterable
          >
            <el-option v-for="(item, idx) of groupAdmins" :label="item.name" :value="item.loginid"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="groremarks" label="巴组简介">
          <el-input type="textarea" :row="3" v-model="groupForm.groremarks" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="closeGroupDlg">取 消</el-button>
        <el-button type="primary" @click="updateGroup">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import service from "@/router/request";
import BaZuAdd from "./BaZuAdd.vue";
export default {
  name: "bazu",
  components: { BaZuAdd },
  data() {
    return {
      currentPage: 1,
      pageSize: 10,
      userList: [],
      totalData: 0,

      groupDlg: false,
      groupForm: {
        groname: "",
        grono: "",
        groheadman: "",
        groremarks: "",
      },
      groupFormRules: { groname: [{ required: true, message: "请输入巴组名", trigger: "blur" }] },
      groupAdmins: [],
    };
  },
  computed: {
    // 根据当前页码和每页显示条数，计算出当前页要显示的数据
    currentPageData() {
      const startIndex = (this.currentPage - 1) * this.pageSize;
      const endIndex = this.currentPage * this.pageSize;
      return this.userList.slice(startIndex, endIndex);
    },
  },

  mounted() {
    this.load();
  },
  methods: {
    handleSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1;
    },

    handleCurrentChange(page) {
      this.currentPage = page;
    },

    load() {
      service.get("/Bazu/bazulist").then((res) => {
        if (res.status == "succeed") {
          this.userList = res.data;
          this.totalData = res.data.length;
        }
      });
    },

    openGroupDlg(row) {
      this.getSystemMember();
      this.groupForm = row;
      this.groupDlg = true;
    },

    toGroupDetail(row) {
      this.$router.push({ name: "groupDetail", query: { groupInfo: encodeURIComponent(JSON.stringify(row)) } });
    },

    getSystemMember() {
      service.post("/Public/GetSystemMember", { roleId: 1 }).then((res) => {
        if (res.status == "succeed") {
          this.groupAdmins = res.data || null;
        }
      });
    },

    closeGroupDlg() {
      this.$refs.groupFormRef.resetFields();
      this.groupDlg = false;
    },

    updateGroup() {
      this.$refs.groupFormRef.validate((result) => {
        if (result) {
          service.post("/Bazu/modifyBazu", this.groupForm).then((res) => {
            if (res.status == "succeed") {
              this.$message.success("修改成功");
              this.groupDlg = false;
            }
          });
        }
      });
    },
  },
};
</script>

<style scoped>
.footer {
  z-index: 500;
  position: fixed;
  bottom: 0;
  width: 100%;
  --footer-height: 10px;
  line-height: var(--footer-height);
  color: #fff;
}

.container {
  position: relative;
  /* 确保容器相对定位 */
}

.left-top-button {
  position: absolute;
  /* 绝对定位 */
  top: 0;
  /* 距离顶部边界为0像素 */
  left: 0;
  /* 距离左侧边界为0像素 */
}

>>> .el-icon.el-icon-arrow-right {
  color: #409eff;
  font-weight: bold;
}
</style>
