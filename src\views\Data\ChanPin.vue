<template>
  <div class="deit">
    <div class="crumbs">
      <div class="search-bar">
        <div class="search-item">
          <span class="label">机型：</span>
          <el-input v-model="keyword" placeholder="请输入名称或型号" />
        </div>
        <el-button type="primary" class="search-btn" @click="search">搜索</el-button>
        <el-button type="warning" class="search-btn" @click="exportAll">导出所有产品</el-button>
        <div>
          <AddChanPin />
        </div>
      </div>

      <div class="cantainer">
        <el-table :data="currentPageData" @cell-click="handleRowClick" border>
          <el-table-column type="index" label="序号"></el-table-column>
          <el-table-column label="机型名称" prop="mmoname"></el-table-column>
          <el-table-column label="机型型号" prop="mmomodel"></el-table-column>
          <el-table-column label="所属巴组" prop="grono"></el-table-column>
          <el-table-column label="录入姓名" prop="createName" min-width="70"></el-table-column>
          <el-table-column label="录入时间" prop="createdTime" min-width="160"></el-table-column>
          <el-table-column label="机型备注" prop="mmoremarks" min-width="250"></el-table-column>
          <el-table-column label="当前状态" prop="mmosflag" width="100">
            <template v-slot="scope">
              <el-tag size="medium" :type="tagType(scope.row.mmosflag)">
                {{
                  scope.row.mmosflag === 0
                    ? "停用"
                    : scope.row.mmosflag === 1
                    ? "整机未审"
                    : scope.row.mmosflag === 2
                    ? "变更未审"
                    : scope.row.mmosflag === 3
                    ? "新增未审"
                    : scope.row.mmosflag === 4
                    ? "正常"
                    : ""
                }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template v-slot:default="scope">
              <el-tooltip content="暂无权限" :disabled="havePermissionRole([2, 8])">
                <el-button
                  size="mini"
                  type="primary"
                  plain
                  @click.stop="handleEdit(scope.row)"
                  :disabled="!havePermissionRole([2, 8])"
                  >编辑</el-button
                >
              </el-tooltip>
              <el-tooltip content="暂无权限" :disabled="havePermissionRole([2, 8])">
                <el-button
                  size="mini"
                  type="danger"
                  plain
                  @click.stop="shanchu(scope.row)"
                  :disabled="!havePermissionRole([2, 8])"
                  >删除</el-button
                >
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          style="width: 75%"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[5, 10, 20, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalData"
        ></el-pagination>
      </div>
    </div>

    <el-dialog title="信息" :visible.sync="fromVisible" width="40%" :close-on-click-modal="false" destroy-on-close>
      <el-form label-width="100px" style="padding-right: 50px" :model="form" :rules="rules" ref="formRef">
        <el-form-item prop="mmoname" label="机型名称">
          <el-input v-model="form.mmoname" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item prop="mmomodel" label="机型型号">
          <el-input v-model="form.mmomodel" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item prop="mmoremarks" label="机型备注">
          <el-input type="textarea" :row="3" v-model="form.mmoremarks" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="fromVisible = false">取 消</el-button>
        <el-button type="primary" @click="savemmodetails">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import service from "@/router/request";
import AddChanPin from "@/views/Data/AddChanPin";
import { xlsx } from "../../utils/utils";
import { havePermissionRole } from "@/utils/permission";

export default {
  components: { AddChanPin },
  name: "chanpin",
  data() {
    return {
      currentPage: 1,
      pageSize: 10,
      chanpinList: [],
      totalData: 0,
      fromVisible: false,
      form: {},
      rules: {
        mmoname: [{ required: true, message: "名称不能为空", trigger: "blur" }],
        mmomodel: [{ required: true, message: "型号不能为空", trigger: "blur" }],
      },
      keyword: "",
    };
  },
  computed: {
    currentPageData() {
      const startIndex = (this.currentPage - 1) * this.pageSize;
      const endIndex = this.currentPage * this.pageSize;
      return this.chanpinList.slice(startIndex, endIndex);
    },
  },
  mounted() {
    this.load();
  },
  methods: {
    havePermissionRole,

    handleSizeChange(size) {
      // 每页显示条数改变时，重新设置当前页码，并进行相应的处理
      this.pageSize = size;
      this.currentPage = 1;
    },
    handleCurrentChange(page) {
      // 当前页码改变时，重新设置当前页码，并进行相应的处理
      this.currentPage = page;
    },
    handleClick(row) {
      console.log(row);
    },
    //查询所有产品
    load() {
      service.get("/Chanpin/Getmmodetails").then((res) => {
        if (res.status == "succeed") {
          this.chanpinList = res.data;
          this.totalData = res.data.length;
        } // 更新总数据条数
      });
      console.log(this.chanpinList);
    },
    handleRowClick(row) {
      localStorage.setItem("rowData", JSON.stringify(row));
      // 跳转到详情页面
      this.$router.push({ path: "/chanpin1" });
    },
    handleEdit(row) {
      this.form = JSON.parse(JSON.stringify(row)); // 给form对象赋值  注意要深拷贝数据
      this.fromVisible = true; // 打开弹窗
    },
    //编辑更新产品信息
    savemmodetails() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          service.post("/Chanpin/Updatemmodetails", this.form).then((res) => {
            if (res.status == "succeed") {
              // 表示成功保存
              this.$message.success("保存成功");
              this.load();
              this.fromVisible = false;
            } else {
              this.$message.error("保存失败"); // 弹出错误的信息
            }
          });
        }
      });
    },
    //删除产品
    shanchu(row) {
      const mmono = row.mmono; // 获取产品ID
      // 弹出确认弹窗
      this.$confirm("确定要删除该机型吗?该机型所有数据将会消失！", "提示", {
        type: "warning",
      })
        .then(() => {
          // 用户点击了确定按钮，发送 DELETE 请求到后端接口
          service
            .post("/Chanpin/Deletemmodetails", {
              // 将产品ID作为参数
              mmono: mmono,

              // eslint-disable-next-line no-unused-vars
            })
            .then((res) => {
              if (res.status == "succeed") {
                // 请求成功的处理逻辑
                // 根据后端返回的数据，做出相应操作
                console.log("删除机型成功");
                this.load();
              }
            })
            .catch((error) => {
              // 请求失败的处理逻辑
              // 输出错误信息或进行错误处理
              console.error("删除机型失败:", error);
            });
        })
        .catch(() => {
          // 用户点击了取消按钮
          console.log("取消删除小组操作");
        });
    },
    search() {
      this.currentPage = 1;
      this.onSubmit();
    },
    //模糊查询机型信息
    onSubmit() {
      service
        .post("/Chanpin/select_mmodetails?query=" + this.keyword)
        .then((res) => {
          if (res.status == "succeed") {
            this.chanpinList = res.data;
          }
        })
        .catch((error) => {
          console.error(error);
        });
    },

    exportAll() {
      service.post("/Chanpin/getAllMMOAndMPA", {}).then((res) => {
        if (res.status == "succeed") {
          xlsx(
            res.data,
            {
              mmoname: "机型名称",
              mmomodel: "机型型号",
              mmono: "机型编号",
              mpaname: "部位",
              sasname: "组件",
              ssuname: "子件",
              wpr_no: "工序",
              manhours: "工时",
              mhprice: "工时价",
              swsflagstatus: "状态",
              swremarks: "备注",
            },
            "产品资料.xlsx"
          );
        }
      });
    },

    tagType(mmosflag) {
      switch (mmosflag) {
        case 0:
          return "danger";
        case 4:
          return "success";
        default:
          return "warning";
      }
    },
  },
};
</script>
<style></style>
