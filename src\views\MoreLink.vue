<template>
  <div class="more-link">
    <div v-for="(group, gIdx) in groupedLinks" :key="group.groupName" class="link-group">
      <h3>{{ group.groupName }}</h3>
      <div v-for="(item, idx) in group.links" :key="item.link" class="link-item">
        <i :class="item.icon"></i>
        <a :href="item.link" target="_blank" rel="noopener noreferrer">
          {{ item.text }}
        </a>
        <el-tooltip :content="item.description" placement="bottom" :disabled="!item.isOverflow">
          <p :ref="`description${gIdx}${idx}`">{{ item.description }}</p>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "MoreLink",
  data: () => ({
    groupedLinks: [
      {
        groupName: "公司内网",
        links: [
          {
            text: "欧克协同商务系统",
            link: "http://***************/",
            description: "泛微新一代协同融合平台",
            isOverflow: false,
          },
          {
            text: "欧克考勤系统",
            link: "http://***************:9099/okhrm/index",
            description: "欧克人事考勤系统 -- 考勤，申请，审批，排班",
            isOverflow: false,
          },
          {
            text: "轮播首页",
            link: "http://***************:8484/ast/",
            description: "装配车间实时生产动态",
            isOverflow: false,
          },
          { text: "耐斯首页", link: "https://www.nicepacker.com/", description: "欧克官网", isOverflow: false },
        ],
      },
      {
        groupName: "合作伙伴",
        links: [
          { text: "金红叶纸业", link: "https://www.ghy.com.cn/", description: "金红叶纸业官网", isOverflow: false },
          { text: "中顺洁柔", link: "https://www.zsjr.com/", description: "中顺洁柔官网", isOverflow: false },
          { text: "恒安集团", link: "https://www.hengan.com/", description: "恒安集团官网", isOverflow: false },
          { text: "维达集团", link: "https://www.vindapaper.com/", description: "维达集团官网", isOverflow: false },
        ],
      },
      {
        groupName: "常用工具",
        links: [
          {
            text: "快递100",
            link: "https://www.kuaidi100.com/",
            description: "查快递、寄快递、管快递",
            isOverflow: false,
          },
          {
            text: "我的钢铁网",
            link: "https://www.mysteel.com/",
            description: "大宗商品行业数据服务商，涵盖黑色金属、有色金属、建筑材料、能源化工、农产品、新能源等",
            isOverflow: false,
          },
          {
            text: "长江有色金属网",
            link: "https://www.ccmn.cn/",
            description: "有色金属价格行情网站，有色金属采购批发市场",
            isOverflow: false,
          },
          {
            text: "上海有色网",
            link: "https://www.smm.cn/",
            description:
              "有色金属价格行情门户，提供最新的上海、长江等有色金属市场期货现货行情、资讯、交易、金融服务以及物流相关信息",
            isOverflow: false,
          },
          {
            text: "中国有色网",
            link: "https://www.cnmn.com.cn/",
            description: "中国有色金属工业协会主办的综合性门户网站",
            isOverflow: false,
          },
          {
            text: "中国招标网",
            link: "https://zb.yfb.qianlima.com/",
            description: "全国招标采购信息平台",
            isOverflow: false,
          },
        ],
      },
      //   {
      //     groupName: "政务中心",
      //     links: [],
      //   },
    ],
  }),

  mounted() {
    this.checkDescriptionOverflow();
  },

  methods: {
    checkDescriptionOverflow() {
      this.groupedLinks.forEach((group, gIdx) => {
        group.links.forEach((item, idx) => {
          const descriptionElement = this.$refs[`description${gIdx}${idx}`][0];
          if (descriptionElement) {
            item.isOverflow = descriptionElement.scrollWidth > descriptionElement.clientWidth;
          }
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.more-link {
  display: flex;
  flex-direction: column;

  .link-group {
    margin-bottom: 2rem;

    h3 {
      margin-bottom: 1rem;
      font-size: 1.2rem;
      color: #343a40;
    }

    .link-item {
      display: inline-block;
      margin: 0 20px 20px 0;
      padding: 1rem;
      border: 1px solid #dee2e6;
      border-radius: 0.5rem;
      background-color: #f8f9fa;
      width: auto;
      max-width: 300px;

      i {
        margin-right: 0.5rem;
      }

      a {
        color: #007bff;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }

      p {
        margin: 0.5rem 0 0;
        color: #6c757d;
        font-size: 0.9rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
</style>
