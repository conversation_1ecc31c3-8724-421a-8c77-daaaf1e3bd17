<template>
  <div class="home">
    <!--  -->
    <div ref="searchBarRef" class="search-bar">
      <div class="search-item">
        <span class="label">查询类别：</span>
        <el-select v-model="value" filterable placeholder="请选择">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </div>
      <el-input v-model="mokey" class="search-item" filterable placeholder="输入机型名称、规格、型号"></el-input>
      <div class="search-item">
        <span class="label">开始日期：</span>
        <el-date-picker
          v-model="start"
          placeholder="开始日期"
          format="yyyy 年 MM 月 dd 日"
          value-format="yyyy-MM-dd"
        ></el-date-picker>
      </div>
      <div class="search-item">
        <span class="label">结束日期：</span>
        <el-date-picker
          v-model="end"
          placeholder="结束日期"
          format="yyyy 年 MM 月 dd 日"
          value-format="yyyy-MM-dd"
        ></el-date-picker>
      </div>
      <el-button type="primary" class="search-btn" @click="search">搜索</el-button>
    </div>
    <!--  -->
    <el-table v-if="maxHeight" :data="currentPageData" border :max-height="maxHeight">
      <el-table-column fixed prop="planno" label="计划单号" width="200"></el-table-column>

      <el-table-column prop="billtypen" label="单据类型" width="120"></el-table-column>
      <el-table-column prop="mmono" label="有无资料" width="200">
        <template v-slot="scope">
          <el-select
            v-model="scope.row.mmono"
            @change="mmoChange(scope.row)"
            placeholder="选择产品资料分配"
            clearable
            @clear="debounce(mmoChange, 500)(scope.row)"
          >
            <el-option
              v-for="item of MMOList"
              :label="`${item.mmoname}(${item.mmomodel})：${item.mmoremarks}`"
              :value="item.mmono"
              :key="item.mmono"
            ></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="tasno" label="有无派单" width="140">
        <template v-slot="scope">{{ scope.row.tasno ? scope.row.tasno : "无派单" }}</template>
      </el-table-column>
      <el-table-column prop="prono" label="机型编码" width="150"></el-table-column>
      <el-table-column prop="proname" label="机型名称" width="200"></el-table-column>
      <el-table-column prop="prospecs" label="机型规格" width="150" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="mmomodel" label="机型型号" width="80"></el-table-column>
      <el-table-column prop="counit" label="单位" width="60"></el-table-column>
      <el-table-column prop="plannedqty" label="计划数量" width="60"></el-table-column>
      <el-table-column prop="planneddate" label="计划日期" width="120"></el-table-column>
      <el-table-column prop="deliveryTime" label="计划交期" width="120"></el-table-column>
      <el-table-column prop="remarks" label="备注" width="150" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="sorderno" label="销售单号" width="150"></el-table-column>
      <!-- <el-table-column prop="adstatus" label="计划状态" width="150"></el-table-column> -->
      <el-table-column prop="apno" label="装配计划单号" width="150"></el-table-column>
      <el-table-column prop="apcdate" label="装配计划创建日期" width="180"></el-table-column>
      <el-table-column prop="apdmdate" label="装配计划需求日期" width="180"></el-table-column>
      <el-table-column prop="adstatusn" label="装配状态" width="80"></el-table-column>
      <el-table-column prop="apcloseflag" label="装配结案" width="80"></el-table-column>
      <el-table-column prop="apwhqty" label="已入库数" width="80"></el-table-column>
      <el-table-column prop="apunwhqty" label="未入库数" width="80"></el-table-column>
      <el-table-column
        prop="apremarks"
        label="装配计划备注"
        width="150"
        :show-overflow-tooltip="true"
      ></el-table-column>
      <el-table-column prop="grohmname" label="巴组管理" width="80"></el-table-column>
      <el-table-column prop="groname" label="指派巴组" fixed="right" width="150">
        <template v-slot="scope">
          <el-select v-model="scope.row.grono" @change="addassigngro(scope.row)">
            <el-option v-for="item in option" :key="item.grono" :label="item.groname" :value="item.grono"></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="area" label="区域" fixed="right" width="120">
        <template v-slot="scope">
          <el-select v-model="scope.row.area" @change="addassigngro(scope.row)">
            <el-option v-for="item in areaOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      ref="paginationRef"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="[5, 10, 20, 40]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="totalData"
    ></el-pagination>
  </div>
</template>
<script>
import service from "@/router/request";
import { debounce } from "@/utils/utils";
export default {
  name: "plancheck",
  data() {
    const currentDate = new Date();
    const end = new Date(currentDate); // 设置当前日期为 end

    // 将 end 的月份减去 6
    end.setMonth(end.getMonth() - 12);

    return {
      start: end, // 设置 start 为 end 的前 6 个月
      end: currentDate, // 设置当前日期为 end
      options: [
        { value: "已审核", label: "已审核" },
        { value: "未审核", label: "未审核" },
        { value: "有资料", label: "有资料" },
        { value: "无资料", label: "无资料" },
        { value: "已派单", label: "已派单" },
        { value: "未派单", label: "未派单" },
        { value: "全部", label: "全部" },
      ],
      value: "全部",
      tableData: [],
      totalData: 0,
      currentPage: 1, //初始页
      pageSize: 10, //    每页的数据
      mokey: "",
      option: [],
      maxHeight: 0,
      MMOList: [],
      areaOption: [
        { value: "A", label: "A区" },
        { value: "B", label: "B区" },
        { value: "C", label: "C区" },
        { value: "D", label: "D区" },
        { value: "E", label: "E区" },
        { value: "F", label: "F区" },
        { value: "G", label: "G区" },
        { value: "H", label: "H区" },
      ],
    };
  },

  computed: {
    // 根据当前页码和每页显示条数，计算出当前页要显示的数据
    currentPageData() {
      const startIndex = (this.currentPage - 1) * this.pageSize;
      const endIndex = this.currentPage * this.pageSize;
      if (startIndex === endIndex) {
        return [this.tableData[startIndex]];
      }

      return this.tableData.slice(startIndex, endIndex);
    },
  },

  mounted() {
    this.getBuzu();
    this.selectplan();
    this.computeMaxHeight();
    this.getMMOList();
  },

  methods: {
    handleSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1;
    },

    handleCurrentChange(page) {
      this.currentPage = page;
    },

    search() {
      this.currentPage = 1;
      this.selectplan();
    },

    //查询计划
    selectplan() {
      const loading = this.$loading({
        lock: true,
        text: "加载数据中...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      service
        .get("/PlanCheck/GetPlanItems", {
          params: {
            type: this.value,
            startdate: this.start,
            enddate: this.end,
            value: this.mokey,
          },
        })
        .then((res) => {
          if (res.status == "succeed") {
            this.tableData = res.data;
            this.totalData = res.data.length;
          }
        })
        .finally(() => {
          loading.close(); // 关闭加载动画
        });
    },

    getBuzu() {
      service.get("/Bazu/bazulist").then((res) => {
        if (res.status == "succeed") {
          this.option = res.data;
        }
      });
    },

    addassigngro(row) {
      service
        .post("/PlanCheck/addassigngro", {
          //分配巴组编号
          tasno: row.grono,
          //机器编码
          tagno: row.prono,
          mmono: row.mmono,
          deadline: "2099-01-10T02:22:13.565Z",
          tascontents: "",
          tasremarks: "",
          pwhours: 0,
          pwages: 0,
          awhours: 0,
          awages: 0,
          tasflag: 0,
          comprate: 0,
          area: row.area,
        })
        .then((res) => {
          if (res.status == "succeed") {
            // 表示成功保存
            this.$message.success("保存成功");
            // this.selectplan();
          } else {
            this.$message.error("保存失败"); // 弹出错误的信息
          }
        });
    },

    computeMaxHeight() {
      let mainEl = document.getElementById("main");
      this.maxHeight =
        mainEl.clientHeight -
        this.$refs.searchBarRef.clientHeight -
        this.$refs.paginationRef.$el.clientHeight -
        20 -
        40;
    },

    getMMOList() {
      service.get("/Chanpin/Getmmodetails").then((res) => {
        if (res.status == "succeed") {
          this.MMOList = res.data;
        }
      });
    },

    mmoChange(row) {
      if (!row.grono) this.$message.warning("请指派巴组");
      this.addassigngro(row);
    },
  },
};
</script>

<style>
.footer {
  z-index: 500;
  position: fixed;
  bottom: 0;
  width: 100%;
  --footer-height: 5px;
  line-height: var(--footer-height);
  color: #fff;
}
</style>
