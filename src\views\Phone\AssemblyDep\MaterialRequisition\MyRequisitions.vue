<template>
  <div class="container">
    <TopNavigationBar title="我的领用单"></TopNavigationBar>
    <div class="content">
      <!-- 筛选区域 -->
      <div class="filter-section">
        <el-select v-model="filterStatus" placeholder="状态" size="small" @change="loadRequisitions">
          <el-option label="全部" value=""></el-option>
          <el-option label="待审核" :value="1"></el-option>
          <el-option label="已通过" :value="2"></el-option>
          <el-option label="已拒绝" :value="3"></el-option>
          <el-option label="已发放" :value="4"></el-option>
        </el-select>
      </div>

      <!-- 列表区域 -->
      <div class="list-container">
        <div v-if="loading" class="loading-container">
          <el-skeleton :loading="loading" :count="3" animated></el-skeleton>
        </div>

        <div v-else-if="requisitionList.length === 0" class="empty-state">
          <div class="empty-icon">📋</div>
          <p>暂无领用记录</p>
          <el-button type="primary" size="small" @click="$router.push('/createRequisition')"> 创建领用单 </el-button>
        </div>

        <div v-else class="requisition-items">
          <div
            v-for="item in requisitionList"
            :key="item.id"
            class="requisition-item"
            :class="getStatusClass(item.status)"
            @click="viewDetail(item)"
          >
            <div class="item-header">
              <div class="header-left">
                <h4 class="material-name">{{ item.materialName }}</h4>
                <span class="quantity">{{ item.quantity }}{{ item.unit }}</span>
              </div>
              <div class="header-right">
                <span class="status-badge" :class="getStatusClass(item.status)">
                  {{ getStatusText(item.status) }}
                </span>
              </div>
            </div>

            <div class="item-content">
              <p class="purpose">{{ item.purpose }}</p>
            </div>

            <div class="item-footer">
              <div class="time-info">
                <span>申请时间: {{ formatDateTime(item.createTime) }}</span>
                <span v-if="item.expectedUseDate">预计使用: {{ item.expectedUseDate }}</span>
              </div>
              <div class="actions">
                <el-button type="text" size="mini" @click.stop="viewDetail(item)"> 查看详情 </el-button>
                <el-button v-if="item.status === 1" type="text" size="mini" @click.stop="editRequisition(item)">
                  编辑
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详情对话框 -->
    <el-dialog title="领用单详情" :visible.sync="detailDialogVisible" width="90%" :close-on-click-modal="true">
      <div v-if="selectedItem" class="detail-content">
        <div class="detail-row">
          <label>领用类型:</label>
          <span class="type-badge" :class="getTypeClass(selectedItem.requisitionType)">
            {{ getTypeText(selectedItem.requisitionType) }}
          </span>
        </div>
        <div class="detail-row">
          <label>物品名称:</label>
          <span>{{ selectedItem.materialName }}</span>
        </div>
        <div class="detail-row">
          <label>数量:</label>
          <span>{{ selectedItem.quantity }}{{ selectedItem.unit }}</span>
        </div>
        <div class="detail-row">
          <label>用途说明:</label>
          <span>{{ selectedItem.purpose }}</span>
        </div>
        <div class="detail-row">
          <label>状态:</label>
          <span class="status-badge" :class="getStatusClass(selectedItem.status)">
            {{ getStatusText(selectedItem.status) }}
          </span>
        </div>

        <!-- 以旧换新时显示旧物品信息 -->
        <div v-if="selectedItem.requisitionType === 2" class="old-material-info">
          <div class="detail-row">
            <label>旧物品状态:</label>
            <span>{{ getOldMaterialConditionText(selectedItem.oldMaterialCondition) }}</span>
          </div>
          <div class="detail-row">
            <label>旧物品描述:</label>
            <span>{{ selectedItem.oldMaterialDescription }}</span>
          </div>
        </div>
        <div v-if="selectedItem.remark" class="detail-row">
          <label>备注:</label>
          <span>{{ selectedItem.remark }}</span>
        </div>
        <div v-if="selectedItem.attachments" class="detail-row">
          <label>附件:</label>
          <div class="attachments">
            <div
              v-for="(file, index) in getAttachmentList(selectedItem.attachments)"
              :key="index"
              class="attachment-item"
            >
              <a :href="file.url" target="_blank" class="attachment-link">
                <i class="el-icon-document"></i>
                {{ file.name }}
              </a>
            </div>
          </div>
        </div>
        <div class="detail-row">
          <label>申请时间:</label>
          <span>{{ formatDateTime(selectedItem.createTime) }}</span>
        </div>
        <div v-if="selectedItem.approveTime" class="detail-row">
          <label>审核时间:</label>
          <span>{{ formatDateTime(selectedItem.approveTime) }}</span>
        </div>
        <div v-if="selectedItem.approveRemark" class="detail-row">
          <label>审核意见:</label>
          <span>{{ selectedItem.approveRemark }}</span>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TopNavigationBar from "@/components/XTopNavigationBar.vue";
import service from "@/router/request";
import { MessageUtil } from "@/utils/utils";

export default {
  components: { TopNavigationBar },
  name: "MyRequisitions",
  data() {
    return {
      loading: false,
      filterStatus: "",
      requisitionList: [],
      detailDialogVisible: false,
      selectedItem: null,
    };
  },

  mounted() {
    this.loadRequisitions();
  },

  methods: {
    // 加载领用单列表
    async loadRequisitions() {
      this.loading = true;
      try {
        const params = {};
        if (this.filterStatus) params.status = this.filterStatus;

        const response = await service.get("/MaterialRequisition/GetMyRequisitions", { params });
        if (response.status === "succeed") {
          this.requisitionList = response.data || [];
        } else {
          MessageUtil.error("获取领用单列表失败");
        }
      } catch (error) {
        console.error("获取领用单列表失败:", error);
        MessageUtil.error("获取领用单列表失败");
      } finally {
        this.loading = false;
      }
    },

    // 查看详情
    viewDetail(item) {
      this.selectedItem = item;
      this.detailDialogVisible = true;
    },

    // 编辑领用单
    editRequisition(item) {
      this.$router.push(`/editRequisition/${item.id}`);
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        1: "待审核",
        2: "已通过",
        3: "已拒绝",
        4: "已发放",
      };
      return statusMap[status] || status;
    },

    // 获取状态样式
    getStatusClass(status) {
      const statusClassMap = {
        1: "status-pending",
        2: "status-approved",
        3: "status-rejected",
        4: "status-distributed",
      };
      return statusClassMap[status] || `status-${status}`;
    },

    // 获取领用类型文本
    getTypeText(type) {
      const typeMap = {
        1: "新领",
        2: "以旧换新",
      };
      return typeMap[type] || "未知";
    },

    // 获取领用类型样式
    getTypeClass(type) {
      return `type-${type}`;
    },

    // 获取旧物品状态文本
    getOldMaterialConditionText(condition) {
      const conditionMap = {
        1: "损坏",
        2: "磨损",
        3: "过期",
        0: "其他",
      };
      return conditionMap[condition] || condition;
    },

    // 格式化日期时间
    formatDateTime(dateString) {
      if (!dateString) return "";
      const date = new Date(dateString);
      return date.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
      });
    },

    // 解析附件列表
    getAttachmentList(attachmentsString) {
      if (!attachmentsString) return [];
      try {
        return JSON.parse(attachmentsString);
      } catch (error) {
        return [];
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.content {
  flex: 1;
  padding: 16px;
}

/* 筛选区域 */
.filter-section {
  background: white;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 12px;

  .el-select {
    flex: 1;
  }
}

/* 列表容器 */
.list-container {
  .loading-container {
    background: white;
    padding: 16px;
    border-radius: 8px;
  }

  .empty-state {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 8px;

    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }

    p {
      color: #909399;
      font-size: 16px;
      margin: 0 0 20px 0;
    }
  }
}

/* 领用单项样式 */
.requisition-items {
  .requisition-item {
    background: white;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #e4e7ed;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &.status-pending {
      border-left-color: #e6a23c;
    }

    &.status-approved {
      border-left-color: #67c23a;
    }

    &.status-rejected {
      border-left-color: #f56c6c;
    }

    &.status-completed {
      border-left-color: #909399;
    }
  }
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;

  .header-left {
    flex: 1;
    display: flex;

    .material-name {
      margin: 0 0 4px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .quantity {
      font-size: 14px;
      color: #606266;
      background-color: #f0f2f5;
      padding: 2px 8px;
      border-radius: 12px;
      margin-left: 10px;
    }
  }

  .header-right {
    display: flex;
    flex-direction: column;
    gap: 4px;
    align-items: flex-end;
  }
}

/* 状态标签 */
.status-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;

  &.status-pending {
    background-color: rgba(230, 162, 60, 0.1);
    color: #e6a23c;
  }

  &.status-approved {
    background-color: rgba(103, 194, 58, 0.1);
    color: #67c23a;
  }

  &.status-rejected {
    background-color: rgba(245, 108, 108, 0.1);
    color: #f56c6c;
  }

  &.status-completed {
    background-color: rgba(144, 147, 153, 0.1);
    color: #909399;
  }
}

/* 领用类型标签 */
.type-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;

  &.type-1 {
    background-color: rgba(64, 158, 255, 0.1);
    color: #409eff;
  }

  &.type-2 {
    background-color: rgba(230, 162, 60, 0.1);
    color: #e6a23c;
  }
}

/* 旧物品信息区域 */
.old-material-info {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #ebeef5;
}

.item-content {
  margin-bottom: 12px;

  .purpose {
    color: #606266;
    line-height: 1.5;
    margin: 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #909399;

  .time-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .actions {
    display: flex;
    gap: 8px;
  }
}

/* 详情对话框 */
.detail-content {
  .detail-row {
    display: flex;
    margin-bottom: 12px;
    align-items: flex-start;

    label {
      min-width: 80px;
      font-weight: 600;
      color: #606266;
      margin-right: 12px;
    }

    span {
      flex: 1;
      color: #303133;
    }
  }

  .attachments {
    .attachment-item {
      margin-bottom: 8px;

      .attachment-link {
        color: #409eff;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 4px;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

.dialog-footer {
  text-align: center;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .content {
    padding: 12px;
  }

  .filter-section {
    padding: 12px;
    flex-direction: column;
    gap: 8px;
  }

  .requisition-item {
    padding: 12px;
  }

  .item-header {
    // flex-direction: column;
    align-items: flex-start;
    gap: 8px;

    .header-right {
      align-items: flex-start;
      flex-direction: row;
      gap: 8px;
    }
  }

  .item-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;

    .actions {
      align-self: flex-end;
    }
  }
}
</style>
