<template>
  <div>
    <div class="mb-2" style="text-align: center; position: relative">
      <el-button icon="el-icon-plus" type="success" circle @click="memberDialogVisible = true"></el-button>
      <el-button type="danger" icon="el-icon-minus" circle @click="deleteTaskSelected"></el-button>
      <el-button @click="saveTask" type="primary" style="position: absolute; right: 0">保存</el-button>
    </div>
    <el-table
      ref="taskTable"
      :data="tableData"
      border
      @selection-change="taskSelection = $event"
      @row-click="$refs.taskTable.toggleRowSelection($event)"
    >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column v-for="config of tableConfig" :key="config.prop" v-bind="config">
        <template v-if="config.type == 'input'" v-slot="scope">
          <el-input
            v-model="scope.row[config.prop]"
            @input="computeWage(scope.row)"
            @click.native.stop
            v-bind="config.attr"
          ></el-input>
        </template>
        <template v-else-if="config.type == 'date'" v-slot="scope">
          <el-date-picker
            v-model="scope.row[config.prop]"
            type="month"
            placeholder="选择日期"
            @click.native.stop
            style="width: 160px"
            @change="config.attr.change($event, scope.row)"
            value-format="yyyy-MM"
          ></el-date-picker>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog :visible.sync="memberDialogVisible" title="成员">
      <div class="mb-2">
        <el-select v-model="memberSearchKey" @change="searchMember" style="width: 100px" class="mr-1">
          <el-option v-for="item of memberSearchKeys" :key="item.value" v-bind="item"></el-option>
        </el-select>
        <el-input
          v-model="memberSearchValue"
          placeholder="请输入搜索值"
          clearable
          @clear="searchMember"
          style="width: auto"
          class="mr-1"
        ></el-input>
        <el-button type="primary" @click="searchMember">搜索</el-button>
      </div>
      <el-table
        ref="memberTable"
        :data="memberListFilter"
        @selection-change="memberSelection = $event"
        @row-click="$refs.memberTable.toggleRowSelection($event)"
        max-height="500"
        :row-key="(row) => row.loginid"
        border
      >
        <el-table-column type="selection" width="40" :reserve-selection="true"></el-table-column>
        <el-table-column v-for="config of memberTableConfig" :key="config.prop" v-bind="config"></el-table-column>
      </el-table>
      <div slot="footer">
        <el-button
          @click="
            memberDialogVisible = false;
            $refs.memberTable.clearSelection();
          "
          >取 消</el-button
        >
        <el-button type="primary" @click="addTask">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import service from "@/router/request";

export default {
  data() {
    return {
      tableData: [],
      tableConfig: [
        { label: "姓名", prop: "lastname" },
        { label: "工号", prop: "loginid" },
        { label: "所属巴组", prop: "departmentname" },
        { label: "绩效金额", prop: "basic_performance" },
        { label: "结算月份", prop: "acc_month", "min-width": 160, type: "date", attr: { change: () => {} } },
        { label: "工作天数", prop: "work_days", type: "input", attr: { type: "number" } },
        { label: "考核天数", prop: "assessment_days", type: "input", attr: { type: "number" } },
        { label: "考核分数", prop: "assessment_grade", type: "input", attr: { type: "number", max: 100, min: 0 } },
        { label: "应得金额", prop: "performance_wage" },
        { label: "备注", prop: "remarks", type: "input", attr: { type: "textarea", autosize: true } },
      ],
      taskSelection: [],
      memberDialogVisible: false,
      memberList: [],
      memberListFilter: [],
      memberTableConfig: [
        { label: "姓名", prop: "lastname" },
        { label: "工号", prop: "loginid" },
        { label: "所属巴组", prop: "groname" },
        { label: "所属小队", prop: "tmename" },
        // { label: "绩效金额", prop: "basic_performance" },
      ],
      memberSearchKey: "lastname",
      memberSearchValue: "",
      memberSearchKeys: [
        { label: "姓名", value: "lastname" },
        { label: "工号", value: "loginid" },
        { label: "部门", value: "groname" },
        { label: "小队", value: "tmename" },
      ],
      memberSelection: [],
    };
  },

  mounted() {
    this.tableConfig.find((item) => item.prop == "acc_month").attr.change = this.monthChange;
    this.getMember();
  },

  methods: {
    getMember() {
      service.get("/Public/Getmember1").then((res) => {
        if (res.status == "succeed") {
          this.memberListFilter = this.memberList = res.data;
        }
      });
    },

    searchMember() {
      this.memberListFilter = this.memberList.filter((item) =>
        item[this.memberSearchKey].includes(this.memberSearchValue)
      );
    },

    addTask() {
      let month = this.$moment().subtract(1, "months").format("yyyy-MM");

      this.memberSelection.forEach(async (item) => {
        const res = await service.get("/DailyReport/getHourlyWage", { params: { loginId: item.loginid, month } });
        if (!res.data) return this.$message.warning("绩效金额为0请先配置基础绩效");
        this.tableData.push({
          ...item,
          acc_month: month,
          work_days: this.$moment(month, "yyyy-MM").daysInMonth(),
          assessment_days: 0,
          assessment_grade: 100,
          performance_wage: 0,
          remarks: "",
          basic_performance: res.data.basicPerformance,
        });
      });
      this.memberDialogVisible = false;
      this.$refs.memberTable.clearSelection();
    },

    computeWage(row) {
      row.performance_wage = row.basic_performance
        ? ((row.basic_performance / row.work_days) * row.assessment_days * row.assessment_grade * 0.01).toFixed(2)
        : 0;
    },

    deleteTaskSelected() {
      if (this.taskSelection.length == 0) return this.$message.warning("请选中任务");
      this.$confirm("确定要删除选中任务吗?", "提示", { type: "warning" })
        .then(() => {
          this.taskSelection.forEach((item) =>
            this.tableData.splice(
              this.tableData.findIndex((row) => row.loginid == item.loginid),
              1
            )
          );
        })
        .catch(() => {});
    },

    saveTask() {
      if (this.tableData.some((item) => !item.basic_performance)) return this.$message.warning("绩效金额不能为0");
      if (this.tableData.some((item) => !item.assessment_days)) return this.$message.warning("考核天数不能为0");
      service.post("/Performance/addPerformanceTask", this.tableData).then((res) => {
        if (res.status == "succeed") {
          this.$message.success("保存成功！");
        } else {
          this.$message.error(res.err);
        }
      });
    },

    monthChange(month, row) {
      (row.work_days = this.$moment(month, "yyyy-MM").daysInMonth()),
        service
          .get("/DailyReport/getHourlyWage", {
            params: { loginId: row.loginid, month: this.$moment(month).format("yyyy-MM") },
          })
          .then((res) => {
            row.basic_performance = res.data.basicPerformance;
            this.computeWage(row);
          });
    },
  },
};
</script>
