import Vue from "vue";
import Vuex from "vuex";
import App from "./App.vue";
import router from "./router";
import store from "./store.js";
import "core-js/stable";

import ElementUI from "element-ui";
import "element-ui/lib/theme-chalk/index.css";
import moment from "moment";
import service from "@/router/request";

import "@/assets/css/common.css";
import "@/assets/css/theme.css";
import "@/assets/css/tailwind.css";
import "font-awesome/css/font-awesome.min.css";

Vue.use(ElementUI);

Vue.config.productionTip = false;

moment.locale("zh-cn");
Vue.prototype.$moment = moment;
Vue.prototype.$service = service;

Vue.use(Vuex);

new Vue({
  store,
  router,
  render: (h) => h(App),
}).$mount("#app");
