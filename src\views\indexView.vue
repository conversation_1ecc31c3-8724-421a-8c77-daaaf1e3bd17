<template>
  <div>
    <el-container style="height: 95vh">
      <el-aside ref="asideRef" width="250px">
        <XMenu :menuData="menu_data" :activeMenu="activeMenuIndex" @menu-click="selectMenu" />
        <i ref="dragIconRef" class="el-icon-d-caret"></i>
      </el-aside>

      <el-container>
        <el-header class="main-header">
          <div class="header-content">
            <el-tabs v-model="editableTabsValue" closable @tab-remove="removeTab" @tab-click="handleTabClick">
              <el-tab-pane :key="item.name" v-for="item in editableTabs" :label="item.title" :name="item.name">
              </el-tab-pane>
            </el-tabs>

            <el-dropdown class="tab-dropdown" @command="handleCommand" style="margin: 0 10px">
              <span class="el-dropdown-link">
                <i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="closeAll">关闭所有页面</el-dropdown-item>
                <el-dropdown-item command="closeOther">关闭其他页面</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>

            <!-- <el-button type="success" @click="download" class="mr-2">操作文档</el-button> -->

            <el-dropdown @command="handleCommand">
              <div class="user-panel">
                <el-avatar :size="36" :src="headPortrait" class="user-avatar" />
                <div class="user-info">
                  <span class="user-name">{{ $store.state.user.name }}</span>
                  <span class="user-role">{{ $store.state.user.role_name }}</span>
                </div>
                <el-icon class="dropdown-icon"><arrow-down /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="toModifyPassword" class="mb-1">修改密码 </el-dropdown-item>
                  <el-dropdown-item command="exit">退出</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>

        <el-main id="main">
          <keep-alive :include="keepAlive">
            <router-view></router-view>
          </keep-alive>
        </el-main>
      </el-container>
    </el-container>

    <!--  -->
    <el-dialog title="修改密码" :visible="modifyPWD" @close="modifyPWD = false">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px">
        <div class="nav">
          <el-form-item label="原密码" prop="oldpassword">
            <el-input type="password" v-model="ruleForm.oldpassword" auto-complete="off" show-password></el-input>
          </el-form-item>
          <el-form-item label="新密码" prop="newpassword">
            <el-input type="password" v-model="ruleForm.newpassword" autocomplete="off" show-password></el-input>
          </el-form-item>
          <el-form-item label="确认新密码" prop="confirmPassword">
            <el-input type="password" v-model="ruleForm.confirmPassword" autocomplete="off" show-password></el-input>
          </el-form-item>
          <el-button type="primary" @click="changepassword">提交</el-button>
          <el-button type="success" @click="reset('ruleForm')">重置</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { mapMutations } from "vuex";
import router from "@/router";
import service from "@/router/request";
import headPortrait from "@/assets/images/head_portrait.png";
import XMenu from "@/components/XMenu.vue";

export default {
  name: "index",
  components: {
    XMenu,
  },
  data() {
    var validatePass = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入新密码"));
      } else if (value.toString().length < 6 || value.toString().length > 18) {
        callback(new Error("密码长度为6-18位"));
      } else {
        callback();
      }
    };
    var validatePass2 = (rules, value, callback) => {
      if (value === "") {
        callback(new Error("请再次输入密码"));
      } else if (value !== this.ruleForm.newpassword) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    return {
      headPortrait,
      keepAlive: [
        "bazu",
        "chanpin",
        "shenhe",
        "xinchou",
        "plancheck",
        "privilegemanage",
        "jijian",
        // "jishi",
        // "linshi",
        "plansearch",
        "monthlyassess",
        "personalaccount",
        "teamaccount",
        "monthlysettle",
        "taskDetails",
        "groupMembers",
        "IndividualHoursSummary",

        "ReportForDay",
        "DailyOverview",

        "MachineTaskOverview",
        "ProNoIndividualHoursSummary",
      ],
      //改为后端返回树
      menu_data: [],

      modifyPWD: false,
      ruleForm: {
        oldpassword: "",
        newpassword: "",
        confirmPassword: "",
      },

      //修改密码规则
      rules: {
        oldpassword: [{ required: true, message: "请输入原密码", trigger: "blur" }],
        newpassword: [{ required: true, trigger: "blur", validator: validatePass }],
        confirmPassword: [{ required: true, trigger: "blur", validator: validatePass2 }],
      },

      activeMenuIndex: "/default",
    };
  },

  created() {
    service.get(`/menu/getMenuTree/${this.$store.state.user.loginId}`).then((res) => {
      if (res.status == "succeed") {
        this.menu_data = res.data || [];
        this.menu_data.unshift({
          id: 0,
          name: "首页",
          path: "/default",
          icon: "el-icon-s-home",
        });
      } else {
        this.$message.error("菜单数据获取失败");
      }
    });
  },

  mounted() {
    this.$store.state.indexViewInstance = this;
    this.activeMenu(this.editableTabsValue);
    this.menuResize(this.$refs.dragIconRef, this.$refs.asideRef.$el);
  },

  computed: {
    editableTabsValue: {
      get() {
        return this.$store.state.editableTabsValue;
      },
      set(val) {
        this.activeMenu(val);
        this.$store.state.editableTabsValue = val;
      },
    },
    editableTabs: {
      get() {
        return this.$store.state.editableTabs;
      },
      set(val) {
        this.$store.state.editableTabs = val;
      },
    },
  },

  methods: {
    activeMenu(val) {
      this.activeMenuIndex = this.editableTabs.find((item) => item.name == val)?.index || "/default";
    },
    download() {
      let a = document.createElement("a");
      a.download = "操作手册.pdf";
      a.href = "static/任务管理系统.pdf";
      a.click();
      a.remove();
    },
    //将方法映射到当前组件
    ...mapMutations(["addEditableTabs"]),
    //调用方法，增加标签
    selectMenu(item = this.editableTabs[0]) {
      this.addEditableTabs(item);
    },
    //删除标签
    removeTab(targetName) {
      let tabs = this.editableTabs;
      if (targetName === "首页") return;
      if (this.editableTabsValue === targetName) {
        tabs.forEach((tab, index) => {
          if (tab.name === targetName) {
            let nextTab = tabs[index + 1] || tabs[index - 1];
            if (nextTab) {
              this.editableTabsValue = nextTab.name;
              for (let i = 0; i < tabs.length; i++) {
                if (tabs[i].name == this.editableTabsValue) this.$router.push({ path: tabs[i].index });
              }
            }
          }
        });
      }
      //跳转到前一个页面
      // this.editableTabsValue = activeName;
      //删除当前页面
      this.editableTabs = tabs.filter((tab) => tab.name !== targetName);
      //寻找上一个标签的路由
    },
    //标签点击路由跳转
    handleTabClick(target) {
      if (this.$route.name !== target.name) {
        this.$router.push({ name: target.name });
      }
    },
    // 实现关闭页面的逻辑
    handleCommand(command) {
      switch (command) {
        case "closeAll":
          this.closeAllTabs();
          break;
        case "closeOther":
          this.closeOtherTabs();
          break;
        case "toModifyPassword":
          this.toModifyPassword();
          break;
        case "exit":
          this.exit();
          break;
      }
    },
    //关闭所有标签页
    closeAllTabs() {
      const homeTab = this.editableTabs.find((tab) => tab.name === "首页"); // 获取首页标签页
      this.editableTabs = [homeTab]; // 只保留首页标签
      this.editableTabsValue = "首页"; // 设置当前标签页为首页
      if (this.$route.path != "/default") router.push("/default");
    },
    //关闭除首页外的其他标签页
    closeOtherTabs() {
      const homeTab = this.editableTabs.find((tab) => tab.name === "首页"); // 获取首页标签页
      const retainTab = this.editableTabs.filter((tab) => tab.name === this.editableTabsValue);
      if (retainTab.length === 1 && retainTab[0].name !== "首页") {
        this.editableTabs = [homeTab, retainTab[0]];
      } else return;
    },
    //密码重置
    reset(ruleForm) {
      this.$refs[ruleForm].resetFields();
    },
    //修改密码
    changepassword() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          console.log(this.ruleForm);
          service.post("/index/update_password", this.ruleForm).then((res) => {
            if (res.status == "succeed") {
              this.$message.success("修改成功,3秒后跳转到登录页！");
              setTimeout(() => {
                // 注销用户
                localStorage.removeItem("user");
                // 立刻调用跳转
                this.modifyPWD = false;
                this.$router.push("/login");
              }, 3000);
            } else {
              this.$message({
                message: res.err,
                type: "error",
                duration: "2000",
              });
              return false;
            }
          });
        }
      });
    },

    toModifyPassword() {
      this.modifyPWD = true;
    },

    exit() {
      this.$store.dispatch("logout");
    },

    // 拖拽宽度
    menuResize(dragElement, resizeElement) {
      dragElement.addEventListener("mousedown", (e) => {
        let startX = e.pageX;
        let width = resizeElement.offsetWidth;

        let move = (e) => {
          let moveX = e.pageX - startX;
          resizeElement.style.width = width + moveX + "px";
        };
        document.addEventListener("mousemove", move);
        document.addEventListener(
          "mouseup",
          (e) => {
            document.removeEventListener("mousemove", move);
          },
          { once: true }
        );
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.main-header {
  height: 64px;
  // background: white;
  border-bottom: 1px solid $border-color;
  padding: 0 24px;
  .header-content {
    height: 100%;
    display: flex;
    align-items: end;
    justify-content: space-between;

    // 顶部历史导航栏
    ::v-deep .el-tabs {
      flex: 1;
      overflow-x: auto;
      overflow-y: hidden;

      // 去除默认边框和阴影
      &__header,
      &__nav-wrap::after {
        border: none !important;
        box-shadow: none;
        margin: 0;
      }

      // 标签栏容器
      &__nav-scroll {
        // padding: 0 20px;
        background: #f8f9fa; // 浅灰背景（可修改为深色模式变量）
      }

      // 内容区域
      &__content {
        // padding: 24px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05); // 柔和投影
      }
    }

    ::v-deep .el-tabs__item {
      // 基础参数
      height: 40px;
      line-height: 40px;
      padding: 0 24px !important;
      font-size: 14px;
      color: #666;
      transition: all 0.2s ease;

      // 悬浮效果
      &:hover {
        color: #333;
        background: rgba(0, 0, 0, 0.03);
      }

      // 激活状态
      &.is-active {
        color: #2c3e50; // 深蓝灰主色
        font-weight: 500;
        &::after {
          content: "";
          position: absolute;
          bottom: 0;
          left: 50%;
          width: 40%;
          height: 2px;
          background: #3498db; // 激活指示条
          transform: translateX(-50%);
        }
      }

      // 关闭按钮样式
      .el-icon-close {
        margin-left: 8px;
        &:hover {
          background: rgba(0, 0, 0, 0.08);
          border-radius: 50%;
        }
      }
    }

    .tab-dropdown {
      height: 50%;
    }

    .user-panel {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      border-radius: 8px;
      transition: background 0.2s;
      cursor: pointer;

      &:hover {
        background: $light-bg;

        .dropdown-icon {
          transform: rotate(180deg);
        }
      }

      .user-avatar {
        margin-right: 12px;
      }

      .user-info {
        line-height: 1.4;
        .user-name {
          display: block;
          color: --dark-text;
          font-weight: 500;
        }
        .user-role {
          font-size: 12px;
          color: $medium-text;
        }
      }

      .dropdown-icon {
        margin-left: 8px;
        color: $medium-text;
        transition: transform 0.3s;
      }
    }
  }
}

/* 侧边导航栏 */
.el-aside {
  box-shadow: 4px 0 12px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: visible;
}

.el-menu-vertical-demo {
  overflow: auto;
  height: 100%;
}

.el-icon-d-caret {
  position: absolute;
  right: -8px;
  top: 50%;
  transform: translateY(-50%) rotate(90deg);

  background: rgb(102, 177, 255) !important;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 16px;
  height: 16px;
  color: #fff;
  padding: 4px;
}

.el-icon-d-caret:hover {
  cursor: w-resize;
}

a {
  text-decoration: none;
}

// .el-menu {
//   border: none;
// }

// .el-menu-item {
//   text-align: left;
// }

// .el-submenu {
//   text-align: left;
// }
</style>
