<template>
  <div>
    <el-dialog :visible.sync="dialogVisible" :title="form.id ? '培训会议' : '添加培训会议'" width="80%" @close="cancel">
      <el-form :model="form" ref="formRef" :rules="formRules" label-width="80px" label-position="top">
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" size="mini"></el-input>
        </el-form-item>
        <el-form-item label="主讲人" prop="speaker">
          <el-select
            v-model="form.speaker"
            filterable
            remote
            :remote-method="(query) => getMemberList(query, 1, 9999)"
            :loading="memberLoading"
            placeholder="请选择主讲人"
            size="mini"
          >
            <el-option
              v-for="member in memberList"
              :key="member.loginId"
              :label="member.name"
              :value="member.loginId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="参会人员" prop="participants">
          <GroupMultiSelector
            :options="groupMemberList"
            v-model="form.participants"
            ref="groupMultiSelectorRef"
          ></GroupMultiSelector>
        </el-form-item>
        <el-form-item label="时间" prop="planTime">
          <el-date-picker
            v-model="form.planTime"
            type="datetime"
            placeholder="选择时间"
            :picker-options="pickerOptions"
            size="mini"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="附件上传">
          <!--  -->
          <el-upload
            ref="uploadRef"
            action="#"
            :multiple="true"
            :file-list="form.attachments"
            :auto-upload="false"
            :before-remove="beforeRemove"
            :on-change="handleFileChange"
            :on-preview="handlePreview"
            size="mini"
          >
            <el-button slot="trigger" size="small" type="primary">选择文件</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <template
        #footer
        v-if="!form.id || form.creator == $store.state.user.loginId || form.speaker == $store.state.user.loginId"
      >
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="submitForm">{{ form.id ? "修改" : "添加" }}</el-button>
      </template>
    </el-dialog>
    <el-dialog :visible.sync="previewDialogVisible" title="文件预览" width="80%">
      <FilePreview
        :file-url="previewFileUrl"
        :file-name="previewFileName"
        @close="previewDialogVisible = false"
      ></FilePreview>
    </el-dialog>
  </div>
</template>

<script>
import service from "@/router/request";
import GroupMultiSelector from "@/components/GroupedMultiSelector.vue";
import FilePreview from "@/components/XFilePreview.vue";

export default {
  components: { GroupMultiSelector, FilePreview },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      form: {
        id: 0,
        title: "",
        speaker: "",
        participants: [],
        planTime: "",
        attachments: [],
      },
      formRules: {
        title: [{ required: true, message: "请填写标题", trigger: "blur" }],
        speaker: [{ required: true, message: "请选择主讲人", trigger: "change" }],
        participants: [{ required: true, message: "请选择参会人员", trigger: "change" }],
        planTime: [{ required: true, message: "请选择会议时间", trigger: "change" }],
      },
      memberList: [],
      memberLoading: false,
      groupMemberList: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7 * 5 * 365;
        },
      },
      previewDialogVisible: false,
      previewFileName: "",
      previewFileUrl: "",
    };
  },
  watch: {
    visible(newVal) {
      this.dialogVisible = newVal;
      if (newVal) {
        this.initForm();
      }
    },
  },
  created() {
    this.getMemberList();
    this.getGroupMemberList();
  },
  methods: {
    initForm() {
      if (this.initialData) {
        this.form = { ...this.initialData };
      } else {
        this.form = {
          id: 0,
          title: "",
          speaker: "",
          participants: [],
          planTime: "",
          attachments: [],
        };
      }
      this.$nextTick(() => {
        this.$refs.formRef.clearValidate();
      });
    },

    getMemberList(key, pageNum = 1, pageSize = 20) {
      if (!key) pageSize = 20;
      this.memberLoading = true;
      service.post("/Member/getMemberList", { key, pageNum, pageSize }).then((res) => {
        if (res.status == "succeed") {
          this.memberList = res.data;
          this.memberLoading = false;
        }
      });
    },

    getGroupMemberList() {
      service.get("/Member/getGroupMemberList").then((res) => {
        if (res.status == "succeed") {
          this.groupMemberList = res.data;
          this.groupMemberList = this.groupMemberList.map((item) => ({
            label: item.groupName,
            children: item.children.map((cItem) => ({ label: cItem.name, value: cItem.loginId })),
          }));
        }
      });
    },

    handlePreview(file) {
      this.previewFileUrl = file.url || URL.createObjectURL(file.raw);
      this.previewFileName = file.name;
      this.previewDialogVisible = true;
    },

    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`, "", { customClass: "phone-message-box" });
    },

    handleFileChange(file, fileList) {
      this.form.attachments = fileList;
    },

    async submitUpload() {
      // 筛选已上传和未上传的文件
      const uploadedFiles = this.form.attachments.filter((f) => f.url);
      const unuploadedFiles = this.form.attachments.filter((f) => !f.url);

      let domain = service.getUri().replace("/api", "");
      let urls = uploadedFiles.map((f) => f.url.replace(domain, ""));

      if (unuploadedFiles.length > 0) {
        const formData = new FormData();
        unuploadedFiles.forEach((file) => {
          formData.append("files", file.raw || file);
        });

        formData.append("type", "training");

        const res = await service.post("/Upload/UploadFile", formData);
        if (res.status === "succeed" && Array.isArray(res.data)) {
          urls = urls.concat(res.data);
        } else {
          throw new Exception(res.message);
        }
      }

      // 拼接成逗号隔开的字符串
      return urls.join(",");
    },

    async submitForm() {
      this.$refs.formRef.validate(async (valid) => {
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: "提交中.....",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          let attachments = "";
          try {
            attachments = await this.submitUpload();
          } catch (e) {
            return MessageUtil.error(`文件上传失败: ${e.message}`);
          }
          let result = {
            ...this.form,
            participants: this.form.participants.join(","),
            attachments,
          };

          if (result) {
            let url = result.id ? "/Training/updateTrainingMeeting" : "/Training/addTrainingMeeting";

            service
              .post(url, result)
              .then((res) => {
                if (res.status == "succeed") {
                  this.$emit("confirm");
                  MessageUtil.success("会议信息已更新");
                } else {
                  MessageUtil.error("会议信息更新失败");
                }
              })
              .finally(() => {
                loading.close();
              });
          }
          this.dialogVisible = false;
        } else {
          MessageUtil.error("请填写完整信息");
          return false;
        }
      });
    },

    cancel() {
      this.$refs.groupMultiSelectorRef.resetComponent();
      this.dialogVisible = false;
      this.$emit("update:visible", false);
    },
  },
};
</script>

<style lang="scss" scoped>
.el-dialog__body {
  padding: 20px;
}

.el-form-item {
  margin-bottom: 20px;
}

/* 手机端样式调整 */
@media (max-width: 767px) {
  .el-dialog {
    width: 90%;
    margin-top: 20vh !important;
  }

  :deep(.el-dialog__body) {
    padding: 20px 20px;
  }

  :deep(.el-form-item__content) {
    line-height: 1;
  }

  :deep(.el-form-item__label) {
    font-size: 14px;
    line-height: 1;
  }

  .el-input,
  .el-select,
  .el-date-picker,
  .el-checkbox {
    font-size: 14px;
  }

  .el-button {
    font-size: 14px;
  }
}
</style>
