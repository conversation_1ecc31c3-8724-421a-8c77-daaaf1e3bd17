<template>
  <div>
    <div ref="topBarRef" class="top-bar mb-2">
      <el-button @click="goBack" icon="el-icon-back" circle></el-button>
      <h1 style="flex: 1">机型内容修改</h1>
    </div>
    <el-descriptions ref="descriptionsRef" :column="4" border>
      <el-descriptions-item label="机型名称">{{
        rowData.mmoname
      }}</el-descriptions-item>
      <el-descriptions-item label="所属巴组">{{
        rowData.grono
      }}</el-descriptions-item>
      <el-descriptions-item label="机型型号">{{
        rowData.mmomodel
      }}</el-descriptions-item>
      <el-descriptions-item label="当前状态">
        {{
          rowData.mmosflag === 0
            ? "停用"
            : rowData.mmosflag === 1
            ? "整机未审"
            : rowData.mmosflag === 2
            ? "变更未审"
            : rowData.mmosflag === 3
            ? "新增未审"
            : rowData.mmosflag === 4
            ? "正常"
            : ""
        }}
      </el-descriptions-item>
      <el-descriptions-item label="录入姓名">{{
        rowData.createName
      }}</el-descriptions-item>
      <el-descriptions-item label="产品描述">{{
        rowData.mmoremarks
      }}</el-descriptions-item>
    </el-descriptions>
    <div class="mb-1 mt-2" style="display: flex">
      <el-tooltip content="暂无权限" :disabled="havePermissionRole([2, 8])">
        <el-button
          type="primary"
          plain
          @click="openAddDialog"
          :disabled="!havePermissionRole([2, 8])"
          >新增</el-button
        >
      </el-tooltip>
      <el-upload
        class="ml-1"
        action="action"
        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        :show-file-list="false"
        :on-change="onUploadChange"
        :auto-upload="false"
      >
        <el-button type="primary" plain :disabled="!havePermissionRole([2, 8])"
          >导入</el-button
        >
      </el-upload>
    </div>
    <el-table
      v-if="tableMaxHeight"
      :row-class-name="tableRowClassName"
      :data="tableData"
      border
      :header-cell-style="{ textAlign: 'center', backgroundColor: '#c5e3ef' }"
      :max-height="tableMaxHeight"
    >
      <el-table-column prop="mpaname" label="部位" align="center" width="140">
        <template v-slot="scope">
          <el-input v-model="scope.row.mpaname"></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="sasname" label="组件" align="center" width="140">
        <template v-slot="scope">
          <el-input
            v-model="scope.row.sasname"
            type="textarea"
            autosize
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="ssuname" label="子件" align="center" width="140">
        <template v-slot="scope">
          <el-input
            v-model="scope.row.ssuname"
            type="textarea"
            autosize
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="wprno" label="工序" align="center" width="100">
        <template v-slot="scope">
          <el-input v-model="scope.row.wprno"></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="manhours" label="工时" align="center" width="90">
        <template v-slot="scope">
          <el-input v-model="scope.row.manhours"></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="mhprice" label="工时价" align="center" width="90">
        <template v-slot="scope">
          <el-input v-model="scope.row.mhprice"></el-input>
        </template>
      </el-table-column>
      <el-table-column label="总工价" align="center" width="70">
        <template v-slot="scope">{{
          parseFloat(scope.row.manhours) * parseFloat(scope.row.mhprice)
        }}</template>
      </el-table-column>
      <el-table-column
        prop="swremarks"
        label="备注"
        align="center"
        min-width="250"
      >
        <template v-slot="scope">
          <el-input
            v-model="scope.row.swremarks"
            type="textarea"
            autosize
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column
        prop="swsflagstatus"
        label="状态"
        align="center"
        width="60"
      >
        <template v-slot="scope">
          <span
            :style="{
              color: scope.row.swsflagstatus == '审核' ? 'red' : 'green',
            }"
            >{{ scope.row.swsflagstatus }}</span
          >
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="150">
        <template v-slot:default="scope">
          <el-tooltip content="暂无权限" :disabled="havePermissionRole([2, 8])">
            <el-button
              @click="Updatemmodetails(scope.row)"
              type="primary"
              plain
              size="small"
              :disabled="!havePermissionRole([2, 8])"
              >保存</el-button
            >
          </el-tooltip>
          <el-tooltip content="暂无权限" :disabled="havePermissionRole([2, 8])">
            <el-button
              @click="shanchu(scope.row)"
              type="danger"
              plain
              size="small"
              :disabled="!havePermissionRole([2, 8])"
              >删除</el-button
            >
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <!--  -->
    <el-dialog
      title="信息"
      :visible.sync="fromVisible"
      width="80%"
      :close-on-click-modal="false"
      destroy-on-close
      @close="over"
    >
      <el-button @click="pushFormData">添加</el-button>
      <el-table :data="form" max-height="500" border>
        <el-table-column prop="mpaname" label="部位" align="center" width="140">
          <template v-slot="scope">
            <el-input v-model="scope.row.mpaname"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="sasname" label="组件" align="center" width="140">
          <template v-slot="scope">
            <el-input
              v-model="scope.row.sasname"
              type="textarea"
              autosize
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="ssuname" label="子件" align="center" width="140">
          <template v-slot="scope">
            <el-input
              v-model="scope.row.ssuname"
              type="textarea"
              autosize
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="wprno" label="工序" align="center" width="100">
          <template v-slot="scope">
            <el-input v-model="scope.row.wprno"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="manhours" label="工时" align="center" width="90">
          <template v-slot="scope">
            <el-input v-model="scope.row.manhours"></el-input>
          </template>
        </el-table-column>
        <el-table-column
          prop="mhprice"
          label="工时价"
          align="center"
          width="90"
        >
          <template v-slot="scope">
            <el-input v-model="scope.row.mhprice"></el-input>
          </template>
        </el-table-column>
        <el-table-column
          prop="swremarks"
          label="备注"
          align="center"
          min-width="250"
        >
          <template v-slot="scope">
            <el-input
              v-model="scope.row.swremarks"
              type="textarea"
              autosize
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right">
          <el-button
            slot-scope="scope"
            type="danger"
            @click="deleteFormData(scope.$index)"
            >删除</el-button
          >
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="over">取 消</el-button>
        <el-button type="primary" @click="savempadetail">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import service from "@/router/request";
import { havePermissionRole } from "@/utils/permission";
import { readExcelToJson } from "@/utils/utils";

export default {
  name: "ChanPin1",

  data() {
    return {
      // 表格数据
      rowData: [],
      tableData: [],
      fromVisible: false,
      form: [],
      rules: {
        mpaname: [
          { required: true, message: "请输入部位名称", trigger: "blur" },
        ],
        sasname: [{ required: true, message: "请输入组件", trigger: "blur" }],
        ssuname: [
          { required: true, message: "请输入子件名称", trigger: "blur" },
        ],
        wprno: [{ required: true, message: "请输入工序名称", trigger: "blur" }],
        mhprice: [{ required: true, message: "请输入工时", trigger: "blur" }],
        manhours: [
          { required: true, message: "请输入工时价", trigger: "blur" },
        ],
      },
      tableMaxHeight: 0,
    };
  },
  mounted() {
    this.rowData = JSON.parse(localStorage.getItem("rowData"));
    this.getdata();
    this.computeTableMaxHeight();
  },

  methods: {
    havePermissionRole,

    over() {
      this.fromVisible = false;
      this.form = [];
    },
    //产品部件信息
    getdata() {
      service
        .post("/Chanpin/Getmpadetail", { mmo_no: this.rowData.mmono })
        .then((res) => {
          if (res.status == "succeed") {
            this.tableData = res.data;
          }
        });
    },
    //实现表格双击可编辑
    // eslint-disable-next-line no-unused-vars
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex % 4 == 1) {
        return "warning-row";
      } else if (rowIndex % 4 == 3) {
        return "success-row";
      }
      return "";
    },
    //更新部件信息
    Updatemmodetails(row) {
      service
        .post(
          "/Chanpin/Updatempadetail",
          row
          // eslint-disable-next-line no-unused-vars
        )
        .then((res) => {
          if (res.status == "succeed") {
            // 处理响应结果
            this.$message.success("保存成功！");
            this.getdata();
          }
        })
        .catch((error) => {
          // 处理错误
          console.log(error);
          this.$message.error("保存失败，请重试！");
        });
    },
    //删除部件信息
    shanchu(row) {
      const cbano = row.cbano; // 获取部件ID
      // 弹出确认弹窗
      this.$confirm("确定要删除该部件吗?该部件数据将会消失！", "提示", {
        type: "warning",
      })
        .then(() => {
          // 用户点击了确定按钮，发送 DELETE 请求到后端接口
          service
            .post("/Chanpin/Deletempadetail", {
              // 将部件ID作为参数
              cbano: cbano,

              // eslint-disable-next-line no-unused-vars
            })
            .then((res) => {
              if (res.status == "succeed") {
                // 请求成功的处理逻辑
                // 根据后端返回的数据，做出相应操作
                console.log("删除部件成功");
                this.getdata();
              }
            })
            .catch((error) => {
              // 请求失败的处理逻辑
              // 输出错误信息或进行错误处理
              console.error("删除部件失败:", error);
            });
        })
        .catch(() => {
          // 用户点击了取消按钮
          console.log("取消删除部件操作");
        });
    },

    goBack() {
      this.$router.back();
    },

    //新增部件信息
    savempadetail() {
      // this.$refs.formRef.validate(valid => {
      // if (valid) {
      // 表单验证通过，执行保存逻辑
      // 执行保存操作
      // 可以在这里调用接口，将表单数据传递给后端进行保存
      service
        .post("/Chanpin/Addmpadetail", {
          mmono: this.rowData.mmono,
          mpas: this.form,
        })
        .then((res) => {
          if (res.status == "succeed") {
            // 表示成功保存
            this.over();
            this.getdata();
            this.$message.success(res.message || "添加成功");
          } else {
            this.$message.error("添加失败"); // 弹出错误的信息
          }
        });
      // }
      // });
    },

    computeTableMaxHeight() {
      let main = document.getElementById("main");
      this.tableMaxHeight =
        main.clientHeight -
        this.$refs.topBarRef.clientHeight -
        this.$refs.descriptionsRef.$el.clientHeight -
        20 -
        30 -
        40 -
        40;
    },

    openAddDialog() {
      this.pushFormData();
      this.fromVisible = true;
    },

    pushFormData() {
      this.form.push({
        mpaname: "",
        sasname: "",
        ssuname: "",
        wprno: "",
        manhours: "",
        mhprice: "",
        swremarks: "",
      });
    },

    deleteFormData(idx) {
      this.form.splice(idx, 1);
    },

    onUploadChange(file) {
      readExcelToJson(file).then((data) => {
        this.form = data.map((item) => ({
          mpaname: item["部位"] || "/",
          sasname: item["组件"] || "/",
          ssuname: item["子件"] || "/",
          wprno: item["工序"] || "/",
          manhours: item["工时"] || 0,
          mhprice: item["工时价"] || 0,
          swremarks: item["备注"] || "",
        }));
        this.fromVisible = true;
      });
    },
  },
};
</script>

<style scoped>
.top-bar {
  display: flex;
  align-items: center;
  text-align: center;
}

.top-bar h1 {
  margin: 0;
}
</style>