<template>
  <div class="container">
    <TopNavigationBar title="精益改善"></TopNavigationBar>
    <el-main class="main">
      <div class="grid-container">
        <div
          v-for="(option, index) in options"
          :key="index"
          class="grid-item"
          @click="navigateTo(option)"
          :class="{ disabled: option.permissionAuth && !hasPermission }"
        >
          <div class="icon">{{ option.icon || "🔗" }}</div>
          <div class="label">{{ option.label }}</div>
          <div v-if="option.permissionAuth && !hasPermission" class="lock-icon">🔒</div>
        </div>
      </div>
    </el-main>
  </div>
</template>

<script>
import TopNavigationBar from "@/components/XTopNavigationBar.vue";

export default {
  name: "Announcement",
  components: { TopNavigationBar },
  data() {
    return {
      options: [
        { label: "新增通告", icon: "💡", path: "/addAnnouncement" }, // 灯泡-创意/改善
        { label: "通告查看", icon: "🛡️", path: "" }, // 盾牌-指挥/作战
        { label: "统计", icon: "📝", path: "/myProposal" }, // 笔记-我的提案
      ],
    };
  },

  computed: {
    hasPermission() {
      // 直接访问 store 状态，确保响应式
      const memberRole = this.$store.state.user?.roleId || this.$store.state.user?.roleId || 0;
      return memberRole == 8 || memberRole == 1;
    },
  },

  methods: {
    navigateTo(option) {
      if (option.permissionAuth && !this.hasPermission) {
        return this.$message.warning({
          message: "暂无权限",
          customClass: "phone-message-tip",
        });
      }
      this.$router.push({ path: option.path });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding-bottom: env(safe-area-inset-bottom, 20px);
}

.main {
  margin: 0;
  // padding: 20px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.grid-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  padding: 16px;
  .grid-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 16px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #f9f9f9;
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    &.disabled {
      opacity: 0.6;
      cursor: not-allowed;

      &:hover {
        transform: none;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }

    .icon {
      font-size: 24px;
      margin-bottom: 8px;
    }

    .label {
      font-size: 14px;
      color: #333;
      text-align: center;
    }
    .lock-icon {
      position: absolute;
      top: 4px;
      right: 4px;
      font-size: 12px;
      opacity: 0.7;
    }
  }
}
</style>
