<template>
  <div class="container">
    <TopNavigationBar title="审核管理">
      <template #tool-bar>
        <i class="el-icon-search text-xl" @click="toggleSearchPanel"></i>
      </template>
    </TopNavigationBar>
    <!-- 筛选区域 -->
    <transition name="slide-down">
      <div v-show="showSearchPanel" class="filter-section">
        <el-input
          v-model="searchText"
          placeholder="搜索申请人或物品名称"
          prefix-icon="el-icon-search"
          clearable
          @input="handleSearch"
          class="search-input"
        ></el-input>

        <div class="filter-row">
          <el-select v-model="filterStatus" placeholder="状态" size="small" @change="loadRequisitions">
            <el-option label="全部" value=""></el-option>
            <el-option label="待审核" :value="1"></el-option>
            <el-option label="已通过" :value="2"></el-option>
            <el-option label="已拒绝" :value="3"></el-option>
            <el-option label="已发放" :value="4"></el-option>
          </el-select>
        </div>
      </div>
    </transition>
    <div class="content">
      <!-- 统计信息 -->
      <div class="stats-section">
        <div class="stats-row">
          <div class="stat-item pending">
            <div class="stat-content">
              <div class="stat-number">{{ stats.pending }}</div>
              <div class="stat-label">待审核</div>
            </div>
          </div>
          <div class="stat-item approved">
            <div class="stat-content">
              <div class="stat-number">{{ stats.approved }}</div>
              <div class="stat-label">已通过</div>
            </div>
          </div>
        </div>
        <div class="stats-row">
          <div class="stat-item rejected">
            <div class="stat-content">
              <div class="stat-number">{{ stats.rejected }}</div>
              <div class="stat-label">已拒绝</div>
            </div>
          </div>
          <div class="stat-item distributed">
            <div class="stat-content">
              <div class="stat-number">{{ stats.distributed }}</div>
              <div class="stat-label">已发放</div>
            </div>
          </div>
        </div>
        <div class="stats-total">
          <div class="total-item">
            <span class="total-label">总计：</span>
            <span class="total-number">{{ stats.total }}</span>
          </div>
        </div>
      </div>

      <!-- 列表区域 -->
      <div class="list-container">
        <div v-if="loading" class="loading-container">
          <el-skeleton :loading="loading" :count="3" animated></el-skeleton>
        </div>

        <div v-else-if="requisitionList.length === 0" class="empty-state">
          <div class="empty-icon">📋</div>
          <p>暂无领用单数据</p>
        </div>

        <div v-else class="requisition-items">
          <div
            v-for="item in requisitionList"
            :key="item.id"
            class="requisition-item"
            :class="getStatusClass(item.status)"
          >
            <div class="item-header">
              <div class="header-left">
                <h4 class="material-name">{{ item.materialName }}</h4>
                <div class="applicant-info">
                  <span class="quantity">{{ item.quantity }}{{ item.unit }}</span>
                  <span class="applicant">申请人: {{ item.applicantName }}</span>
                </div>
              </div>
              <div class="header-right">
                <span class="status-badge" :class="getStatusClass(item.status)">
                  {{ getStatusText(item.status) }}
                </span>
              </div>
            </div>

            <div class="item-content">
              <p class="purpose">{{ item.purpose }}</p>
            </div>

            <div class="item-footer">
              <div class="time-info">
                <span>申请时间: {{ formatDateTime(item.createTime) }}</span>
              </div>
              <div class="actions">
                <el-button type="text" size="mini" @click="viewDetail(item)"> 查看详情 </el-button>
                <el-button
                  v-if="item.status === 1"
                  type="text"
                  size="mini"
                  @click="approveRequisition(item)"
                  class="approve-btn"
                >
                  审核
                </el-button>
                <el-button
                  v-if="item.status === 2"
                  type="text"
                  size="mini"
                  @click="confirmDistribution(item)"
                  class="distribute-btn"
                >
                  确认发放
                </el-button>
                <!-- <el-button type="text" size="mini" @click="deleteRequisition(item)" class="delete-btn">
                  删除
                </el-button> -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详情对话框 -->
    <el-dialog title="领用单详情" :visible.sync="detailDialogVisible" width="90%" :close-on-click-modal="true">
      <div v-if="selectedItem" class="detail-content">
        <div class="detail-row">
          <label>申请人:</label>
          <span>{{ selectedItem.applicantName }}</span>
        </div>
        <div class="detail-row">
          <label>物品名称:</label>
          <span>{{ selectedItem.materialName }}</span>
        </div>
        <div class="detail-row">
          <label>数量:</label>
          <span>{{ selectedItem.quantity }}{{ selectedItem.unit }}</span>
        </div>
        <div class="detail-row">
          <label>用途说明:</label>
          <span>{{ selectedItem.purpose }}</span>
        </div>
        <div class="detail-row">
          <label>状态:</label>
          <span class="status-badge" :class="getStatusClass(selectedItem.status)">
            {{ getStatusText(selectedItem.status) }}
          </span>
        </div>
        <div v-if="selectedItem.remark" class="detail-row">
          <label>备注:</label>
          <span>{{ selectedItem.remark }}</span>
        </div>
        <div v-if="selectedItem.attachments" class="detail-row">
          <label>附件:</label>
          <div class="attachments">
            <div
              v-for="(file, index) in getAttachmentList(selectedItem.attachments)"
              :key="index"
              class="attachment-item"
            >
              <a :href="file.url" target="_blank" class="attachment-link">
                <i class="el-icon-document"></i>
                {{ file.name }}
              </a>
            </div>
          </div>
        </div>
        <div class="detail-row">
          <label>申请时间:</label>
          <span>{{ formatDateTime(selectedItem.createTime) }}</span>
        </div>
        <div v-if="selectedItem.approveTime" class="detail-row">
          <label>审核时间:</label>
          <span>{{ formatDateTime(selectedItem.approveTime) }}</span>
        </div>
        <div v-if="selectedItem.approveRemark" class="detail-row">
          <label>审核意见:</label>
          <span>{{ selectedItem.approveRemark }}</span>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button
          v-if="selectedItem && selectedItem.status === 1"
          type="primary"
          @click="approveRequisition(selectedItem)"
        >
          审核
        </el-button>
      </div>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog title="审核领用单" :visible.sync="approveDialogVisible" width="90%" :close-on-click-modal="false">
      <el-form :model="approveForm" :rules="approveRules" ref="approveFormRef" label-position="top">
        <div class="approve-info">
          <p><strong>申请人:</strong> {{ approveForm.applicantName }}</p>
          <p>
            <strong>物品:</strong> {{ approveForm.materialName }} × {{ approveForm.quantity }}{{ approveForm.unit }}
          </p>
          <p><strong>用途:</strong> {{ approveForm.purpose }}</p>
        </div>

        <el-form-item label="审核结果" prop="result">
          <el-radio-group v-model="approveForm.result">
            <el-radio :label="2">通过</el-radio>
            <el-radio :label="3">拒绝</el-radio>
          </el-radio-group>
          <div v-if="approveForm.result === 2" class="approval-note">
            <i class="el-icon-info"></i> 审核通过后，物品将进入待发放状态，需要文员确认发放
          </div>
        </el-form-item>

        <el-form-item label="审核意见" prop="remark">
          <el-input type="textarea" v-model="approveForm.remark" placeholder="请输入审核意见" rows="4"></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="approveDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitApprove" :loading="approving"> 提交审核 </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import TopNavigationBar from "@/components/XTopNavigationBar.vue";
import service from "@/router/request";
import { MessageUtil } from "@/utils/utils";

export default {
  components: { TopNavigationBar },
  name: "RequisitionManagement",
  data() {
    return {
      loading: false,
      approving: false,
      searchText: "",
      filterStatus: "",
      filterUrgency: "",
      requisitionList: [],
      stats: {
        pending: 0, // 状态1：待审核
        approved: 0, // 状态2：已通过
        rejected: 0, // 状态3：已拒绝
        distributed: 0, // 状态4：已发放
        total: 0,
      },

      // 详情对话框
      detailDialogVisible: false,
      selectedItem: null,

      // 审核对话框
      approveDialogVisible: false,
      approveForm: {
        id: null,
        applicantName: "",
        materialName: "",
        quantity: "",
        unit: "",
        purpose: "",
        result: "",
        remark: "",
      },
      approveRules: {
        result: [{ required: true, message: "请选择审核结果", trigger: "change" }],
        remark: [{ required: true, message: "请输入审核意见", trigger: "blur" }],
      },

      searchTimer: null,
      showSearchPanel: false,
    };
  },

  mounted() {
    this.loadRequisitions();
    this.loadStats();
  },

  beforeDestroy() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
  },

  methods: {
    // 加载领用单列表
    async loadRequisitions() {
      this.loading = true;
      try {
        const params = {};
        if (this.filterStatus) params.status = this.filterStatus;
        if (this.filterUrgency) params.urgency = this.filterUrgency;
        if (this.searchText) params.search = this.searchText;

        const response = await service.get("/MaterialRequisition/GetAllRequisitions", { params });
        if (response.status === "succeed") {
          this.requisitionList = response.data || [];
        } else {
          MessageUtil.error("获取领用单列表失败");
        }
      } catch (error) {
        console.error("获取领用单列表失败:", error);
        MessageUtil.error("获取领用单列表失败");
      } finally {
        this.loading = false;
      }
    },

    // 加载统计数据
    async loadStats() {
      try {
        const response = await service.get("/MaterialRequisition/GetStats");
        if (response.status === "succeed") {
          this.stats = response.data || this.stats;
        }
      } catch (error) {
        console.error("获取统计数据失败:", error);
      }
    },

    toggleSearchPanel() {
      this.showSearchPanel = !this.showSearchPanel;
    },

    // 搜索处理
    handleSearch() {
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }

      this.searchTimer = setTimeout(() => {
        this.loadRequisitions();
      }, 300);
    },

    // 查看详情
    viewDetail(item) {
      this.selectedItem = item;
      this.detailDialogVisible = true;
    },

    // 审核领用单
    approveRequisition(item) {
      this.approveForm = {
        id: item.id,
        applicantName: item.applicantName,
        materialName: item.materialName,
        quantity: item.quantity,
        unit: item.unit,
        purpose: item.purpose,
        result: "",
        remark: "",
      };
      this.approveDialogVisible = true;
      this.detailDialogVisible = false;
    },

    // 提交审核
    submitApprove() {
      this.$refs.approveFormRef.validate(async (valid) => {
        if (valid) {
          this.approving = true;
          try {
            const response = await service.post("/MaterialRequisition/Approve", {
              id: this.approveForm.id,
              result: this.approveForm.result,
              remark: this.approveForm.remark,
            });

            if (response.status === "succeed") {
              MessageUtil.success("审核完成！");
              this.approveDialogVisible = false;
              this.loadRequisitions();
              this.loadStats();
            } else {
              MessageUtil.error("审核失败，请稍后重试！");
            }
          } catch (error) {
            console.error("审核失败:", error);
            MessageUtil.error("审核失败，请稍后重试！");
          } finally {
            this.approving = false;
          }
        } else {
          MessageUtil.error("请完善审核信息！");
        }
      });
    },

    // 确认发放物品
    confirmDistribution(item) {
      this.$confirm(`确认已发放物品给${item.applicantName}吗？`, "确认发放", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "info",
        customClass: "phone-message-box",
      })
        .then(async () => {
          try {
            const response = await service.post("/MaterialRequisition/ConfirmDistribution", { id: item.id });
            if (response.status === "succeed") {
              MessageUtil.success("物品发放确认成功！");
              this.loadRequisitions();
              this.loadStats();
            } else {
              MessageUtil.error("操作失败，请稍后重试！");
            }
          } catch (error) {
            console.error("确认发放失败:", error);
            MessageUtil.error("操作失败，请稍后重试！");
          }
        })
        .catch(() => {
          // 用户取消操作
        });
    },

    // 删除领用单
    deleteRequisition(item) {
      this.$confirm(`确定要删除${item.applicantName}的领用申请吗？`, "确认删除", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        customClass: "phone-message-box",
      })
        .then(async () => {
          try {
            const response = await service.post("/MaterialRequisition/Delete", { id: item.id });
            if (response.status === "succeed") {
              MessageUtil.success("删除成功！");
              this.loadRequisitions();
              this.loadStats();
            } else {
              MessageUtil.error("删除失败，请稍后重试！");
            }
          } catch (error) {
            console.error("删除失败:", error);
            MessageUtil.error("删除失败，请稍后重试！");
          }
        })
        .catch(() => {
          // 用户取消删除
        });
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        1: "待审核",
        2: "已通过",
        3: "已拒绝",
        4: "已发放",
      };
      return statusMap[status] || status;
    },

    // 获取状态样式
    getStatusClass(status) {
      const statusClassMap = {
        1: "status-pending",
        2: "status-approved",
        3: "status-rejected",
        4: "status-distributed",
      };
      return statusClassMap[status] || `status-${status}`;
    },

    // 获取紧急程度文本
    getUrgencyText(urgency) {
      const urgencyMap = {
        1: "普通",
        2: "紧急",
        3: "特急",
      };
      return urgencyMap[urgency] || "普通";
    },

    // 格式化日期时间
    formatDateTime(dateString) {
      if (!dateString) return "";
      const date = new Date(dateString);
      return date.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
      });
    },

    // 解析附件列表
    getAttachmentList(attachmentsString) {
      if (!attachmentsString) return [];
      try {
        return JSON.parse(attachmentsString);
      } catch (error) {
        return [];
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.content {
  flex: 1;
  padding: 16px;
}

/* 筛选区域 */
.filter-section {
  background: white;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .search-input {
    margin-bottom: 12px;
  }

  .filter-row {
    display: flex;
    gap: 12px;

    .el-select {
      flex: 1;
    }
  }
}

/* 统计区域 */
.stats-section {
  margin-bottom: 16px;

  .stats-row {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;

    .stat-item {
      flex: 1;
      background: white;
      border-radius: 12px;
      padding: 20px 16px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      border-left: 4px solid #e4e7ed;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
      }

      .stat-content {
        text-align: center;

        .stat-number {
          font-size: 28px;
          font-weight: 700;
          margin-bottom: 8px;
          line-height: 1;
        }

        .stat-label {
          font-size: 14px;
          font-weight: 500;
          color: #606266;
        }
      }

      &.pending {
        border-left-color: #e6a23c;
        .stat-number {
          color: #e6a23c;
        }
      }

      &.approved {
        border-left-color: #67c23a;
        .stat-number {
          color: #67c23a;
        }
      }

      &.rejected {
        border-left-color: #f56c6c;
        .stat-number {
          color: #f56c6c;
        }
      }

      &.distributed {
        border-left-color: #909399;
        .stat-number {
          color: #909399;
        }
      }
    }
  }

  .stats-total {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 16px 20px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);

    .total-item {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 8px;

      .total-label {
        color: rgba(255, 255, 255, 0.9);
        font-size: 16px;
        font-weight: 500;
      }

      .total-number {
        color: white;
        font-size: 24px;
        font-weight: 700;
      }
    }
  }
}

/* 列表容器 */
.list-container {
  .loading-container {
    background: white;
    padding: 16px;
    border-radius: 8px;
  }

  .empty-state {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 8px;

    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }

    p {
      color: #909399;
      font-size: 16px;
      margin: 0;
    }
  }
}

/* 领用单项样式 */
.requisition-items {
  .requisition-item {
    background: white;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #e4e7ed;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &.status-pending {
      border-left-color: #e6a23c;
    }

    &.status-approved {
      border-left-color: #67c23a;
    }

    &.status-rejected {
      border-left-color: #f56c6c;
    }

    &.status-completed {
      border-left-color: #909399;
    }
  }
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;

  .header-left {
    flex: 1;
    display: flex;

    .material-name {
      margin: 0 0 8px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .applicant-info {
      display: flex;
      gap: 12px;
      align-items: center;
      margin-left: 10px;

      .applicant {
        font-size: 14px;
        color: #606266;
      }

      .quantity {
        font-size: 14px;
        color: #606266;
        background-color: #f0f2f5;
        padding: 2px 8px;
        border-radius: 12px;
      }
    }
  }

  .header-right {
    display: flex;
    flex-direction: column;
    gap: 4px;
    align-items: flex-end;
  }
}

/* 状态标签 */
.status-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;

  &.status-pending {
    background-color: rgba(230, 162, 60, 0.1);
    color: #e6a23c;
  }

  &.status-approved {
    background-color: rgba(103, 194, 58, 0.1);
    color: #67c23a;
  }

  &.status-rejected {
    background-color: rgba(245, 108, 108, 0.1);
    color: #f56c6c;
  }

  &.status-completed {
    background-color: rgba(144, 147, 153, 0.1);
    color: #909399;
  }
}

/* 紧急程度标签 */
.urgency-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;

  &.urgency-1 {
    background-color: rgba(64, 158, 255, 0.1);
    color: #409eff;
  }

  &.urgency-2 {
    background-color: rgba(230, 162, 60, 0.1);
    color: #e6a23c;
  }

  &.urgency-3 {
    background-color: rgba(245, 108, 108, 0.1);
    color: #f56c6c;
  }
}

.item-content {
  margin-bottom: 12px;

  .purpose {
    color: #606266;
    line-height: 1.5;
    margin: 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #909399;

  .time-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .actions {
    display: flex;
    gap: 8px;

    .approve-btn {
      color: #67c23a;

      &:hover {
        color: #85ce61;
      }
    }

    .distribute-btn {
      color: #409eff;

      &:hover {
        color: #66b1ff;
      }
    }

    .delete-btn {
      color: #f56c6c;

      &:hover {
        color: #f78989;
      }
    }
  }
}

/* 详情对话框 */
.detail-content {
  .detail-row {
    display: flex;
    margin-bottom: 12px;
    align-items: flex-start;

    label {
      min-width: 80px;
      font-weight: 600;
      color: #606266;
      margin-right: 12px;
    }

    span {
      flex: 1;
      color: #303133;
    }
  }

  .attachments {
    .attachment-item {
      margin-bottom: 8px;

      .attachment-link {
        color: #409eff;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 4px;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

/* 审核对话框 */
.approve-info {
  background-color: #f8f9fa;
  // padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;

  p {
    margin: 0 0 8px 0;
    color: #606266;

    &:last-child {
      margin-bottom: 0;
    }

    strong {
      color: #303133;
    }
  }
}

/* 审核说明样式 */
.approval-note {
  margin-top: 8px;
  padding: 8px 12px;
  background-color: #e1f3d8;
  border: 1px solid #b3d8a4;
  border-radius: 4px;
  color: #67c23a;
  font-size: 12px;
  line-height: 1.5;

  i {
    margin-right: 4px;
  }
}

.dialog-footer {
  text-align: center;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .content {
    padding: 12px;
  }

  .filter-section {
    padding: 12px;

    .filter-row {
      flex-direction: column;
      gap: 8px;
    }
  }

  .stats-section {
    .stats-row {
      gap: 8px;
      margin-bottom: 8px;

      .stat-item {
        padding: 16px 12px;

        .stat-content {
          .stat-number {
            font-size: 24px;
          }

          .stat-label {
            font-size: 13px;
          }
        }
      }
    }

    .stats-total {
      padding: 12px 16px;

      .total-item {
        .total-label {
          font-size: 14px;
        }

        .total-number {
          font-size: 20px;
        }
      }
    }
  }

  .requisition-item {
    padding: 12px;
  }

  .item-header {
    // flex-direction: column;
    align-items: flex-start;
    gap: 8px;

    .applicant-info {
      // flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }

    .header-right {
      align-items: flex-start;
      flex-direction: row;
      gap: 8px;
    }
  }

  .item-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;

    .actions {
      align-self: flex-end;
    }
  }
}

@media (max-width: 480px) {
  .stats-section {
    .stats-row {
      // flex-direction: column;
      gap: 8px;

      .stat-item {
        padding: 14px 12px;

        .stat-content {
          .stat-number {
            font-size: 22px;
          }

          .stat-label {
            font-size: 12px;
          }
        }
      }
    }

    .stats-total {
      padding: 12px 16px;

      .total-item {
        .total-label {
          font-size: 14px;
        }

        .total-number {
          font-size: 18px;
        }
      }
    }
  }
}

/* 自定义过渡动画 */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}
.slide-down-enter,
.slide-down-leave-to {
  transform: translateY(-20px);
  opacity: 0;
}
</style>
