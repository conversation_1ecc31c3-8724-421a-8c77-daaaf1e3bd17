<template>
  <div style="display: flex; justify-content: end" class="mb-2">
    <el-dialog :visible.sync="show" title="新增人员" :close-on-click-modal="false">
      <el-form label-position="top" label-width="100px" style="padding-right: 50px" :model="form" :rules="rules"
        ref="formRef">
        <el-form-item label="姓名" prop="lastname">
          <el-autocomplete value-key="lastname" v-model="form.lastname" :fetch-suggestions="querySearchAsync"
            placeholder="请选择人，可输入名字搜索" @select="handleSelect" @focus="loadAll"></el-autocomplete>
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item prop="loginid" label="工号">
              <el-input v-model="form.loginid" :disabled="true"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="pinyinlastname" label="拼音首写">
              <el-input v-model="form.pinyinlastname"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="sex" label="性别">
              <el-input v-model="form.sex"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="departmentname" label="部门">
              <el-input v-model="form.departmentname"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="departmentid" label="部门号">
              <el-input v-model="form.departmentid" :disabled="true"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="jobtitlename" label="职务">
              <el-input v-model="form.jobtitlename"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="mobile" label="电话">
              <el-input v-model="form.mobile"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="houwage" label="工时价">
              <el-input v-model="form.houwage"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="basic_performance" label="绩效金额">
              <el-input v-model="form.basic_performance" type="number"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div>
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="save">确 定</el-button>
      </div>
    </el-dialog>
    <el-tooltip content="暂无权限" :disabled="havePermissionRole([2, 8])">
      <el-button type="primary" @click="show = true" round :disabled="!havePermissionRole([2, 8])">新增人员</el-button>
    </el-tooltip>
  </div>
</template>

<script>
import service from "@/router/request";
import { havePermissionRole } from "@/utils/permission";

export default {
  name: "XinChouAdd",
  data() {
    return {
      show: false,
      form: {
        loginid: '',
        sex: '',
        mobile: '',
        departmentname: '',
        departmentid: '',
        jobtitlename: '',
        pinyinlastname: '',
        createdate: ''
      },
      rules: {
        houwage: [
          { required: true, message: "工时价不能为空", trigger: "blur" }
        ],
        lastname: [{ required: true, message: "请输入姓名", trigger: "blur" }]
      },
      restaurants: []
    };
  },
  mounted() {
    this.loadAll();
  },
  methods: {
    havePermissionRole,

    save() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          // 表单验证通过，执行保存逻辑

          // 执行保存操作
          // 可以在这里调用接口，将表单数据传递给后端进行保存
          service.post("/XinChou/addperson", this.form).then(res => {
            if (res.status == "succeed") {
              // 表示成功保存
              this.$message.success("添加成功");

              this.show = false; // 关闭对话框
              this.$router.go(0);
            } else {
              this.$message.error(res.err); // 弹出错误的信息
            }
          });
        }
      });
    },
    cancel() {
      this.show = false; // 关闭对话框
      // 清空表单数据
      this.form = {};
    },
    //查询当前所有人员
    querySearchAsync(queryString, cb) {
      var restaurants = this.restaurants;
      var results = queryString
        ? restaurants.filter(this.createStateFilter(queryString))
        : restaurants;

      clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        cb(results);
      }, 300);
    },
    createStateFilter(queryString) {
      return restaurant => {
        return (
          restaurant.lastname
            .toLowerCase()
            .indexOf(queryString.toLowerCase()) === 0
        );
      };
    },
    handleSelect(item) {
      console.log(item);
      
        this.form.loginid = item.loginid;
        this.form.sex = item.sex;
        this.form.mobile = item.mobile;
        this.form.departmentname = item.departmentname;
        this.form.departmentid = item.departmentid;
        this.form.jobtitlename = item.jobtitlename;
        this.form.createdate = item.createdate;
        this.form.pinyinlastname = item.pinyinlastname;
      
    },
    //查询出所有成员
    loadAll() {
      service
        .get("/Public/Getperson")
        .then(res => {
          if (res.status == "succeed") {
            this.restaurants = res.data;
          }
        })
        .catch(error => {
          console.error("Error fetching restaurants", error);
        });
    }
  }
};
</script>

<style scoped></style>
