<template>
  <div>
    <el-button @click="goBack" icon="el-icon-back" circle></el-button>
    <h1 style="text-align: center;">机型内容</h1>
    <el-descriptions class="mb-2" :column="2" border>
      <el-descriptions-item label="机型名称">{{ rowData.mmoname }}</el-descriptions-item>
      <el-descriptions-item label="所属巴组">{{ rowData.grono }}</el-descriptions-item>
      <el-descriptions-item label="机型型号">{{ rowData.mmomodel }}</el-descriptions-item>
      <el-descriptions-item label="当前状态">
        {{
        rowData.mmosflag === 0
        ? "停用"
        : rowData.mmosflag === 1
        ? "整机未审"
        : rowData.mmosflag === 2
        ? "变更未审"
        : rowData.mmosflag === 3
        ? "新增未审"
        : rowData.mmosflag === 4
        ? "正常"
        : ""
        }}
      </el-descriptions-item>
      <el-descriptions-item label="机型名称">{{ rowData.mmoname }}</el-descriptions-item>
      <el-descriptions-item label="录入姓名">{{ rowData.createname }}</el-descriptions-item>
      <el-descriptions-item label="产品描述">{{ rowData.mmoremarks }}</el-descriptions-item>
    </el-descriptions>
    <el-table
      :data="tableData"
      border
      :header-cell-style="{ textAlign: 'center', backgroundColor: '#c5e3ef' }"
    >
      <el-table-column prop="mpaname" label="部位" align="center"></el-table-column>
      <el-table-column prop="sasname" label="组件" align="center"></el-table-column>
      <el-table-column prop="ssuname" label="子件" align="center"></el-table-column>
      <el-table-column prop="wprno" label="工序" align="center"></el-table-column>
      <el-table-column prop="manhours" label="工时" align="center"></el-table-column>
      <el-table-column prop="mhprice" label="工时价" align="center"></el-table-column>
      <el-table-column prop="swsflagstatus" label="状态" align="center"></el-table-column>
      <el-table-column prop="swremarks" label="备注" align="center"></el-table-column>
    </el-table>
  </div>
</template>

<script>
import service from "@/router/request";

export default {
  name: "JijianZl",

  data() {
    return {
      // 表格数据
      tableData: [],
      rowData: []
    };
  },
  mounted() {
    const mmono = this.$route.query.mmono;
    // 根据mmono进行查询操作

    this.Getjx(mmono);
  },
  computed: {},
  methods: {
    //产品表头信息
    Getjx(mmono) {
      service.post("/JiJian/Getmmodetails1?mmono=" + mmono).then(res => {
        if (res.status == "succeed") {
          this.rowData = res.data;

          service.post("/Chanpin/Getmpadetail", { mmo_no: mmono }).then(res => {
            this.tableData = res.data;
          });
        }
      });
    },

    goBack() {
      this.$router.back();
    }
  }
};
</script>

<style scoped>
.container {
  position: relative; /* 确保容器相对定位 */
}
.left-top-button {
  position: absolute; /* 绝对定位 */
  top: 0; /* 距离顶部边界为0像素 */
  left: 0; /* 距离左侧边界为0像素 */
}
</style>
