<template>
  <div>
    <el-dialog :visible.sync="show" title="新增用户">
      <el-form label-width="100px" style="padding-right: 50px" :model="form" :rules="rules" ref="formRef">
        <el-form-item label="选择用户">
          <el-autocomplete value-key="lastname" v-model="form.name" :fetch-suggestions="querySearchAsync"
            placeholder="请选中用户，可输入姓名搜索" @select="handleSelect" @focus="loadAll"></el-autocomplete>
        </el-form-item>
        <el-form-item prop="loginid" label="工号">
          <el-input v-model="form.loginid" autocomplete="off" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="角色">
          <el-select v-model="form.role_id">
            <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div>
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="save">确 定</el-button>
      </div>
    </el-dialog>
    <el-button style="float: right" type="primary" @click="SelectRole" round>新增成员</el-button>
  </div>
</template>

<script>
import service from "@/router/request";

export default {
  name: "AddUser",
  data() {
    return {
      show: false,
      form: {},
      rules: {},
      restaurants: [],
      options: [],
    };
  },
  mounted() {
    this.getBuzu();
  },
  methods: {
    //查询当前所有人员
    querySearchAsync(queryString, cb) {
      var restaurants = this.restaurants;
      var results = queryString
        ? restaurants.filter(this.createStateFilter(queryString))
        : restaurants;

      clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        cb(results);
      }, 300);
    },
    createStateFilter(queryString) {
      return (restaurant) => {
        return (
          restaurant.lastname
            .toLowerCase()
            .indexOf(queryString.toLowerCase()) === 0
        );
      };
    },
    handleSelect(item) {
      console.log(item);
      this.form.loginid = item.loginid;
    },
    //查询所有人员
    loadAll() {
      service
        .get("/Public/Getmember")
        .then((res) => {
          if (res.status == "succeed") {
            this.restaurants = res.data;
          }
        })
        .catch((error) => {
          console.error("Error fetching restaurants", error);
        });
    },
    save() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          // 表单验证通过，执行保存逻辑

          // 执行保存操作
          // 可以在这里调用接口，将表单数据传递给后端进行保存
          service.post("/Privilege/add_user", this.form).then((res) => {
            if (res.status == "succeed") {
              // 表示成功保存
              this.$message.success("添加成功");

              this.show = false; // 关闭对话框
              this.$router.go(0);
            } else {
              this.$message.error(res.err); // 弹出错误的信息
            }
          });
        }
      });
    },
    cancel() {
      this.show = false; // 关闭对话框
      // 清空表单数据
      this.form = {};
    },
    SelectRole() {
      this.show = true;
      service.get("/Role/getRoles").then((res) => {
        if (res.status == "succeed") {
          this.options = res.data;
        }
      });
    },
    getBuzu() {
      service.get("/Bazu/bazulist").then((res) => {
        if (res.status == "succeed") {
          this.option = res.data;
        }
      });
    },
  },
};
</script>

<style scoped></style>
