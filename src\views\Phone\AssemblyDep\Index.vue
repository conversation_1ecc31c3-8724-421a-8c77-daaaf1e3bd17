<template>
  <div class="assembly-dep-container">
    <!-- 顶部标题栏 -->
    <div class="header-section">
      <h1 class="page-title">总装部</h1>
      <p class="page-subtitle">欢迎使用总装部移动工作台</p>
    </div>

    <!-- 功能网格 -->
    <div class="grid-container">
      <template v-for="(option, index) in options">
        <div
          :key="index"
          class="grid-item"
          @click="navigateTo(option)"
          :class="{ disabled: option.permissionAuth && !hasPermission }"
        >
          <div class="icon">{{ option.icon || "🔗" }}</div>
          <div class="label">{{ option.label }}</div>
          <div v-if="option.permissionAuth && !hasPermission" class="lock-icon">🔒</div>
        </div>
      </template>
    </div>

    <!-- 公告区域 -->
    <div class="announcement-container">
      <!-- 设置按钮 -->
      <div v-if="hasPermission" class="settings-btn" @click="showSettings = true">
        <i class="el-icon-setting"></i>
      </div>

      <div class="announcement">
        <h3><span class="icon">🏆</span> 表彰栏</h3>
        <div class="scroll-container" :style="{ height: announcementSettings.scrollHeight + 'px' }">
          <div
            class="scroll-content"
            v-if="commendations.length"
            :style="{
              animationDuration: calculateScrollDuration('commendations') + 's',
              animationPlayState: settingsPreview ? 'running' : 'running',
              '--container-height': announcementSettings.scrollHeight + 'px',
            }"
            ref="commendationsContent"
          >
            <div
              v-for="(item, index) in commendations"
              :key="index"
              class="announcement-item"
              :class="getPriorityClass(item.priority)"
            >
              <span class="bullet">•</span>
              <span v-if="item.priority >= 3" class="priority-icon">{{ getPriorityIcon(item.priority) }}</span>
              {{ item.content }}
            </div>
          </div>
          <div v-else class="empty-state">暂无表彰信息</div>
        </div>
      </div>
      <div class="announcement">
        <h3><span class="icon">📢</span> 公示栏</h3>
        <div class="scroll-container" :style="{ height: announcementSettings.scrollHeight + 'px' }">
          <div
            class="scroll-content"
            v-if="notices.length"
            :style="{
              animationDuration: calculateScrollDuration('notices') + 's',
              animationPlayState: settingsPreview ? 'running' : 'running',
              '--container-height': announcementSettings.scrollHeight + 'px',
            }"
            ref="noticesContent"
          >
            <div
              v-for="(item, index) in notices"
              :key="index"
              class="announcement-item"
              :class="getPriorityClass(item.priority)"
            >
              <span class="bullet">•</span>
              <span v-if="item.priority >= 3" class="priority-icon">{{ getPriorityIcon(item.priority) }}</span>
              {{ item.content }}
            </div>
          </div>
          <div v-else class="empty-state">暂无公示信息</div>
        </div>
      </div>
    </div>

    <!-- 设置面板 -->
    <div v-if="showSettings" class="settings-overlay" @click="closeSettings">
      <div class="settings-panel" @click.stop>
        <div class="settings-header">
          <h3>公告栏设置</h3>
          <button class="close-btn" @click="closeSettings">
            <i class="el-icon-close"></i>
          </button>
        </div>

        <div class="settings-content">
          <!-- 滚动速度设置 -->
          <div class="setting-group">
            <label class="setting-label">
              <span class="label-text">滚动速度</span>
              <span class="label-value">{{ announcementSettings.scrollSpeed }}px/s</span>
            </label>
            <div class="slider-container">
              <span class="slider-label">慢</span>
              <input
                type="range"
                v-model="announcementSettings.scrollSpeed"
                min="10"
                max="50"
                step="2"
                class="speed-slider"
                @input="onSettingsChange"
              />
              <span class="slider-label">快</span>
            </div>
            <div class="speed-presets">
              <button
                v-for="preset in speedPresets"
                :key="preset.value"
                class="preset-btn"
                :class="{ active: announcementSettings.scrollSpeed == preset.value }"
                @click="setScrollSpeed(preset.value)"
              >
                {{ preset.label }}
              </button>
            </div>
          </div>

          <!-- 滚动区域高度设置 -->
          <div class="setting-group">
            <label class="setting-label">
              <span class="label-text">滚动区域高度</span>
              <span class="label-value">{{ announcementSettings.scrollHeight }}px</span>
            </label>
            <div class="slider-container">
              <span class="slider-label">低</span>
              <input
                type="range"
                v-model="announcementSettings.scrollHeight"
                min="80"
                max="200"
                step="10"
                class="height-slider"
                @input="onSettingsChange"
              />
              <span class="slider-label">高</span>
            </div>
            <div class="height-presets">
              <button
                v-for="preset in heightPresets"
                :key="preset.value"
                class="preset-btn"
                :class="{ active: announcementSettings.scrollHeight == preset.value }"
                @click="setScrollHeight(preset.value)"
              >
                {{ preset.label }}
              </button>
            </div>
          </div>
        </div>

        <div class="settings-footer">
          <button class="reset-btn" @click="resetSettings">
            <i class="el-icon-refresh"></i>
            重置默认
          </button>
          <button class="save-btn" @click="saveSettings">
            <i class="el-icon-check"></i>
            保存设置
          </button>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p>加载中...</p>
    </div>
  </div>
</template>

<script>
import service from "@/router/request";
import { MessageUtil } from "@/utils/utils";
import {
  getAnnouncementSettings,
  saveAnnouncementSettings,
  getDefaultSettings,
  validateSettings,
} from "@/api/announcementSettings";

export default {
  name: "AssemblyDep",
  data() {
    return {
      loading: false,
      member: {
        loginId: this.$route.query.loginid || this.$store.state.user.loginId || 0,
      },
      options: [
        { label: "工作日报", icon: "📄", path: "/dailyWorkReport", permissionAuth: false },
        { label: "考勤中心", icon: "⏰", path: "/attendanceCenter", permissionAuth: false },
        { label: "部门通告", icon: "📢", path: "/announcement", permissionAuth: true },
        { label: "工资条", icon: "💰", path: "/salarySheet", permissionAuth: false },
        { label: "培训中心", icon: "🎓", path: "/trainingCenter", permissionAuth: false },
        { label: "精益改善", icon: "🛠️", path: "/leanImprovementApp", permissionAuth: false },
        { label: "部门调动", icon: "🔄", path: "/departmentTransfer", permissionAuth: true },
        // 开发测试用，生产环境应移除
        // ...(process.env.NODE_ENV === "development"
        //   ? [
        //       { label: "权限测试", icon: "🔧", path: "/permissionTest", permissionAuth: false },
        //       { label: "设置演示", icon: "🎛️", path: "/announcementDemo", permissionAuth: false },
        //     ]
        //   : []),
      ],
      commendations: [
        "张三同志在本月生产任务中表现突出，获得月度优秀员工称号",
        "李四团队成功完成重要项目，为公司创造显著价值",
        "王五在技术创新方面贡献突出，获得技术创新奖",
        "赵六积极参与公司培训，获得学习进步奖",
        "钱七在安全生产方面表现优异，获得安全标兵称号",
      ],
      notices: [
        "关于调整作息时间的通知：自下周一起，上班时间调整为8:30-17:30",
        "公司将于本周五举行消防演练，请全体员工积极配合",
        "年度体检安排：请各部门按照通知时间安排员工体检",
        "关于规范停车秩序的通知：请将车辆停放在指定区域",
        "食堂菜单更新：新增多种营养搭配套餐，欢迎品尝",
      ],

      // 设置相关
      showSettings: false,
      settingsPreview: false,
      announcementSettings: {
        scrollSpeed: 20, // 滚动速度（px/s）
        scrollHeight: 120, // 滚动区域高度（px）
      },

      // 预设选项
      speedPresets: [
        { label: "极慢", value: 10 },
        { label: "慢速", value: 16 },
        { label: "标准", value: 20 },
        { label: "快速", value: 30 },
        { label: "极快", value: 50 },
      ],
      heightPresets: [
        { label: "紧凑", value: 80 },
        { label: "标准", value: 120 },
        { label: "宽松", value: 160 },
        { label: "超大", value: 200 },
      ],
    };
  },
  computed: {
    hasPermission() {
      // 直接访问 store 状态，确保响应式
      const memberRole = this.$store.state.user?.roleId || this.$store.state.user?.roleId || 0;
      return memberRole == 8;
    },
  },
  mounted() {
    // store.state.user.loginId = this.member.loginId;
    this.getMember();
    this.getAnnouncement();
    this.loadSettings();
  },
  methods: {
    // 获取重要性样式类
    getPriorityClass(priority) {
      if (!priority) return "";

      const priorityClasses = {
        1: "priority-low",
        2: "priority-normal",
        3: "priority-important",
        4: "priority-urgent",
        5: "priority-critical",
      };

      return priorityClasses[priority] || "";
    },

    // 获取重要性图标
    getPriorityIcon(priority) {
      const priorityIcons = {
        1: "📝",
        2: "📢",
        3: "⚠️",
        4: "🚨",
        5: "🔥",
      };

      return priorityIcons[priority] || "";
    },

    getMember() {
      service.get(`/Member/getMember/${this.member.loginId}`).then((res) => {
        if (res.status == "succeed") {
          if (!res.data) MessageUtil.warning("未查询到用户信息");
          this.member = res.data;
          // 使用 mutation 更新 store 状态，确保响应式
          this.$store.commit("updatePersonInfo", this.member);
          localStorage.setItem("person", JSON.stringify({ token: "", info: this.member }));
        }
      });
    },
    navigateTo(option) {
      if (option.permissionAuth && !this.hasPermission) {
        return MessageUtil.warning("暂无权限");
      }
      if (!this.member) return MessageUtil.warning("用户不存在");
      this.$router.push({ path: option.path, query: { loginid: this.member.loginId } });
    },
    getAnnouncement() {
      this.loading = true;
      service
        .get("/Announcement/GetAnnouncement")
        .then((response) => {
          if (response.status === "succeed") {
            // 保留完整的通告对象，包含priority信息
            this.commendations = response.data.filter((item) => item.type === 1);
            this.notices = response.data.filter((item) => item.type === 2);
          } else {
            MessageUtil.error("获取公告失败");
          }
        })
        .catch(() => {
          MessageUtil.error("获取公告失败");
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 设置相关方法
    async loadSettings() {
      try {
        const response = await getAnnouncementSettings();
        if (response.status === "succeed" && response.data) {
          // 验证获取的设置
          const validation = validateSettings(response.data);
          if (validation.valid) {
            this.announcementSettings = { ...this.announcementSettings, ...response.data };
          } else {
            console.warn("获取的设置无效，使用默认设置:", validation.errors);
            this.announcementSettings = getDefaultSettings();
          }
        } else {
          // 如果API调用失败，使用默认设置
          this.announcementSettings = getDefaultSettings();
        }
      } catch (error) {
        console.error("加载设置失败:", error);
        // 加载失败时使用默认设置，不显示错误消息（避免影响用户体验）
        this.announcementSettings = getDefaultSettings();
      }
    },

    async saveSettings() {
      try {
        // 验证设置
        const validation = validateSettings(this.announcementSettings);
        if (!validation.valid) {
          MessageUtil.error(`设置无效: ${validation.errors.join(", ")}`);
          return;
        }

        const response = await saveAnnouncementSettings(this.announcementSettings);
        if (response.status === "succeed") {
          MessageUtil.success("设置已保存");
          this.showSettings = false;
        } else {
          throw new Error(response.message || "保存失败");
        }
      } catch (error) {
        console.error("保存设置失败:", error);
        MessageUtil.error("保存设置失败");
      }
    },

    resetSettings() {
      this.announcementSettings = getDefaultSettings();
      MessageUtil.info("已重置为默认设置");
    },

    closeSettings() {
      this.showSettings = false;
    },

    // 计算滚动动画持续时间
    calculateScrollDuration(contentType) {
      // 获取内容元素的实际高度
      const contentElement = this.$refs[contentType + "Content"];
      if (!contentElement) return 10; // 默认值

      const contentHeight = contentElement.scrollHeight;
      const containerHeight = this.announcementSettings.scrollHeight;

      // 新的动画逻辑：从容器高度位置滚动到-100%（内容高度）
      // 滚动距离 = 容器高度 + 内容高度
      const scrollDistance = containerHeight + contentHeight;

      // 根据设置的速度（px/s）计算持续时间
      const duration = scrollDistance / this.announcementSettings.scrollSpeed;

      return Math.max(duration, 3); // 最小3秒
    },

    onSettingsChange() {
      // 实时预览效果
      this.settingsPreview = true;
      // 防抖处理
      clearTimeout(this.previewTimer);
      this.previewTimer = setTimeout(() => {
        this.settingsPreview = false;
      }, 100);
    },

    setScrollSpeed(speed) {
      this.announcementSettings.scrollSpeed = speed;
      this.onSettingsChange();
    },

    setScrollHeight(height) {
      this.announcementSettings.scrollHeight = height;
      this.onSettingsChange();
    },
  },
};
</script>

<style lang="scss" scoped>
.assembly-dep-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: 280px; // 为底部公告区域留出空间

  // 移动端优化
  @media (max-width: 768px) {
    padding-bottom: 300px;
  }
}

.header-section {
  text-align: center;
  padding: 20px 16px 30px;
  color: white;

  .page-title {
    font-size: clamp(1.8rem, 4vw, 2.5rem);
    font-weight: bold;
    margin: 0 0 8px 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .page-subtitle {
    font-size: clamp(0.9rem, 2.5vw, 1.1rem);
    margin: 0;
    opacity: 0.9;
  }
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: clamp(12px, 3vw, 20px);
  padding: 0 16px;
  max-width: 600px;
  margin: 0 auto;

  // 移动端优化
  @media (max-width: 480px) {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    padding: 0 12px;
  }

  @media (min-width: 768px) {
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
  }
}

.grid-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: clamp(12px, 3vw, 20px);
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  min-height: 80px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    background: rgba(255, 255, 255, 1);
  }

  &:active {
    transform: translateY(0);
    transition: transform 0.1s ease;
  }

  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;

    &:hover {
      transform: none;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }
}

.icon {
  font-size: clamp(20px, 5vw, 28px);
  margin-bottom: clamp(4px, 1vw, 8px);
  line-height: 1;
}

.label {
  font-size: clamp(11px, 2.5vw, 14px);
  color: #333;
  text-align: center;
  font-weight: 500;
  line-height: 1.2;
}

.lock-icon {
  position: absolute;
  top: 4px;
  right: 4px;
  font-size: 12px;
  opacity: 0.7;
}

.announcement-container {
  display: flex;
  justify-content: space-between;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
  padding: 16px 12px calc(env(safe-area-inset-bottom) + 12px);
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  gap: 12px;

  // 移动端优化
  @media (max-width: 480px) {
    padding: 12px 8px calc(env(safe-area-inset-bottom) + 8px);
    gap: 8px;
  }
}

.announcement {
  flex: 1;
  padding: 12px;
  border: 1px solid rgba(221, 221, 221, 0.5);
  border-radius: 12px;
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
  box-sizing: border-box;
  position: relative;
  max-width: calc(50% - 6px);

  @media (max-width: 480px) {
    padding: 10px;
    border-radius: 8px;
  }
}

.scroll-container {
  height: clamp(120px, 20vh, 180px);
  overflow: hidden;
  position: relative;
  border-top: 1px solid rgba(221, 221, 221, 0.5);
  margin-top: 8px;
  padding-top: 8px;

  @media (max-width: 480px) {
    height: clamp(100px, 18vh, 140px);
  }
}

.scroll-content {
  position: absolute;
  animation: scroll-up 11s linear infinite;
  font-size: clamp(11px, 2.5vw, 13px);
  min-height: 100%;
  width: 100%;

  &:hover {
    animation-play-state: paused;
  }
}

.announcement-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  line-height: 1.4;
  word-break: break-word;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

/* 重要性样式 */
.priority-low {
  background-color: rgba(144, 147, 153, 0.1);
  border-left: 3px solid #909399;
}

.priority-normal {
  background-color: rgba(64, 158, 255, 0.1);
  border-left: 3px solid #409eff;
}

.priority-important {
  background-color: rgba(230, 162, 60, 0.15);
  border-left: 3px solid #e6a23c;
  font-weight: 600;
  animation: pulse-important 2s infinite;
}

.priority-urgent {
  background-color: rgba(245, 108, 108, 0.15);
  border-left: 3px solid #f56c6c;
  font-weight: 700;
  color: #f56c6c;
  animation: pulse-urgent 1.5s infinite;
}

.priority-critical {
  background-color: rgba(255, 0, 0, 0.2);
  border-left: 3px solid #ff0000;
  font-weight: 700;
  color: #ff0000;
  animation: blink-critical 1s infinite;
  box-shadow: 0 0 10px rgba(255, 0, 0, 0.3);
}

.priority-icon {
  margin-right: 4px;
  font-size: 14px;
}

/* 动画效果 */
@keyframes pulse-important {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.02);
    opacity: 0.9;
  }
}

@keyframes pulse-urgent {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 0 5px rgba(245, 108, 108, 0.3);
  }
  50% {
    transform: scale(1.03);
    box-shadow: 0 0 15px rgba(245, 108, 108, 0.5);
  }
}

@keyframes blink-critical {
  0%,
  50% {
    opacity: 1;
    box-shadow: 0 0 10px rgba(255, 0, 0, 0.3);
  }
  25%,
  75% {
    opacity: 0.7;
    box-shadow: 0 0 20px rgba(255, 0, 0, 0.6);
  }
}

.bullet {
  color: #007bff;
  margin-right: 6px;
  font-size: 14px;
  flex-shrink: 0;
  margin-top: 2px;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
  font-size: clamp(11px, 2.5vw, 13px);
  font-style: italic;
}

h3 {
  margin: 0 0 8px;
  font-size: clamp(13px, 3vw, 16px);
  color: #333;
  display: flex;
  align-items: center;
  font-weight: 600;

  .icon {
    margin-right: 6px;
    font-size: clamp(14px, 3.5vw, 18px);
  }
}

@keyframes scroll-up {
  0% {
    transform: translateY(var(--container-height, 120px));
  }
  100% {
    transform: translateY(-100%);
  }
}

// 加载状态
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  color: white;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }

  p {
    margin: 0;
    font-size: 16px;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 设置按钮样式
.settings-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 32px;
  height: 32px;
  background: rgba(102, 126, 234, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;

  i {
    color: white;
    font-size: 16px;
  }

  &:hover {
    background: rgba(102, 126, 234, 1);
    transform: scale(1.1);
  }

  &:active {
    transform: scale(0.95);
  }
}

// 设置面板样式
.settings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  // background: rgba(0, 0, 0, 0.5);
  // backdrop-filter: blur(4px);
  z-index: 1000;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 20px;
}

.settings-panel {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 400px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.settings-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;

  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
  }

  .close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}

.settings-content {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.setting-group {
  margin-bottom: 32px;

  &:last-child {
    margin-bottom: 0;
  }
}

.setting-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .label-text {
    font-size: 16px;
    font-weight: 500;
    color: #333;
  }

  .label-value {
    font-size: 14px;
    color: #666;
    background: #f5f5f5;
    padding: 4px 8px;
    border-radius: 4px;
  }
}

.slider-container {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;

  .slider-label {
    font-size: 12px;
    color: #999;
    min-width: 20px;
    text-align: center;
  }

  input[type="range"] {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: #e0e0e0;
    outline: none;
    -webkit-appearance: none;
    appearance: none;

    &::-webkit-slider-thumb {
      -webkit-appearance: none;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      cursor: pointer;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    }

    &::-moz-range-thumb {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      cursor: pointer;
      border: none;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    }
  }
}

.speed-presets,
.height-presets {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.preset-btn {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 16px;
  background: white;
  color: #666;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: #667eea;
    color: #667eea;
  }

  &.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: transparent;
    color: white;
  }
}

.settings-footer {
  display: flex;
  gap: 12px;
  padding: 20px 24px;
  background: #f8f9fa;
  border-top: 1px solid #eee;

  .reset-btn,
  .save-btn {
    flex: 1;
    padding: 12px 16px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;

    i {
      font-size: 16px;
    }
  }

  .reset-btn {
    background: #f5f5f5;
    color: #666;

    &:hover {
      background: #e0e0e0;
    }
  }

  .save-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }
  }
}

// 移动端优化
@media (max-width: 480px) {
  .settings-overlay {
    padding: 12px;
  }

  .settings-panel {
    border-radius: 12px;
  }

  .settings-header {
    padding: 16px 20px;

    h3 {
      font-size: 16px;
    }
  }

  .settings-content {
    padding: 20px;
  }

  .settings-footer {
    padding: 16px 20px;

    .reset-btn,
    .save-btn {
      padding: 10px 12px;
      font-size: 13px;
    }
  }

  .settings-btn {
    width: 28px;
    height: 28px;
    top: 6px;
    right: 6px;

    i {
      font-size: 14px;
    }
  }
}
</style>
