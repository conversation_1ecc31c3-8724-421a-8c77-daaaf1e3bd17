<template>
  <div class="assembly-dep-container">
    <!-- 顶部标题栏 -->
    <div class="header-section">
      <h1 class="page-title">总装部</h1>
      <p class="page-subtitle">欢迎使用总装部移动工作台</p>
    </div>

    <!-- 功能网格 -->
    <div class="grid-container">
      <template v-for="(option, index) in options">
        <div
          :key="index"
          class="grid-item"
          @click="navigateTo(option)"
          :class="{ disabled: option.permissionAuth && !hasPermission }"
        >
          <div class="icon">{{ option.icon || "🔗" }}</div>
          <div class="label">{{ option.label }}</div>
          <div v-if="option.permissionAuth && !hasPermission" class="lock-icon">🔒</div>
        </div>
      </template>
    </div>

    <!-- 公告区域 -->
    <div class="announcement-container">
      <!-- 设置按钮 -->
      <div v-if="hasPermission" class="settings-btn" @click="showSettings = true">
        <i class="el-icon-setting"></i>
      </div>

      <div class="announcement">
        <h3><span class="icon">🏆</span> 表彰栏</h3>
        <div
          class="scroll-container"
          :style="{ height: announcementSettings.scrollHeight + 'px' }"
          @touchstart="handleTouchStart($event, 'commendations')"
          @touchmove="handleTouchMove($event, 'commendations')"
          @touchend="handleTouchEnd($event, 'commendations')"
        >
          <div
            class="scroll-content"
            v-if="commendations.length"
            :style="{
              transform: `translateY(${scrollState.commendations.currentY}px)`,
              transition:
                scrollState.commendations.isManualScrolling || scrollState.commendations.isResetting
                  ? 'none'
                  : 'transform 0.1s linear',
            }"
            ref="commendationsContent"
          >
            <!-- 第一份内容 -->
            <div
              v-for="(item, index) in commendations"
              :key="`first-${index}`"
              class="announcement-item"
              :class="getPriorityClass(item.priority)"
              @dblclick="showAnnouncementDetail(item)"
            >
              <span class="bullet">•</span>
              <span v-if="item.priority >= 2" class="priority-icon">{{ getPriorityIcon(item.priority) }}</span>
              {{ item.content }}
            </div>

            <!-- 第二份内容（用于无缝循环） -->
            <div
              v-for="(item, index) in commendations"
              :key="`second-${index}`"
              class="announcement-item"
              :class="getPriorityClass(item.priority)"
              @dblclick="showAnnouncementDetail(item)"
            >
              <span class="bullet">•</span>
              <span v-if="item.priority >= 2" class="priority-icon">{{ getPriorityIcon(item.priority) }}</span>
              {{ item.content }}
            </div>
          </div>
          <div v-else class="empty-state">暂无表彰信息</div>
        </div>
      </div>
      <div class="announcement">
        <h3><span class="icon">📢</span> 公示栏</h3>
        <div
          class="scroll-container"
          :style="{ height: announcementSettings.scrollHeight + 'px' }"
          @touchstart="handleTouchStart($event, 'notices')"
          @touchmove="handleTouchMove($event, 'notices')"
          @touchend="handleTouchEnd($event, 'notices')"
        >
          <div
            class="scroll-content"
            v-if="notices.length"
            :style="{
              transform: `translateY(${scrollState.notices.currentY}px)`,
              transition:
                scrollState.notices.isManualScrolling || scrollState.notices.isResetting
                  ? 'none'
                  : 'transform 0.1s linear',
            }"
            ref="noticesContent"
          >
            <!-- 第一份内容 -->
            <div
              v-for="(item, index) in notices"
              :key="`first-${index}`"
              class="announcement-item"
              :class="getPriorityClass(item.priority)"
              @dblclick="showAnnouncementDetail(item)"
            >
              <span class="bullet">•</span>
              <span v-if="item.priority >= 2" class="priority-icon">{{ getPriorityIcon(item.priority) }}</span>
              {{ item.content }}
            </div>

            <!-- 第二份内容（用于无缝循环） -->
            <div
              v-for="(item, index) in notices"
              :key="`second-${index}`"
              class="announcement-item"
              :class="getPriorityClass(item.priority)"
              @dblclick="showAnnouncementDetail(item)"
            >
              <span class="bullet">•</span>
              <span v-if="item.priority >= 2" class="priority-icon">{{ getPriorityIcon(item.priority) }}</span>
              {{ item.content }}
            </div>
          </div>
          <div v-else class="empty-state">暂无公示信息</div>
        </div>
      </div>
    </div>

    <!-- 设置面板 -->
    <div v-if="showSettings" class="settings-overlay" @click="closeSettings">
      <div class="settings-panel" @click.stop>
        <div class="settings-header">
          <h3>公告栏设置</h3>
          <button class="close-btn" @click="closeSettings">
            <i class="el-icon-close"></i>
          </button>
        </div>

        <div class="settings-content">
          <!-- 滚动速度设置 -->
          <div class="setting-group">
            <label class="setting-label">
              <span class="label-text">滚动速度</span>
              <span class="label-value">{{ announcementSettings.scrollSpeed }}px/s</span>
            </label>
            <div class="slider-container">
              <span class="slider-label">慢</span>
              <input
                type="range"
                v-model="announcementSettings.scrollSpeed"
                min="10"
                max="50"
                step="2"
                class="speed-slider"
                @input="onSettingsChange"
              />
              <span class="slider-label">快</span>
            </div>
            <div class="speed-presets">
              <button
                v-for="preset in speedPresets"
                :key="preset.value"
                class="preset-btn"
                :class="{ active: announcementSettings.scrollSpeed == preset.value }"
                @click="setScrollSpeed(preset.value)"
              >
                {{ preset.label }}
              </button>
            </div>
          </div>

          <!-- 滚动区域高度设置 -->
          <div class="setting-group">
            <label class="setting-label">
              <span class="label-text">滚动区域高度</span>
              <span class="label-value">{{ announcementSettings.scrollHeight }}px</span>
            </label>
            <div class="slider-container">
              <span class="slider-label">低</span>
              <input
                type="range"
                v-model="announcementSettings.scrollHeight"
                min="80"
                max="200"
                step="10"
                class="height-slider"
                @input="onSettingsChange"
              />
              <span class="slider-label">高</span>
            </div>
            <div class="height-presets">
              <button
                v-for="preset in heightPresets"
                :key="preset.value"
                class="preset-btn"
                :class="{ active: announcementSettings.scrollHeight == preset.value }"
                @click="setScrollHeight(preset.value)"
              >
                {{ preset.label }}
              </button>
            </div>
          </div>
        </div>

        <div class="settings-footer">
          <button class="reset-btn" @click="resetSettings">
            <i class="el-icon-refresh"></i>
            重置默认
          </button>
          <button class="save-btn" @click="saveSettings">
            <i class="el-icon-check"></i>
            保存设置
          </button>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p>加载中...</p>
    </div>

    <!-- 通告详情对话框 -->
    <el-dialog
      :title="announcementDetail.title || '通告详情'"
      :visible.sync="announcementDetailVisible"
      width="90%"
      :close-on-click-modal="true"
      class="announcement-detail-dialog"
    >
      <div class="announcement-detail-content">
        <div class="detail-header">
          <div class="priority-badge" :class="getPriorityClass(announcementDetail.priority)">
            <span class="priority-icon">{{ getPriorityIcon(announcementDetail.priority) }}</span>
            <span class="priority-text">{{ getPriorityText(announcementDetail.priority) }}</span>
          </div>
          <div class="type-badge">
            {{ announcementDetail.type === 1 ? "表彰" : "公示" }}
          </div>
        </div>

        <div class="detail-content">
          <p>{{ announcementDetail.content }}</p>
        </div>

        <!-- <div class="detail-footer">
          <div class="time-info">
            <div v-if="announcementDetail.startTime">
              <i class="el-icon-time"></i>
              开始时间: {{ formatDateTime(announcementDetail.startTime) }}
            </div>
            <div v-if="announcementDetail.endTime">
              <i class="el-icon-time"></i>
              结束时间: {{ formatDateTime(announcementDetail.endTime) }}
            </div>
          </div>
        </div> -->
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="announcementDetailVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import service from "@/router/request";
import { MessageUtil } from "@/utils/utils";
import {
  getAnnouncementSettings,
  saveAnnouncementSettings,
  getDefaultSettings,
  validateSettings,
} from "@/api/announcementSettings";

export default {
  name: "AssemblyDep",
  data() {
    return {
      loading: false,
      member: {
        loginId: this.$route.query.loginid || this.$store.state.user.loginId || 0,
      },
      options: [
        { label: "工作日报", icon: "📄", path: "/dailyWorkReport", permissionAuth: false },
        { label: "考勤中心", icon: "⏰", path: "/attendanceCenter", permissionAuth: false },
        { label: "部门通告", icon: "📢", path: "/announcement", permissionAuth: true },
        { label: "工资条", icon: "💰", path: "/salarySheet", permissionAuth: false },
        { label: "培训中心", icon: "🎓", path: "/trainingCenter", permissionAuth: false },
        { label: "精益改善", icon: "🛠️", path: "/leanImprovementApp", permissionAuth: false },
        { label: "部门调动", icon: "🔄", path: "/departmentTransfer", permissionAuth: true },
        // 开发测试用，生产环境应移除
        // ...(process.env.NODE_ENV === "development"
        //   ? [
        //       { label: "权限测试", icon: "🔧", path: "/permissionTest", permissionAuth: false },
        //       { label: "设置演示", icon: "🎛️", path: "/announcementDemo", permissionAuth: false },
        //     ]
        //   : []),
      ],
      commendations: [
        "张三同志在本月生产任务中表现突出，获得月度优秀员工称号",
        "李四团队成功完成重要项目，为公司创造显著价值",
        "王五在技术创新方面贡献突出，获得技术创新奖",
        "赵六积极参与公司培训，获得学习进步奖",
        "钱七在安全生产方面表现优异，获得安全标兵称号",
      ],
      notices: [
        "关于调整作息时间的通知：自下周一起，上班时间调整为8:30-17:30",
        "公司将于本周五举行消防演练，请全体员工积极配合",
        "年度体检安排：请各部门按照通知时间安排员工体检",
        "关于规范停车秩序的通知：请将车辆停放在指定区域",
        "食堂菜单更新：新增多种营养搭配套餐，欢迎品尝",
      ],

      // 设置相关
      showSettings: false,
      settingsPreview: false,
      announcementSettings: {
        scrollSpeed: 20, // 滚动速度（px/s）
        scrollHeight: 120, // 滚动区域高度（px）
      },

      // 预设选项
      speedPresets: [
        { label: "极慢", value: 10 },
        { label: "慢速", value: 16 },
        { label: "标准", value: 20 },
        { label: "快速", value: 30 },
        { label: "极快", value: 50 },
      ],
      heightPresets: [
        { label: "紧凑", value: 80 },
        { label: "标准", value: 120 },
        { label: "宽松", value: 160 },
        { label: "超大", value: 200 },
      ],

      // 滚动状态管理
      scrollState: {
        commendations: {
          currentY: 0,
          isManualScrolling: false,
          isResetting: false,
          autoScrollTimer: null,
          contentHeight: 0,
          containerHeight: 120,
        },
        notices: {
          currentY: 0,
          isManualScrolling: false,
          isResetting: false,
          autoScrollTimer: null,
          contentHeight: 0,
          containerHeight: 120,
        },
      },

      // 触摸状态
      touchState: {
        commendations: {
          startY: 0,
          startScrollY: 0,
          isActive: false,
        },
        notices: {
          startY: 0,
          startScrollY: 0,
          isActive: false,
        },
      },

      // 通告详情
      announcementDetailVisible: false,
      announcementDetail: {},
    };
  },
  computed: {
    hasPermission() {
      // 直接访问 store 状态，确保响应式
      const memberRole = this.$store.state.user?.roleId || this.$store.state.user?.roleId || 0;
      return memberRole == 8;
    },
  },
  mounted() {
    // store.state.user.loginId = this.member.loginId;
    this.getMember();
    this.getAnnouncement();
    this.loadSettings();
  },

  beforeDestroy() {
    // 清理定时器
    this.stopAutoScroll("commendations");
    this.stopAutoScroll("notices");
  },
  methods: {
    // 获取重要性样式类
    getPriorityClass(priority) {
      if (!priority) return "";

      const priorityClasses = {
        1: "priority-normal",
        2: "priority-important",
        3: "priority-urgent",
      };

      return priorityClasses[priority] || "";
    },

    // 获取重要性图标
    getPriorityIcon(priority) {
      const priorityIcons = {
        1: "📢",
        2: "⚠️",
        3: "🚨",
      };

      return priorityIcons[priority] || "";
    },

    // 获取重要性文本
    getPriorityText(priority) {
      const priorityTexts = {
        1: "普通",
        2: "重要",
        3: "紧急",
      };

      return priorityTexts[priority] || "普通";
    },

    // 触摸开始
    handleTouchStart(event, type) {
      const touch = event.touches[0];
      this.touchState[type].startY = touch.clientY;
      this.touchState[type].startScrollY = this.scrollState[type].currentY;
      this.touchState[type].isActive = true;

      // 停止自动滚动
      this.stopAutoScroll(type);
      this.scrollState[type].isManualScrolling = true;
    },

    // 触摸移动
    handleTouchMove(event, type) {
      if (!this.touchState[type].isActive) return;

      event.preventDefault();
      const touch = event.touches[0];
      const deltaY = touch.clientY - this.touchState[type].startY;

      let currentY = this.touchState[type].startScrollY + deltaY;
      // 从触摸开始时的滚动位置计算新位置
      this.scrollState[type].currentY = currentY > 100 ? 100 : currentY;
    },

    // 触摸结束
    handleTouchEnd(_, type) {
      this.touchState[type].isActive = false;
      this.scrollState[type].isManualScrolling = false;

      // 立即从当前位置恢复自动滚动
      this.startAutoScroll(type);
    },

    // 开始自动滚动
    startAutoScroll(type) {
      this.stopAutoScroll(type); // 先清除之前的定时器

      const scroll = () => {
        if (this.scrollState[type].isManualScrolling) return;

        // 计算滚动速度 (px per frame)
        const speed = this.announcementSettings.scrollSpeed / 60; // 60fps
        this.scrollState[type].currentY -= speed;

        // 检查是否需要重置位置 - 无缝循环
        const contentHeight = this.scrollState[type].contentHeight;
        const containerHeight = this.scrollState[type].containerHeight;

        // 当内容完全滚出视野时，无缝重置到起始位置
        if (this.scrollState[type].currentY <= -contentHeight) {
          // 设置重置标志，禁用过渡动画
          this.scrollState[type].isResetting = true;

          // 无缝重置：直接跳转到起始位置
          this.scrollState[type].currentY = containerHeight;

          // 下一帧恢复过渡动画
          requestAnimationFrame(() => {
            this.scrollState[type].isResetting = false;
          });
        }

        this.scrollState[type].autoScrollTimer = requestAnimationFrame(scroll);
      };

      this.scrollState[type].autoScrollTimer = requestAnimationFrame(scroll);
    },

    // 停止自动滚动
    stopAutoScroll(type) {
      if (this.scrollState[type].autoScrollTimer) {
        cancelAnimationFrame(this.scrollState[type].autoScrollTimer);
        this.scrollState[type].autoScrollTimer = null;
      }
    },

    // 初始化滚动
    initializeScroll() {
      this.$nextTick(() => {
        // 计算内容高度
        ["commendations", "notices"].forEach((type) => {
          const contentEl = this.$refs[`${type}Content`];
          if (contentEl) {
            // 停止之前的滚动
            this.stopAutoScroll(type);

            // 重新计算尺寸
            this.scrollState[type].contentHeight = contentEl.scrollHeight;
            this.scrollState[type].containerHeight = this.announcementSettings.scrollHeight;

            // 设置初始位置（无过渡）
            this.scrollState[type].isResetting = true;
            this.scrollState[type].currentY = this.scrollState[type].containerHeight;

            // 下一帧开始滚动并恢复过渡
            requestAnimationFrame(() => {
              this.scrollState[type].isResetting = false;
              this.startAutoScroll(type);
            });
          }
        });
      });
    },

    // 显示通告详情
    showAnnouncementDetail(item) {
      this.announcementDetail = { ...item };
      this.announcementDetailVisible = true;
    },

    // 格式化日期时间
    formatDateTime(dateString) {
      if (!dateString) return "";
      const date = new Date(dateString);
      return date.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
      });
    },

    getMember() {
      service.get(`/Member/getMember/${this.member.loginId}`).then((res) => {
        if (res.status == "succeed") {
          if (!res.data) MessageUtil.warning("未查询到用户信息");
          this.member = res.data;
          // 使用 mutation 更新 store 状态，确保响应式
          this.$store.commit("updatePersonInfo", this.member);
          localStorage.setItem("person", JSON.stringify({ token: "", info: this.member }));
        }
      });
    },
    navigateTo(option) {
      if (option.permissionAuth && !this.hasPermission) {
        return MessageUtil.warning("暂无权限");
      }
      if (!this.member) return MessageUtil.warning("用户不存在");
      this.$router.push({ path: option.path, query: { loginid: this.member.loginId } });
    },
    getAnnouncement() {
      this.loading = true;
      service
        .get("/Announcement/GetAnnouncement")
        .then((response) => {
          if (response.status === "succeed") {
            // 保留完整的通告对象，包含priority信息
            this.commendations = response.data.filter((item) => item.type === 1);
            this.notices = response.data.filter((item) => item.type === 2);

            // 数据加载完成后初始化滚动
            this.initializeScroll();
          } else {
            MessageUtil.error("获取公告失败");
          }
        })
        .catch(() => {
          MessageUtil.error("获取公告失败");
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 设置相关方法
    async loadSettings() {
      try {
        const response = await getAnnouncementSettings();
        if (response.status === "succeed" && response.data) {
          // 验证获取的设置
          const validation = validateSettings(response.data);
          if (validation.valid) {
            this.announcementSettings = { ...this.announcementSettings, ...response.data };
          } else {
            console.warn("获取的设置无效，使用默认设置:", validation.errors);
            this.announcementSettings = getDefaultSettings();
          }
        } else {
          // 如果API调用失败，使用默认设置
          this.announcementSettings = getDefaultSettings();
        }
      } catch (error) {
        console.error("加载设置失败:", error);
        // 加载失败时使用默认设置，不显示错误消息（避免影响用户体验）
        this.announcementSettings = getDefaultSettings();
      }
    },

    async saveSettings() {
      try {
        // 验证设置
        const validation = validateSettings(this.announcementSettings);
        if (!validation.valid) {
          MessageUtil.error(`设置无效: ${validation.errors.join(", ")}`);
          return;
        }

        const response = await saveAnnouncementSettings(this.announcementSettings);
        if (response.status === "succeed") {
          MessageUtil.success("设置已保存");
          this.showSettings = false;

          // 设置变化后重新初始化滚动
          this.initializeScroll();
        } else {
          throw new Error(response.message || "保存失败");
        }
      } catch (error) {
        console.error("保存设置失败:", error);
        MessageUtil.error("保存设置失败");
      }
    },

    resetSettings() {
      this.announcementSettings = getDefaultSettings();
      MessageUtil.info("已重置为默认设置");
    },

    closeSettings() {
      this.showSettings = false;
    },

    onSettingsChange() {
      // 实时预览效果
      this.settingsPreview = true;
      // 防抖处理
      clearTimeout(this.previewTimer);
      this.previewTimer = setTimeout(() => {
        this.settingsPreview = false;
      }, 100);
    },

    setScrollSpeed(speed) {
      this.announcementSettings.scrollSpeed = speed;
      this.onSettingsChange();
    },

    setScrollHeight(height) {
      this.announcementSettings.scrollHeight = height;
      this.onSettingsChange();
    },
  },
};
</script>

<style lang="scss" scoped>
.assembly-dep-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: 280px; // 为底部公告区域留出空间

  // 移动端优化
  @media (max-width: 768px) {
    padding-bottom: 300px;
  }
}

.header-section {
  text-align: center;
  padding: 20px 16px 30px;
  color: white;

  .page-title {
    font-size: clamp(1.8rem, 4vw, 2.5rem);
    font-weight: bold;
    margin: 0 0 8px 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .page-subtitle {
    font-size: clamp(0.9rem, 2.5vw, 1.1rem);
    margin: 0;
    opacity: 0.9;
  }
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: clamp(12px, 3vw, 20px);
  padding: 0 16px;
  max-width: 600px;
  margin: 0 auto;

  // 移动端优化
  @media (max-width: 480px) {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    padding: 0 12px;
  }

  @media (min-width: 768px) {
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
  }
}

.grid-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: clamp(12px, 3vw, 20px);
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  min-height: 80px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    background: rgba(255, 255, 255, 1);
  }

  &:active {
    transform: translateY(0);
    transition: transform 0.1s ease;
  }

  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;

    &:hover {
      transform: none;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }
}

.icon {
  font-size: clamp(20px, 5vw, 28px);
  margin-bottom: clamp(4px, 1vw, 8px);
  line-height: 1;
}

.label {
  font-size: clamp(11px, 2.5vw, 14px);
  color: #333;
  text-align: center;
  font-weight: 500;
  line-height: 1.2;
}

.lock-icon {
  position: absolute;
  top: 4px;
  right: 4px;
  font-size: 12px;
  opacity: 0.7;
}

.announcement-container {
  display: flex;
  justify-content: space-between;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
  padding: 16px 12px calc(env(safe-area-inset-bottom) + 12px);
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  gap: 12px;

  // 移动端优化
  @media (max-width: 480px) {
    padding: 12px 8px calc(env(safe-area-inset-bottom) + 8px);
    gap: 8px;
  }
}

.announcement {
  flex: 1;
  padding: 12px;
  border: 1px solid rgba(221, 221, 221, 0.5);
  border-radius: 12px;
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
  box-sizing: border-box;
  position: relative;
  max-width: calc(50% - 6px);

  @media (max-width: 480px) {
    padding: 10px;
    border-radius: 8px;
  }
}

.scroll-container {
  height: clamp(120px, 20vh, 180px);
  overflow: hidden;
  position: relative;
  border-top: 1px solid rgba(221, 221, 221, 0.5);
  margin-top: 8px;
  padding-top: 8px;

  @media (max-width: 480px) {
    height: clamp(100px, 18vh, 140px);
  }
}

.scroll-content {
  position: absolute;
  font-size: clamp(11px, 2.5vw, 13px);
  min-height: 100%;
  width: 100%;
}

.announcement-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  line-height: 1.4;
  word-break: break-word;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

/* 重要性样式 */
.priority-normal {
  background-color: rgba(64, 158, 255, 0.1);
  border-left: 3px solid #409eff;
}

.priority-important {
  background-color: rgba(230, 162, 60, 0.15);
  border-left: 3px solid #e6a23c;
  font-weight: 600;
  animation: pulse-important 2s infinite;
}

.priority-urgent {
  background-color: rgba(245, 108, 108, 0.15);
  border-left: 3px solid #f56c6c;
  font-weight: 700;
  color: #f56c6c;
  animation: pulse-urgent 1.5s infinite;
}

.priority-icon {
  margin-right: 4px;
  font-size: 14px;
}

/* 动画效果 */
@keyframes pulse-important {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.02);
    opacity: 0.9;
  }
}

@keyframes pulse-urgent {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 0 5px rgba(245, 108, 108, 0.3);
  }
  50% {
    transform: scale(1.03);
    box-shadow: 0 0 15px rgba(245, 108, 108, 0.5);
  }
}

.bullet {
  color: #007bff;
  margin-right: 6px;
  font-size: 14px;
  flex-shrink: 0;
  margin-top: 2px;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
  font-size: clamp(11px, 2.5vw, 13px);
  font-style: italic;
}

h3 {
  margin: 0 0 8px;
  font-size: clamp(13px, 3vw, 16px);
  color: #333;
  display: flex;
  align-items: center;
  font-weight: 600;

  .icon {
    margin-right: 6px;
    font-size: clamp(14px, 3.5vw, 18px);
  }
}

// 加载状态
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  color: white;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }

  p {
    margin: 0;
    font-size: 16px;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 设置按钮样式
.settings-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 32px;
  height: 32px;
  background: rgba(102, 126, 234, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;

  i {
    color: white;
    font-size: 16px;
  }

  &:hover {
    background: rgba(102, 126, 234, 1);
    transform: scale(1.1);
  }

  &:active {
    transform: scale(0.95);
  }
}

// 设置面板样式
.settings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  // background: rgba(0, 0, 0, 0.5);
  // backdrop-filter: blur(4px);
  z-index: 1000;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 20px;
}

.settings-panel {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 400px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.settings-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;

  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
  }

  .close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}

.settings-content {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.setting-group {
  margin-bottom: 32px;

  &:last-child {
    margin-bottom: 0;
  }
}

.setting-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .label-text {
    font-size: 16px;
    font-weight: 500;
    color: #333;
  }

  .label-value {
    font-size: 14px;
    color: #666;
    background: #f5f5f5;
    padding: 4px 8px;
    border-radius: 4px;
  }
}

.slider-container {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;

  .slider-label {
    font-size: 12px;
    color: #999;
    min-width: 20px;
    text-align: center;
  }

  input[type="range"] {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: #e0e0e0;
    outline: none;
    -webkit-appearance: none;
    appearance: none;

    &::-webkit-slider-thumb {
      -webkit-appearance: none;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      cursor: pointer;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    }

    &::-moz-range-thumb {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      cursor: pointer;
      border: none;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    }
  }
}

.speed-presets,
.height-presets {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.preset-btn {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 16px;
  background: white;
  color: #666;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: #667eea;
    color: #667eea;
  }

  &.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: transparent;
    color: white;
  }
}

.settings-footer {
  display: flex;
  gap: 12px;
  padding: 20px 24px;
  background: #f8f9fa;
  border-top: 1px solid #eee;

  .reset-btn,
  .save-btn {
    flex: 1;
    padding: 12px 16px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;

    i {
      font-size: 16px;
    }
  }

  .reset-btn {
    background: #f5f5f5;
    color: #666;

    &:hover {
      background: #e0e0e0;
    }
  }

  .save-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }
  }
}

// 移动端优化
@media (max-width: 480px) {
  .settings-overlay {
    padding: 12px;
  }

  .settings-panel {
    border-radius: 12px;
  }

  .settings-header {
    padding: 16px 20px;

    h3 {
      font-size: 16px;
    }
  }

  .settings-content {
    padding: 20px;
  }

  .settings-footer {
    padding: 16px 20px;

    .reset-btn,
    .save-btn {
      padding: 10px 12px;
      font-size: 13px;
    }
  }

  .settings-btn {
    width: 28px;
    height: 28px;
    top: 6px;
    right: 6px;

    i {
      font-size: 14px;
    }
  }
}

/* 通告详情对话框样式 */
.announcement-detail-dialog {
  .el-dialog__body {
    padding: 20px;
  }
}

.announcement-detail-content {
  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
  }

  .priority-badge {
    display: flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;

    .priority-icon {
      margin-right: 6px;
    }
  }

  .type-badge {
    background-color: #f0f9ff;
    color: #1e40af;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
  }

  .detail-content {
    overflow: auto;
    max-height: 200px;
    margin-bottom: 20px;

    p {
      line-height: 1.6;
      color: #303133;
      font-size: 16px;
      margin: 0;
      white-space: pre-wrap;
      word-break: break-word;
    }
  }

  .detail-footer {
    .time-info {
      color: #909399;
      font-size: 14px;

      div {
        margin-bottom: 8px;
        display: flex;
        align-items: center;

        i {
          margin-right: 8px;
          color: #409eff;
        }
      }
    }
  }
}

.dialog-footer {
  text-align: center;
}

/* 触摸滑动时的样式 */
.scroll-container {
  user-select: none;
  -webkit-user-select: none;
  touch-action: pan-y;
}

/* 双击提示 */
.announcement-item {
  cursor: pointer;
  user-select: none;
  -webkit-user-select: none;

  &:active {
    background-color: rgba(64, 158, 255, 0.1);
  }
}
</style>
