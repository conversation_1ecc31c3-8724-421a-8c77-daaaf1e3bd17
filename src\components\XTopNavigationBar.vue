<template>
  <header class="bg-primary shadow-md z-10 sticky top-0">
    <div class="container mx-auto px-4 py-3 flex items-center justify-between backdrop-filter backdrop-blur-md">
      <div class="flex items-center">
        <button @click="goBack" class="mr-4 text-xl">
          <i class="fa fa-arrow-left"></i>
        </button>
        <h1 class="text-xl font-bold">{{ title }}</h1>
      </div>
      <slot name="tool-bar"></slot>
    </div>
  </header>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: "",
    },
  },
  data: () => ({}),
  methods: {
    goBack() {
      this.$router.back();
    },
  },
};
</script>
