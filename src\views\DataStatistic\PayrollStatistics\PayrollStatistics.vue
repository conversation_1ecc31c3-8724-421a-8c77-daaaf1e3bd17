<template>
  <div class="container">
    <!-- <h2>薪资统计</h2> -->
    <div v-if="path.length > 1" class="mb-2 top-bar">
      <el-button @click="path.pop()" size="medium" icon="el-icon-back" circle></el-button>
    </div>
    <div class="search-bar">
      <div class="search-item">
        <span class="label">截止月份：</span>
        <el-date-picker
          v-model="month"
          type="month"
          placeholder="选择月份"
          value-format="yyyy-MM"
          @change="getPayrollStatisticData"
        />
      </div>
    </div>

    <!-- 折线图 -->
    <div id="chart" style="width: 100%; height: 400px"></div>

    <!-- Table height="350"-->
    <el-table :data="data" border show-summary>
      <el-table-column label="巴组" prop="name" align="center">
        <template v-slot="scope">
          <el-link @click="clickName(scope.row)" type="primary" :underline="false">{{ scope.row.name }}</el-link>
        </template>
      </el-table-column>
      <el-table-column v-for="m of months" :label="m" :prop="m" :key="m"></el-table-column>
    </el-table>
  </div>
</template>

<script>
import service from "@/router/request";
import * as echarts from "echarts";
import { generateColors } from "@/utils/utils";

export default {
  name: "PayrollStatistic",

  data: () => ({
    month: "",
    data: [],
    path: [{ type: 1 }],
    chart: null,
  }),

  computed: {
    months() {
      let m = this.$moment(this.month).add(-1, "y");
      let ms = [];
      do {
        m = m.add(1, "M");
        ms.push(m.format("yyyy-MM"));
      } while (m.format("yyyy-MM") < this.month);

      return ms;
    },
  },

  watch: {
    path() {
      this.getPayrollStatisticData();
    },
  },

  mounted() {
    this.month = this.$moment().format("yyyy-MM");
    this.getPayrollStatisticData();
  },

  methods: {
    getPayrollStatisticData() {
      service
        .post("/DataSummary/getPayrollStatisticData", { month: this.month, ...this.path[this.path.length - 1] })
        .then((res) => {
          if (res.status == "succeed") {
            this.data = res.data || [];

            // 折线图
            if (this.chart) this.chart.dispose();
            this.chart = echarts.init(document.getElementById("chart"));
            this.chart.on("updateAxisPointer", (event) => {
              const xAxisInfo = event.axesInfo[0];
              if (xAxisInfo) {
                const dimension = xAxisInfo.value + 1;
                this.chart.setOption({
                  series: {
                    id: "pie",
                    label: {
                      formatter: "{b}: {@[" + dimension + "]} ({d}%)",
                    },
                    encode: {
                      value: dimension,
                      tooltip: dimension,
                    },
                  },
                });
              }
            });
            let color = generateColors(this.data.length);
            this.chart.setOption({
              color,
              legend: {},
              tooltip: {
                trigger: "axis",
                showContent: false,
              },
              dataset: {
                source: [
                  ["name", ...this.months],
                  ...this.data.map((item) => [item.name, ...this.months.map((m) => item[m])]),
                ],
              },
              xAxis: { type: "category" },
              yAxis: { gridIndex: 0 },
              grid: {
                top: "55%",
                bottom: "5%",
                containLabel: true,
              },
              series: [
                {
                  type: "pie",
                  id: "pie",
                  radius: "30%",
                  center: ["50%", "30%"],
                  emphasis: {
                    focus: "self",
                  },
                  label: {
                    formatter: `{b}：{@${this.months[0]}}（{d}%）`,
                  },
                  encode: {
                    itemName: "name",
                    value: `${this.months[0]}`,
                    tooltip: `${this.months[0]}`,
                  },
                },
                ...this.data.map((item, index) => ({
                  type: "line",
                  seriesLayoutBy: "row",
                  emphasis: { focus: "series" },
                  lineStyle: { color: color[index % color.length] },
                  itemStyle: { color: color[index % color.length] },
                })),
              ],
            });
          }
        });
    },

    clickName(row) {
      if (!+row.no) this.path.push({ type: row.no.includes("GRO") ? 2 : 3, no: row.no });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  .top-bar {
    display: flex;
    justify-content: space-between;
    position: absolute;
    top: 0;
    z-index: 1;
  }
}

.el-table {
  :deep(.el-table__cell) {
    padding: 0 0;
  }
}
</style>
