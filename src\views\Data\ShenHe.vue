<template>
  <div>
    <el-table :data="tableData">
      <el-table-column label="机型名称" prop="mmoname"></el-table-column>
      <el-table-column label="机型型号" prop="mmomodel"></el-table-column>
      <el-table-column label="机型备注" prop="mmoremarks"></el-table-column>
      <el-table-column label="当前状态" prop="mmosflagstatus"></el-table-column>
      <el-table-column label="操作">
        <template v-slot:default="scope">
          <el-tooltip :content="'暂无权限'" :disabled="havePermissionRole(8)">
            <el-button type="primary" plain @click="Auditbutton(scope.row)" :disabled="!havePermissionRole(8)">审核</el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import service from "@/router/request";
import { havePermissionRole } from "@/utils/permission";

export default {
  name: "shenhe",
  data() {
    return {
      tableData: []
    };
  },

  activated() {
    this.selectAudit();
  },
  methods: {
    havePermissionRole,
    selectAudit() {
      service.get("/ShenHe/Getmmodetails").then(res => {
        if (res.status == "succeed") {
          this.tableData = res.data;
        }
      });
    },

    Auditbutton(row) {
      localStorage.setItem("rowData", JSON.stringify(row));
      // 跳转到详情页面
      this.$router.push({ path: "/shenheaudit" });
    }
  }
};
</script>

<style scoped>
</style>
