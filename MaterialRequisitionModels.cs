using System;
using System.ComponentModel.DataAnnotations;

namespace TaskManagement.Models
{
    // 创建领用单模型
    public class MaterialRequisitionCreateModel
    {
        [Required(ErrorMessage = "申请人姓名不能为空")]
        public string ApplicantName { get; set; }

        [Required(ErrorMessage = "领用类型不能为空")]
        [Range(1, 2, ErrorMessage = "领用类型必须为1(新领)或2(以旧换新)")]
        public int RequisitionType { get; set; }

        [Required(ErrorMessage = "物品ID不能为空")]
        public int MaterialId { get; set; }

        [Required(ErrorMessage = "物品名称不能为空")]
        public string MaterialName { get; set; }

        [Required(ErrorMessage = "数量不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "数量必须大于0")]
        public int Quantity { get; set; }

        [Required(ErrorMessage = "单位不能为空")]
        public string Unit { get; set; }

        [Required(ErrorMessage = "用途说明不能为空")]
        [StringLength(500, MinimumLength = 5, ErrorMessage = "用途说明长度在5-500个字符")]
        public string Purpose { get; set; }

        // 以旧换新时的旧物品状态：1=损坏, 2=磨损, 3=过期, 0=其他
        public int? OldMaterialCondition { get; set; }

        // 以旧换新时的旧物品描述
        [StringLength(500, ErrorMessage = "旧物品描述不能超过500个字符")]
        public string OldMaterialDescription { get; set; }

        [StringLength(500, ErrorMessage = "备注不能超过500个字符")]
        public string Remark { get; set; }

        public string Attachments { get; set; }
    }

    // 查询领用单模型
    public class MaterialRequisitionQueryModel
    {
        public int? Status { get; set; }
        public string Search { get; set; }
        public string SortField { get; set; }
        public string SortOrder { get; set; }
    }

    // 审核领用单模型
    public class MaterialRequisitionApproveModel
    {
        [Required(ErrorMessage = "领用单ID不能为空")]
        public int Id { get; set; }

        [Required(ErrorMessage = "审核结果不能为空")]
        [Range(2, 3, ErrorMessage = "审核结果必须为2(通过)或3(拒绝)")]
        public int Result { get; set; }

        [Required(ErrorMessage = "审核意见不能为空")]
        [StringLength(500, MinimumLength = 5, ErrorMessage = "审核意见长度在5-500个字符")]
        public string Remark { get; set; }
    }

    // 确认发放模型
    public class MaterialRequisitionDistributionModel
    {
        [Required(ErrorMessage = "领用单ID不能为空")]
        public int Id { get; set; }
    }

    // 更新领用单模型
    public class MaterialRequisitionUpdateModel
    {
        [Required(ErrorMessage = "领用单ID不能为空")]
        public int Id { get; set; }

        [Required(ErrorMessage = "领用类型不能为空")]
        [Range(1, 2, ErrorMessage = "领用类型必须为1(新领)或2(以旧换新)")]
        public int RequisitionType { get; set; }

        [Required(ErrorMessage = "物品ID不能为空")]
        public int MaterialId { get; set; }

        [Required(ErrorMessage = "数量不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "数量必须大于0")]
        public int Quantity { get; set; }

        [Required(ErrorMessage = "用途说明不能为空")]
        [StringLength(500, MinimumLength = 5, ErrorMessage = "用途说明长度在5-500个字符")]
        public string Purpose { get; set; }

        // 以旧换新时的旧物品状态：1=损坏, 2=磨损, 3=过期, 0=其他
        public int? OldMaterialCondition { get; set; }

        // 以旧换新时的旧物品描述
        [StringLength(500, ErrorMessage = "旧物品描述不能超过500个字符")]
        public string OldMaterialDescription { get; set; }

        [StringLength(500, ErrorMessage = "备注不能超过500个字符")]
        public string Remark { get; set; }
    }

    // 删除领用单模型
    public class MaterialRequisitionDeleteModel
    {
        [Required(ErrorMessage = "领用单ID不能为空")]
        public int Id { get; set; }
    }

    // 领用单视图模型
    public class MaterialRequisitionViewModel
    {
        public int Id { get; set; }
        public string ApplicantId { get; set; }
        public string ApplicantName { get; set; }
        public int RequisitionType { get; set; }
        public int MaterialId { get; set; }
        public string MaterialName { get; set; }
        public int Quantity { get; set; }
        public string Unit { get; set; }
        public string Purpose { get; set; }
        public int? OldMaterialCondition { get; set; }
        public string OldMaterialDescription { get; set; }
        public string Remark { get; set; }
        public string Attachments { get; set; }
        public int Status { get; set; }
        public DateTime CreateTime { get; set; }
        public DateTime? ApproveTime { get; set; }
        public string ApproveRemark { get; set; }
        public string ApproverId { get; set; }
        public string ApproverName { get; set; }
        public DateTime? DistributionTime { get; set; }
        public string DistributorId { get; set; }
        public string DistributorName { get; set; }
    }

    // 统计数据模型
    public class MaterialRequisitionStatsModel
    {
        public int Pending { get; set; }           // 待审核
        public int Approved { get; set; }          // 已通过
        public int Rejected { get; set; }          // 已拒绝
        public int Distributed { get; set; }       // 已发放
        public int Total { get; set; }             // 总计
    }
}
