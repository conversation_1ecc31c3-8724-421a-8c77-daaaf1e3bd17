const { defineConfig } = require("@vue/cli-service");
let timeStamp = new Date().getTime();

module.exports = defineConfig({
  transpileDependencies: true,
  // 表示禁用保存时的代码风格检查（linting）
  lintOnSave: false,
  filenameHashing: false,
  configureWebpack: {
    devtool: "nosources-source-map",
    output: {
      // ignoreOrder: true,
      filename: `js/js[name].${timeStamp}.js`,
      chunkFilename: `js/chunk.[id].${timeStamp}.js`,
    },
  },
  css: {
    extract: {
      // 打包后css文件名称添加时间戳
      filename: `css/[name].${timeStamp}.css`,
      chunkFilename: `css/chunk.[id].${timeStamp}.css`,
    },
    loaderOptions: {
      scss: {
        additionalData: `@import '@/styles/variable.scss';`,
      },
      // postcss: {
      //   postcssOptions: {
      //     plugins: [require("tailwindcss"), require("autoprefixer")],
      //   },
      // },
    },
  },
});
