<template>
  <div v-if="member && groupList.length">
    <CompositePage v-if="showComposite()" :loginid="member.loginid"></CompositePage>
    <DailyWorkReport v-else :loginid="member.loginid"></DailyWorkReport>
  </div>
  <span v-else>加载中。。。</span>
</template>

<script>
import service from "@/router/request";
import GroupMembers from "./GroupMembers.vue";
import CompositePage from "./CompositePage.vue";
import DailyWorkReport from "./DailyWorkReport.vue";
import store from "@/store";

export default {
  name: "dailyWorkIndex",
  components: { GroupMembers, DailyWorkReport, CompositePage },

  data() {
    return {
      // loginid: this.$route.query.loginid || store.state.user.loginId,
      member: {
        loginid: this.$route.query.loginid || store.state.user.loginId || 0,
      },
      groupList: [],
    };
  },

  mounted() {
    this.getMember();
    this.getGroupList();
  },

  methods: {
    getMember() {
      service
        .get("/DailyReport/getMember", {
          params: { loginId: this.member.loginid },
        })
        .then((res) => {
          if (res.status == "succeed") {
            res.data ? (this.member = { ...res.data, name: res.data.lastname }) : this.member;
            localStorage.setItem("person", JSON.stringify({ token: "", info: this.member }));
          }
        });
    },

    getGroupList() {
      service.get("/DailyReport/getGroups").then((res) => {
        if (res.status == "succeed") {
          this.groupList = res.data;
        }
      });
    },

    showComposite() {
      return (
        this.groupList.some(
          (item) =>
            item.groheadman == this.member.loginid ||
            item?.teamList.some((team) => team.groheadman == this.member.loginid)
        ) ||
        (this.member.role_id && (this.member.role_id == 8 || this.member.role_id == 2))
      );
    },
  },
};
</script>

<style></style>
