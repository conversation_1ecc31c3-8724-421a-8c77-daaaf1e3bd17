<template>
  <div v-if="groupList.length">
    <CompositePage v-if="showComposite()" :loginid="$store.state.user.loginId"></CompositePage>
    <DailyWorkReport v-else :loginid="$store.state.user.loginId"></DailyWorkReport>
  </div>
  <span v-else>加载中。。。</span>
</template>

<script>
import service from "@/router/request";
import GroupMembers from "./GroupMembers.vue";
import CompositePage from "./CompositePage.vue";
import DailyWorkReport from "./DailyWorkReport.vue";

export default {
  name: "dailyWorkIndex",
  components: { GroupMembers, DailyWorkReport, CompositePage },

  data() {
    return {
      groupList: [],
    };
  },

  mounted() {
    this.getGroupList();
  },

  methods: {
    getGroupList() {
      service.get("/DailyReport/getGroups").then((res) => {
        if (res.status == "succeed") {
          this.groupList = res.data;
        }
      });
    },

    showComposite() {
      return (
        this.groupList.some(
          (item) =>
            item.groheadman == this.$store.state.user.loginId ||
            item?.teamList.some((team) => team.groheadman == this.$store.state.user.loginId)
        ) ||
        (this.$store.state.user.roleId && (this.$store.state.user.roleId == 8 || this.$store.state.user.roleId == 2))
      );
    },
  },
};
</script>

<style></style>
