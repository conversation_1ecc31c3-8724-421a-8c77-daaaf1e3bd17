<template>
  <div class="lean-improvement-container">
    <!-- 搜索栏 -->
    <div class="search-section">
      <el-card class="search-card">
        <div class="search-form">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-input
                v-model="searchParams.title"
                placeholder="请输入提案标题"
                clearable
                @keyup.enter.native="handleSearch"
              >
                <template slot="prepend">标题</template>
              </el-input>
            </el-col>
            <el-col :span="6">
              <el-input
                v-model="searchParams.proposer"
                placeholder="请输入提案人"
                clearable
                @keyup.enter.native="handleSearch"
              >
                <template slot="prepend">提案人</template>
              </el-input>
            </el-col>
            <el-col :span="6">
              <el-select v-model="searchParams.status" placeholder="请选择状态" clearable style="width: 100%">
                <el-option
                  v-for="option in statusOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-button type="primary" @click="handleSearch" icon="el-icon-search"> 查询 </el-button>
              <el-button @click="handleReset" icon="el-icon-refresh"> 重置 </el-button>
            </el-col>
          </el-row>

          <!-- 日期范围选择 -->
          <el-row :gutter="20" style="margin-top: 15px">
            <el-col :span="12">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                style="width: 100%"
                @change="handleDateChange"
              />
            </el-col>
            <el-col :span="12">
              <div class="statistics-info">
                <span class="stat-item">
                  <i class="el-icon-document"></i>
                  总计: {{ pagination.total }} 条
                </span>
                <span class="stat-item">
                  <i class="el-icon-check"></i>
                  已处理: {{ statistics.processed || 0 }} 条
                </span>
                <span class="stat-item">
                  <i class="el-icon-time"></i>
                  待处理: {{ statistics.pending || 0 }} 条
                </span>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>

    <!-- 提案列表表格 -->
    <div class="table-section">
      <el-card class="table-card">
        <div slot="header" class="card-header">
          <span class="card-title">
            <i class="el-icon-s-order"></i>
            精益改善提案列表
          </span>
          <div class="header-actions">
            <el-button size="small" type="primary" @click="handleRefresh" icon="el-icon-refresh"> 刷新 </el-button>
          </div>
        </div>

        <el-table
          v-loading="loading"
          :data="proposalList"
          border
          stripe
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />

          <el-table-column prop="proposalNo" label="提案编号" width="140" show-overflow-tooltip />

          <el-table-column prop="title" label="提案标题" min-width="200" show-overflow-tooltip />

          <el-table-column prop="proposerName" label="提案人" width="100" align="center" />

          <el-table-column prop="contact" label="联系方式" width="120" align="center" />

          <el-table-column label="紧急程度" width="100" align="center">
            <template slot-scope="scope">
              <el-tag :type="getUrgencyType(scope.row.urgency)" size="small">
                {{ getUrgencyText(scope.row.urgency) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="难度等级" width="100" align="center">
            <template slot-scope="scope">
              <el-tag :type="getDifficultyType(scope.row.difficulty)" size="small">
                {{ getDifficultyText(scope.row.difficulty) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="100" align="center">
            <template slot-scope="scope">
              <el-tag :type="getStatusType(scope.row.status)" size="small">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            prop="implementationDepartment"
            label="实施部门"
            width="120"
            align="center"
            show-overflow-tooltip
          />

          <el-table-column prop="finishDate" label="完成日期" width="120" align="center">
            <template slot-scope="scope">
              {{ scope.row.finishDate ? formatDate(scope.row.finishDate).substring(0, 10) : "-" }}
            </template>
          </el-table-column>

          <el-table-column prop="createdAt" label="创建时间" width="160" align="center">
            <template slot-scope="scope">
              {{ formatDate(scope.row.createdAt) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="160" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button type="primary" size="mini" @click="handleProcess(scope.row)" icon="el-icon-edit">
                处理
              </el-button>
              <el-button
                type="success"
                size="mini"
                @click="handlePrint(scope.row)"
                icon="el-icon-printer"
                style="margin-left: 5px"
              >
                打印
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
          />
        </div>
      </el-card>
    </div>

    <!-- 处理提案对话框 -->
    <ProcessProposalDialog
      :visible.sync="processDialogVisible"
      :proposal="currentProposal"
      @success="handleProcessSuccess"
    />
  </div>
</template>

<script>
import { getProposalList, getStatusOptions, getUrgencyOptions, getDifficultyOptions } from "@/api/improvementProposal";
import ProcessProposalDialog from "./components/ProcessProposalDialog.vue";
import { IMPROVEMENT_TAGS } from "@/utils/constants";

export default {
  name: "LeanImprovement",
  components: {
    ProcessProposalDialog,
  },
  data() {
    return {
      loading: false,
      // 搜索参数
      searchParams: {
        title: "",
        proposer: "",
        status: "",
      },
      // 日期范围
      dateRange: [],
      // 提案列表
      proposalList: [],
      // 选中的提案
      selectedProposals: [],
      // 分页信息
      pagination: {
        page: 1,
        pageSize: 10,
        total: 0,
      },
      // 统计信息
      statistics: {
        processed: 0,
        pending: 0,
      },
      // 状态选项
      statusOptions: [],
      // 紧急程度选项
      urgencyOptions: [],
      // 难度等级选项
      difficultyOptions: [],
      // 处理对话框
      processDialogVisible: false,
      currentProposal: null,
      // 标签选项
      improvementTags: IMPROVEMENT_TAGS,
    };
  },
  created() {
    this.initOptions();
    this.loadProposalList();
  },
  methods: {
    // 初始化选项数据
    initOptions() {
      this.statusOptions = getStatusOptions();
      this.urgencyOptions = getUrgencyOptions();
      this.difficultyOptions = getDifficultyOptions();
    },

    // 加载提案列表
    async loadProposalList() {
      this.loading = true;
      try {
        const params = {
          page: this.pagination.page,
          pageSize: this.pagination.pageSize,
          ...this.searchParams,
          loginId: this.$store.state.user.loginId,
        };

        // 添加日期范围参数
        if (this.dateRange && this.dateRange.length === 2) {
          params.startDate = this.dateRange[0];
          params.endDate = this.dateRange[1];
        }

        const response = await getProposalList(params);

        if (response && Array.isArray(response)) {
          this.proposalList = response;
          // 如果API返回的是数组，需要根据实际API响应结构调整
          this.pagination.total = response.length;
          this.updateStatistics();
        } else if (response && response.data) {
          // 如果API返回的是包含data和total的对象
          this.proposalList = response.data || [];
          this.pagination.total = response.total || 0;
          this.updateStatistics();
        }
      } catch (error) {
        console.error("加载提案列表失败:", error);
        this.$message.error("加载提案列表失败");
        this.proposalList = [];
        this.pagination.total = 0;
      } finally {
        this.loading = false;
      }
    },

    // 更新统计信息
    updateStatistics() {
      this.statistics.processed = this.proposalList.filter((p) => p.status > 1).length;
      this.statistics.pending = this.proposalList.filter((p) => p.status === 1).length;
    },

    // 搜索
    handleSearch() {
      this.pagination.page = 1;
      this.loadProposalList();
    },

    // 重置搜索
    handleReset() {
      this.searchParams = {
        title: "",
        proposer: "",
        status: "",
      };
      this.dateRange = [];
      this.pagination.page = 1;
      this.loadProposalList();
    },

    // 日期范围变化
    handleDateChange() {
      this.handleSearch();
    },

    // 刷新
    handleRefresh() {
      this.loadProposalList();
    },

    // 分页大小变化
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.pagination.page = 1;
      this.loadProposalList();
    },

    // 当前页变化
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.loadProposalList();
    },

    // 选择变化
    handleSelectionChange(selection) {
      this.selectedProposals = selection;
    },

    // 处理提案
    handleProcess(proposal) {
      this.currentProposal = proposal;
      this.processDialogVisible = true;
    },

    // 打印提案
    handlePrint(row) {
      this.printProposal(row);
    },

    // 打印提案详情
    printProposal(proposal) {
      const printContent = this.generatePrintContent(proposal);
      const printWindow = window.open("", "_blank");
      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.focus();
      printWindow.print();
      printWindow.close();
    },

    // 处理成功回调
    handleProcessSuccess() {
      this.processDialogVisible = false;
      this.currentProposal = null;
      this.loadProposalList();
      this.$message.success("提案处理成功");
    },

    // 获取紧急程度文本
    getUrgencyText(urgency) {
      const option = this.urgencyOptions.find((o) => o.value === urgency);
      return option ? option.label : "未知";
    },

    // 获取紧急程度类型
    getUrgencyType(urgency) {
      const typeMap = {
        1: "success", // 低
        2: "warning", // 中
        3: "danger", // 高
      };
      return typeMap[urgency] || "info";
    },

    // 获取难度等级文本
    getDifficultyText(difficulty) {
      const option = this.difficultyOptions.find((o) => o.value === difficulty);
      return option ? option.label : "未知";
    },

    // 获取难度等级类型
    getDifficultyType(difficulty) {
      const typeMap = {
        1: "success", // 简单
        2: "warning", // 中等
        3: "danger", // 困难
      };
      return typeMap[difficulty] || "info";
    },

    // 获取状态文本
    getStatusText(status) {
      const option = this.statusOptions.find((o) => o.value === status.toString());
      return option ? option.label : "未知";
    },

    // 获取状态类型
    getStatusType(status) {
      const typeMap = {
        1: "warning", // 待处理
        2: "primary", // 处理中
        3: "success", // 已完成
        4: "danger", // 已拒绝
      };
      return typeMap[status] || "info";
    },

    // 生成打印内容
    generatePrintContent(proposal) {
      const currentDate = new Date().toLocaleDateString("zh-CN");

      return `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>精益改善提案 - ${proposal.proposalNo || proposal.id}</title>
          <style>
            body {
              font-family: 'Microsoft YaHei', Arial, sans-serif;
              margin: 20px;
              line-height: 1.6;
              color: #333;
            }
            .header {
              text-align: center;
              margin-bottom: 30px;
              border-bottom: 2px solid #333;
              padding-bottom: 15px;
            }
            .title {
              font-size: 24px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            .subtitle {
              font-size: 14px;
              color: #666;
            }
            .info-table {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 20px;
            }
            .info-table th,
            .info-table td {
              border: 1px solid #333;
              padding: 8px 12px;
              text-align: left;
            }
            .info-table th {
              background-color: #f5f5f5;
              font-weight: bold;
              width: 70px;
            }
            .info-table td{
              min-width: 35px;
            }
            .tags-row {
              background-color: #f9f9f9;
            }
            .tag-item {
              display: inline-block;
              border: 1px solid #333;
              color: #333;
              padding: 2px 8px;
              margin: 2px 4px 2px 0;
              border-radius: 3px;
              font-size: 12px;
              background-color: transparent;
            }
            .attachments-print {
              display: flex;
              flex-wrap: wrap;
              gap: 15px;
              margin-top: 10px;
            }
            .print-image-item {
              text-align: center;
              max-width: 150px;
            }
            .print-image {
              width: 120px;
              height: 80px;
              object-fit: cover;
              border: 1px solid #333;
              border-radius: 4px;
            }
            .print-image-name {
              margin: 5px 0 0 0;
              font-size: 10px;
              color: #333;
              word-break: break-all;
            }
            .print-file-item {
              padding: 5px 10px;
              border: 1px solid #333;
              border-radius: 4px;
              font-size: 12px;
              color: #333;
            }
            .content-section {
              margin-bottom: 20px;
            }
            .content-title {
              font-weight: bold;
              margin-bottom: 8px;
              padding: 5px 0;
              border-bottom: 1px solid #ddd;
            }
            .content-text {
              padding: 10px;
              border: 1px solid #ddd;
              background-color: #fafafa;
            }
            .signature-section {
              margin-top: 30px;
              page-break-inside: avoid;
            }
            .signature-table {
              width: 100%;
              border-collapse: collapse;
              margin-top: 10px;
            }
            .signature-table th,
            .signature-table td {
              border: 1px solid #333;
              padding: 5px;
              text-align: center;
              height: 30px;
              font-size: 12px;
            }
            .signature-table th {
              background-color: #f5f5f5;
              font-weight: bold;
            }
            .print-date {
              text-align: right;
              margin-top: 20px;
              font-size: 12px;
              color: #666;
            }
            @media print {
              body { margin: 0; }
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="title">精益改善提案</div>
            <div class="subtitle">Lean Improvement Proposal</div>
          </div>

          <table class="info-table">
            <tr>
              <th>提案编号</th>
              <td colspan="2">${proposal.proposalNo || proposal.id || "-"}</td>
              <th style="width: 50px">提案人</th>
              <td>${proposal.proposerName || "-"}</td>
              <th>提案标题</th>
              <td colspan="2">${proposal.title || "-"}</td>
            </tr>
            <tr>
              <th>紧急程度</th>
              <td>${this.getUrgencyText(proposal.urgency)}</td>
              <th>实施难度</th>
              <td>${this.getDifficultyText(proposal.difficulty)}</td>
              <th>实施部门</th>
              <td>${proposal.implementationDepartment || "-"}</td>
              <th>完成日期</th>
              <td>${proposal.finishDate ? this.formatDate(proposal.finishDate).substring(0, 10) : "-"}</td>
            </tr>
            <tr class="tags-row">
              <th>提案标签</th>
              <td colspan="7">
                ${
                  this.getTagLabels(proposal.tags)
                    .map((tag) => `<span class="tag-item">${tag.label}</span>`)
                    .join("") || "暂无标签"
                }
              </td>
            </tr>
          </table>

          <div class="content-section">
            <div class="content-title">现状描述</div>
            <div class="content-text">${proposal.currentSituation || "暂无描述"}</div>
          </div>

          <div class="content-section">
            <div class="content-title">改善建议</div>
            <div class="content-text">${proposal.suggestion || "暂无建议"}</div>
          </div>

          <div class="content-section">
            <div class="content-title">预期效果</div>
            <div class="content-text">${proposal.expectedEffect || "暂无描述"}</div>
          </div>

          ${
            proposal.attachments
              ? `
          <div class="content-section">
            <div class="content-title">附件</div>
            <div class="attachments-print">
              ${this.getAttachmentList(proposal.attachments)
                .map((attachment) => {
                  if (this.isImageFile(attachment.name)) {
                    return `<div class="print-image-item">
                    <img src="${attachment.url}" alt="${attachment.name}" class="print-image" />
                    <p class="print-image-name">${attachment.name}</p>
                  </div>`;
                  } else {
                    return `<div class="print-file-item">📄 ${attachment.name}</div>`;
                  }
                })
                .join("")}
            </div>
          </div>
          `
              : ""
          }

          <div class="signature-section">
            <div class="content-title">审批签字</div>
            <table class="signature-table">
              <tr>
                <th style="width: 12%;">职位</th>
                <th style="width: 20%;">签字</th>
                <th style="width: 18%;">日期</th>
                <th style="width: 12%;">职位</th>
                <th style="width: 20%;">签字</th>
                <th style="width: 18%;">日期</th>
              </tr>
              <tr>
                <td>班长</td>
                <td></td>
                <td></td>
                <td>技术员</td>
                <td></td>
                <td></td>
              </tr>
              <tr>
                <td>品质员</td>
                <td></td>
                <td></td>
                <td>生产部长</td>
                <td></td>
                <td></td>
              </tr>
            </table>
          </div>

          <div class="print-date">
            打印日期: ${currentDate}
          </div>
        </body>
        </html>
      `;
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return "";
      const date = new Date(dateString);
      return date.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
      });
    },

    // 获取标签标签
    getTagLabels(tagsString) {
      if (!tagsString) return [];

      // 将标签字符串按逗号分割，转换为数字数组
      const tagIds = tagsString
        .split(",")
        .map((id) => parseInt(id.trim()))
        .filter((id) => !isNaN(id));

      // 根据ID查找对应的标签对象
      return tagIds.map((id) => {
        const tag = this.improvementTags.find((tag) => tag.value === id);
        return tag || { value: id, label: `未知标签(${id})` };
      });
    },

    // 解析附件列表
    getAttachmentList(attachmentsString) {
      if (!attachmentsString) return [];

      try {
        // 尝试解析JSON格式的附件数据
        if (attachmentsString.startsWith("[") || attachmentsString.startsWith("{")) {
          const attachments = JSON.parse(attachmentsString);
          return Array.isArray(attachments) ? attachments : [attachments];
        }

        // 如果是简单的文件名列表（逗号分隔）
        const fileNames = attachmentsString
          .split(",")
          .map((name) => name.trim())
          .filter((name) => name);
        return fileNames.map((name, idx) => ({
          name: `附件${idx + 1}${name.substring(name.lastIndexOf("."))}`,
          url: this.getFileUrl(name), // 根据文件名生成URL
        }));
      } catch (error) {
        console.warn("解析附件数据失败:", error);
        return [];
      }
    },

    // 根据文件名生成文件URL
    getFileUrl(fileName) {
      // 这里需要根据实际的文件存储路径来生成URL
      // 假设文件存储在 /uploads/attachments/ 目录下
      return `http://***************:8485${fileName}`;
    },

    // 判断是否为图片文件
    isImageFile(fileName) {
      if (!fileName) return false;
      const imageExtensions = [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg"];
      const extension = fileName.toLowerCase().substring(fileName.lastIndexOf("."));
      return imageExtensions.includes(extension);
    },
  },
};
</script>

<style scoped>
.lean-improvement-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.search-section {
  margin-bottom: 20px;
}

.search-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.search-form {
  padding: 10px 0;
}

.statistics-info {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 40px;
}

.stat-item {
  margin-left: 20px;
  color: #606266;
  font-size: 14px;
}

.stat-item i {
  margin-right: 5px;
  color: #409eff;
}

.table-section {
  margin-bottom: 20px;
}

.table-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.card-title i {
  margin-right: 8px;
  color: #409eff;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

/* 表格样式优化 */
.el-table {
  border-radius: 4px;
  overflow: hidden;
}

.el-table th {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

.el-table td {
  padding: 12px 0;
}

/* 标签样式 */
.el-tag {
  border-radius: 4px;
  font-weight: 500;
}

/* 按钮样式 */
.el-button--mini {
  padding: 5px 10px;
  font-size: 12px;
  border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .lean-improvement-container {
    padding: 15px;
  }

  .el-col {
    margin-bottom: 10px;
  }
}

@media (max-width: 768px) {
  .lean-improvement-container {
    padding: 10px;
  }

  .statistics-info {
    justify-content: center;
    flex-wrap: wrap;
  }

  .stat-item {
    margin: 5px 10px;
  }

  .header-actions {
    flex-direction: column;
    gap: 5px;
  }
}

/* 打印样式 */
@media print {
  .search-section,
  .header-actions,
  .pagination-wrapper,
  .el-table-column--selection {
    display: none !important;
  }

  .lean-improvement-container {
    padding: 0;
    background: white;
  }

  .table-card {
    box-shadow: none;
    border: 1px solid #ddd;
  }

  .el-table {
    font-size: 12px;
  }

  .el-table th,
  .el-table td {
    padding: 8px 4px;
    border: 1px solid #ddd;
  }
}
</style>
