<template>
  <div>
    <template v-for="item in menuData">
      <el-submenu v-if="item.children && item.children.length" :index="item.path" :key="item.path">
        <template #title>
          <i :class="item.icon"></i>
          <span>{{ item.name }}</span>
        </template>
        <XMenuItems :menuData="item.children" :activeMenu="activeMenu" @menu-click="handleClick" />
      </el-submenu>
      <el-menu-item v-else @click="handleClick(item)" :index="item.path" :key="item.path">
        <i :class="item.icon"></i>
        <span>{{ item.name }}</span>
      </el-menu-item>
    </template>
  </div>
</template>

<script>
export default {
  name: "XMenuItems",
  props: {
    menuData: {
      type: Array,
      required: true,
    },
    activeMenu: {
      type: String,
      default: "",
    },
  },
  methods: {
    handleClick(item) {
      this.$emit("menu-click", item);
    },
  },
};
</script>

<style scoped lang="scss">
:deep(.el-submenu) {
  margin: 4px 12px;
}

:deep(.el-menu-item),
:deep(.el-sub-menu__title) {
  height: 48px;
  line-height: 48px;
  margin: 4px 12px;
  border-radius: 8px;
  transition: all 0.2s;

  &:hover {
    background: $secondary-blue;
    color: $primary-blue !important;
  }

  &.is-active {
    background: $primary-blue !important;
    color: white !important;
  }

  i {
    color: inherit;
  }
}

.brand-area {
  height: 80px !important;
  pointer-events: none;
  .brand-logo {
    width: 48px;
    margin-right: 12px;
  }
  .brand-text {
    font-size: 20px;
    color: var(--dark-text);
    font-weight: 600;
  }
}
</style>
