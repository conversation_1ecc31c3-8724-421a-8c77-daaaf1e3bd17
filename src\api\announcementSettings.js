/**
 * 公告栏设置相关API
 */
import service from "@/router/request";

// API接口路径
const API_BASE = "/Announcement";

/**
 * 获取公告栏设置配置
 * @returns {Promise} 返回配置数据
 */
export function getAnnouncementSettings() {
  return service({
    url: `${API_BASE}/GetAnnouncementConfig`,
    method: "get",
  });
}

/**
 * 保存公告栏设置配置
 * @param {Object} settings 设置数据
 * @param {number} settings.scrollSpeed 滚动速度（px/s）
 * @param {number} settings.scrollHeight 滚动区域高度（px）
 * @returns {Promise} 返回保存结果
 */
export function saveAnnouncementSettings(settings) {
  return service({
    url: `${API_BASE}/SaveAnnouncementConfig`,
    method: "post",
    data: {
      scrollSpeed: settings.scrollSpeed,
      scrollHeight: settings.scrollHeight,
      // 可以添加其他配置项
      updatedAt: new Date().toISOString(),
    },
  });
}

/**
 * 重置公告栏设置为默认值
 * @returns {Promise} 返回重置结果
 */
export function resetAnnouncementSettings() {
  return service({
    url: `${API_BASE}/resetSettings`,
    method: "post",
  });
}

/**
 * 获取公告栏设置的默认配置
 * @returns {Object} 默认配置对象
 */
export function getDefaultSettings() {
  return {
    scrollSpeed: 20, // px/s
    scrollHeight: 120, // px
  };
}

/**
 * 验证设置参数的有效性
 * @param {Object} settings 设置数据
 * @returns {Object} 验证结果 { valid: boolean, errors: string[] }
 */
export function validateSettings(settings) {
  const errors = [];

  // 验证滚动速度
  if (!settings.scrollSpeed || settings.scrollSpeed < 10 || settings.scrollSpeed > 50) {
    errors.push("滚动速度必须在10-50px/s之间");
  }

  // 验证滚动高度
  if (!settings.scrollHeight || settings.scrollHeight < 80 || settings.scrollHeight > 200) {
    errors.push("滚动区域高度必须在80-200px之间");
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}

// 导出所有API方法
export default {
  getAnnouncementSettings,
  saveAnnouncementSettings,
  resetAnnouncementSettings,
  getDefaultSettings,
  validateSettings,
};
