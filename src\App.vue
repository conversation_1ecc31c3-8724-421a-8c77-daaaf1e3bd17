<template>
  <div id="app">
    <keep-alive :include="keepAlive">
      <router-view></router-view>
    </keep-alive>
  </div>
</template>

<script>
import { LocalStorage } from "@/utils/utils.js";

export default {
  name: "App",

  data() {
    return {
      keepAlive: ["groupMembers", "ReportForDay", "DailyOverview"],
    };
  },

  created() {
    document.title = "欧克*任务管理系统";
    window.addEventListener("beforeunload", this.saveStateToLocalStorage);
    // this.restoreStateFromLocalStorage();
  },

  methods: {
    saveStateToLocalStorage() {
      const stateToSave = { ...this.$store.state, indexViewInstance: null };
      LocalStorage.setItem("store", JSON.stringify(stateToSave));
    },
  },
};
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}

#app > div {
  background-color: var(--background-color);
}

nav {
  padding: 30px;
}

nav a {
  font-weight: bold;
  color: #2c3e50;
}

nav a.router-link-exact-active {
  color: #42b983;
}
</style>
