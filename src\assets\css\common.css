html {
  /* overflow: hidden; */
}

.mb-2 {
  margin-bottom: 20px !important;
}

.mb-1 {
  margin-bottom: 10px !important;
}

.mr-2 {
  margin-right: 20px;
}

.mr-1 {
  margin-right: 10px;
}

.mt-1 {
  margin-top: 10px;
}

.mt-2 {
  margin-top: 20px;
}

.ml-1 {
  margin-left: 10px;
}

/* 搜索 */
.search-bar {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  /* margin-bottom: 20px; */
}
.search-bar .search-item {
  display: flex;
  align-items: center;
  margin: 0 20px 20px 0;
  width: 300px;
}
.search-bar .search-btn {
  margin: 0 20px 20px 0;
}
.search-bar .search-item .label {
    flex-shrink: 0;
}

.cell-highlight {
  border: 2px solid red !important;
}


.phone-message-tip {
  width: 200px !important;
  min-width: auto;
}
.phone-message-box {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
}