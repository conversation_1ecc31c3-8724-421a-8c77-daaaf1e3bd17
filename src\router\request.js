import axios from "axios";
import store from "../store";
import { Message } from "element-ui";
import { isMobile } from "@/utils/utils";

const getBaseUrl = () => {
  let baseURL = "http://localhost:5000/"; // http://localhost:5000/api
  switch (location.origin) {
    case "http://***************:8486":
      baseURL = "http://***************:8485/";
      break;
    case "http://*************:8486":
      baseURL = "http://*************:8485/";
      break;
  }
  baseURL += "api";
  return baseURL;
};

axios.defaults.headers.common["X-Requested-With"] = "XMLHttpRequest";

const service = axios.create({
  baseURL: getBaseUrl(),
  // 超时时间
  timeout: 50000,
});

service.interceptors.request.use(
  (config) => {
    const token = store.state.user?.token || "";
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

let logoutTimer = null;

service.interceptors.response.use(
  (response) => {
    let res = response.data;

    // Attempt to parse string data if necessary
    if (typeof res === "string") {
      try {
        res = JSON.parse(res);
      } catch (e) {
        console.warn("Failed to parse response string:", e);
      }
    }

    if (res?.err === "4001") {
      if (logoutTimer) clearTimeout(logoutTimer);
      logoutTimer = setTimeout(() => {
        Message.warning("登录过期，请重新登录");
        store.dispatch("logout", false);
      }, 500);
    }

    return res;
  },
  (error) => {
    if (error.response) {
      const { status, data } = error.response;
      if (status === 401) {
        Message.warning("未授权，请重新登录");
        store.dispatch("logout", false);
      } else if (status === 500) {
        Message.error("服务器错误，请稍后再试");
      } else {
        Message.error(data?.message || "网络故障，请稍后再试");
      }
    } else {
      Message.error("网络故障，请检查您的网络连接");
    }
    return Promise.reject(error);
  }
);

export default service;
