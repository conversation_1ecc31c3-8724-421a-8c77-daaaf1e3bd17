<template>
  <div class="home">
    <div ref="searchBarRef" class="search-bar">
      <div class="search-item">
        <span class="label">查询月份：</span>
        <el-date-picker v-model="date" type="month" placeholder="选择月" value-format="yyyy-MM"></el-date-picker>
      </div>
      <div class="search-item">
        <el-input v-model="keyword" placeholder="姓名、队长工号、队名或巴组" />
      </div>
      <el-button type="primary" class="search-btn" @click="search">搜索</el-button>
      <el-button type="warning" class="search-btn" @click="recalculation">重算</el-button>
      <el-button class="search-btn" @click="exportData()">导出</el-button>
    </div>
    <!-- Table -->
    <div>
      <el-table v-if="maxHeight" :data="tableData" border style="width: 100%" :max-height="maxHeight">
        <el-table-column v-for="config of tableConfig" :key="config.prop" v-bind="config"></el-table-column>
        <el-table-column label="操作" fixed="right">
          <template v-slot="scope">
            <el-link type="primary" @click="viewDetails(scope.row)">查看详情</el-link>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        ref="paginationRef"
        style="width: 75%"
        @size-change="pageSizeChange"
        @current-change="pageNumChange"
        :current-page="paging.pageNum"
        :page-sizes="[5, 10, 20, 40]"
        :page-size="paging.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="paging.total"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import service from "@/router/request";
import { xlsx } from "@/utils/utils";

export default {
  name: "teamaccount",
  data() {
    return {
      date: "",
      tableData: [],
      tableConfig: [
        { label: "所属巴组", prop: "groname" },
        { label: "巴长", prop: "grohmname" },
        { label: "团队名称", prop: "tmename", "min-width": "120" },
        { label: "队长", prop: "tmehmname" },
        { label: "核算月份", prop: "mmonth" },
        { label: "计件工时", prop: "tachours" },
        { label: "计件工资", prop: "tacwages" },
        { label: "计时工时", prop: "ttmhours" },
        { label: "计时工资", prop: "ttmwages" },
        { label: "临时工时", prop: "tpthours" },
        { label: "临时工资", prop: "tptwages" },
        { label: "合计工时", prop: "smhours" },
        { label: "合计工资", prop: "smwages" },
        { label: "巴组小队编号", prop: "tmeno", "min-width": "130" },
      ],
      paging: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      keyword: "",
      maxHeight: 0,
    };
  },

  mounted() {
    this.computeMaxHeight();
    this.date = this.$moment().subtract(1, "months").format("yyyy-MM");
    this.getTeamAccount();
  },

  activated() {
    // this.date = this.$moment().subtract(1, "months").format("yyyy-MM");
    this.getTeamAccount();
  },

  methods: {
    //
    getTeamAccount() {
      service
        .get("/TeamAccount/GetTeamAccount", {
          params: { fuzzy: this.keyword, mmonth: this.date, ...this.paging },
        })
        .then((res) => {
          if (res.status == "succeed") {
            this.tableData = res.data?.list || [];
            this.tableData.forEach((item) => {
              item.tachours = item.tachours || 0;
              item.ttmhours = item.ttmhours || 0;
              item.tpthours = item.tpthours || 0;
            });
            this.paging.total = res.data?.total || [];
          }
        });
    },

    recalculation() {
      const loading = this.$loading({
        lock: true,
        text: "计算中.....",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      service.post("/TeamAccount/calculateTeamSalary", { month: this.date }).then((res) => {
        loading.close();
        if (res.status == "succeed") {
          this.getTeamAccount();
          this.$message.success("计算成功");
        } else {
          this.$message.error("计算失败：", res.err);
        }
      });
    },

    search() {
      this.paging.pageNum = 1;
      this.getTeamAccount();
    },

    pageSizeChange(val) {
      this.paging.pageSize = val;
      this.getTeamAccount();
    },

    pageNumChange(val) {
      this.paging.pageNum = val;
      this.getTeamAccount();
    },

    viewDetails(row) {
      this.$router.push({
        name: "teamWageDetails",
        query: { teamPayroll: encodeURIComponent(JSON.stringify(row)) },
      });
    },

    exportData() {
      service
        .get("/TeamAccount/GetTeamAccount", {
          params: { fuzzy: this.keyword, mmonth: this.date, pageNum: 1, pageSize: 9999999 },
        })
        .then((res) => {
          if (res.status == "succeed") {
            let header = {};
            this.tableConfig.forEach((item) => (header[item.prop] = item.label));
            xlsx(res.data.list, header, "团队结算数据");
          }
        });
    },

    computeMaxHeight() {
      let mainEl = document.getElementById("main");
      this.maxHeight =
        mainEl.clientHeight -
        this.$refs.searchBarRef.clientHeight -
        this.$refs.paginationRef.$el.clientHeight -
        20 -
        40;
    },
  },
};
</script>

<style>
.search-bar {
  margin-bottom: 20px;
}

.search-bar > div {
  display: flex;
  justify-content: center;
  margin-bottom: 10px;
}

.search-bar .search-item {
  margin-right: 10px;
}
</style>
