<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div v-if="proposal" class="proposal-detail">
      <!-- 提案基本信息 -->
      <el-card class="info-card" shadow="never">
        <div slot="header" class="card-header">
          <span class="card-title">
            <i class="el-icon-document"></i>
            提案信息
          </span>
        </div>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>提案编号：</label>
              <span>{{ proposal.proposalNo }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>提案人：</label>
              <span>{{ proposal.proposerName }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>联系方式：</label>
              <span>{{ proposal.contact }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>创建时间：</label>
              <span>{{ formatDate(proposal.createdAt) }}</span>
            </div>
          </el-col>
        </el-row>

        <div class="info-item">
          <label>提案标题：</label>
          <span>{{ proposal.title }}</span>
        </div>

        <div class="info-item" v-if="proposal.tags">
          <label>提案标签：</label>
          <div class="tags-container">
            <el-tag
              v-for="tag in getTagLabels(proposal.tags)"
              :key="tag.value"
              size="small"
              type="primary"
              class="tag-item"
            >
              {{ tag.label }}
            </el-tag>
            <span v-if="!getTagLabels(proposal.tags).length" class="no-tags">暂无标签</span>
          </div>
        </div>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>紧急程度：</label>
              <el-tag :type="getUrgencyType(proposal.urgency)" size="small">
                {{ getUrgencyText(proposal.urgency) }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>难度等级：</label>
              <el-tag :type="getDifficultyType(proposal.difficulty)" size="small">
                {{ getDifficultyText(proposal.difficulty) }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>当前状态：</label>
              <el-tag :type="getStatusType(proposal.status)" size="small">
                {{ getStatusText(proposal.status) }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 提案详细内容 -->
      <el-card class="content-card" shadow="never">
        <div slot="header" class="card-header">
          <span class="card-title">
            <i class="el-icon-edit-outline"></i>
            提案详情
          </span>
        </div>

        <div class="content-section">
          <h4>现状描述</h4>
          <p class="content-text">{{ proposal.currentSituation || "暂无描述" }}</p>
        </div>

        <div class="content-section">
          <h4>改善建议</h4>
          <p class="content-text">{{ proposal.suggestion || "暂无建议" }}</p>
        </div>

        <div class="content-section">
          <h4>预期效果</h4>
          <p class="content-text">{{ proposal.expectedEffect || "暂无描述" }}</p>
        </div>

        <div v-if="proposal.attachments" class="content-section">
          <h4>附件</h4>
          <div class="attachments-container">
            <div
              v-for="(attachment, index) in getAttachmentList(proposal.attachments)"
              :key="index"
              class="attachment-item"
            >
              <!-- 图片附件 -->
              <div v-if="isImageFile(attachment.name)" class="image-attachment">
                <img
                  :src="attachment.url"
                  :alt="attachment.name"
                  class="attachment-image"
                  @click="previewImage(attachment.url, attachment.name)"
                />
                <p class="attachment-name">{{ attachment.name }}</p>
              </div>

              <!-- 非图片附件 -->
              <div v-else class="file-attachment">
                <div class="file-icon">
                  <i :class="getFileIcon(attachment.name)"></i>
                </div>
                <div class="file-info">
                  <p class="file-name">{{ attachment.name }}</p>
                  <el-button type="text" size="mini" @click="downloadFile(attachment.url, attachment.name)">
                    下载
                  </el-button>
                </div>
              </div>
            </div>

            <!-- 如果没有解析出附件，显示原始文本 -->
            <div v-if="!getAttachmentList(proposal.attachments).length" class="content-text">
              {{ proposal.attachments }}
            </div>
          </div>
        </div>

        <div class="content-section">
          <h4>实施信息</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>实施部门：</label>
                <span>{{ proposal.implementationDepartment || "未指定" }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>完成日期：</label>
                <span>{{ proposal.finishDate ? formatDate(proposal.finishDate) : "未设定" }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <!-- 处理表单 -->
      <el-card class="process-card" shadow="never">
        <div slot="header" class="card-header">
          <span class="card-title">
            <i class="el-icon-s-check"></i>
            处理意见
          </span>
        </div>

        <el-form ref="processForm" :model="processForm" :rules="processRules" label-width="100px">
          <el-form-item label="处理状态" prop="status">
            <el-select v-model="processForm.status" placeholder="请选择处理状态" style="width: 200px">
              <el-option
                v-for="option in processStatusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="处理意见" prop="review">
            <el-input
              v-model="processForm.review"
              type="textarea"
              :rows="4"
              placeholder="请输入处理意见..."
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="processing" @click="handleSubmit">
        {{ processing ? "处理中..." : "确认处理" }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { processProposal, getStatusOptions, getUrgencyOptions, getDifficultyOptions } from "@/api/improvementProposal";
import { IMPROVEMENT_TAGS } from "@/utils/constants";

export default {
  name: "ProcessProposalDialog",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    proposal: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      processing: false,
      processForm: {
        status: "",
        review: "",
      },
      processRules: {
        status: [{ required: true, message: "请选择处理状态", trigger: "change" }],
        review: [
          { required: true, message: "请输入处理意见", trigger: "blur" },
          { min: 10, message: "处理意见至少10个字符", trigger: "blur" },
        ],
      },
      statusOptions: [],
      urgencyOptions: [],
      difficultyOptions: [],
      departmentList: [],
      improvementTags: IMPROVEMENT_TAGS,
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
    dialogTitle() {
      return this.proposal ? `处理提案 - ${this.proposal.proposalNo}` : "处理提案";
    },
    processStatusOptions() {
      // 过滤掉"全部"选项，只保留可处理的状态
      return this.statusOptions.filter((option) => option.value !== "");
    },
  },
  created() {
    this.initOptions();
  },
  watch: {
    visible(val) {
      if (val && this.proposal) {
        this.resetForm();
        this.processForm.status = `${this.proposal.review?.result}`;
        this.processForm.review = this.proposal.review?.comment;
      }
    },
  },
  methods: {
    initOptions() {
      this.statusOptions = getStatusOptions();
      this.urgencyOptions = getUrgencyOptions();
      this.difficultyOptions = getDifficultyOptions();
    },

    resetForm() {
      this.processForm = {
        status: "",
        review: "",
      };
      if (this.$refs.processForm) {
        this.$refs.processForm.clearValidate();
      }
    },

    handleClose() {
      this.dialogVisible = false;
      this.resetForm();
    },

    async handleSubmit() {
      try {
        await this.$refs.processForm.validate();

        this.processing = true;

        const processData = {
          proposalId: this.proposal.id,
          result: parseInt(this.processForm.status),
          reviewer: this.$store.state.user.loginId,
          comment: this.processForm.review,
        };

        await processProposal(processData);

        this.$message.success("提案处理成功");
        this.$emit("success");
        this.handleClose();
      } catch (error) {
        console.error("处理提案失败:", error);
        this.$message.error("处理提案失败");
      } finally {
        this.processing = false;
      }
    },

    // 工具方法
    formatDate(dateString) {
      if (!dateString) return "";
      const date = new Date(dateString);
      return date.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
      });
    },

    getUrgencyText(urgency) {
      const option = this.urgencyOptions.find((o) => o.value === urgency);
      return option ? option.label : "未知";
    },

    getUrgencyType(urgency) {
      const typeMap = { 1: "success", 2: "warning", 3: "danger" };
      return typeMap[urgency] || "info";
    },

    getDifficultyText(difficulty) {
      const option = this.difficultyOptions.find((o) => o.value === difficulty);
      return option ? option.label : "未知";
    },

    getDifficultyType(difficulty) {
      const typeMap = { 1: "success", 2: "warning", 3: "danger" };
      return typeMap[difficulty] || "info";
    },

    getStatusText(status) {
      const option = this.statusOptions.find((o) => o.value === status.toString());
      return option ? option.label : "未知";
    },

    getStatusType(status) {
      const typeMap = { 1: "warning", 2: "primary", 3: "success", 4: "danger" };
      return typeMap[status] || "info";
    },

    // 获取标签标签
    getTagLabels(tagsString) {
      if (!tagsString) return [];

      // 将标签字符串按逗号分割，转换为数字数组
      const tagIds = tagsString
        .split(",")
        .map((id) => parseInt(id.trim()))
        .filter((id) => !isNaN(id));

      // 根据ID查找对应的标签对象
      return tagIds.map((id) => {
        const tag = this.improvementTags.find((tag) => tag.value === id);
        return tag || { value: id, label: `未知标签(${id})` };
      });
    },

    // 解析附件列表
    getAttachmentList(attachmentsString) {
      if (!attachmentsString) return [];

      try {
        // 尝试解析JSON格式的附件数据
        if (attachmentsString.startsWith("[") || attachmentsString.startsWith("{")) {
          const attachments = JSON.parse(attachmentsString);
          return Array.isArray(attachments) ? attachments : [attachments];
        }

        // 如果是简单的文件名列表（逗号分隔）
        const fileNames = attachmentsString
          .split(",")
          .map((name) => name.trim())
          .filter((name) => name);
        return fileNames.map((name, idx) => ({
          name: `附件${idx + 1}${name.substring(name.lastIndexOf("."))}`,
          url: this.getFileUrl(name), // 根据文件名生成URL
        }));
      } catch (error) {
        console.warn("解析附件数据失败:", error);
        return [];
      }
    },

    // 根据文件名生成文件URL
    getFileUrl(fileName) {
      // 这里需要根据实际的文件存储路径来生成URL
      // 假设文件存储在 /uploads/attachments/ 目录下
      return `http://192.168.254.232:8485${fileName}`;
    },

    // 判断是否为图片文件
    isImageFile(fileName) {
      if (!fileName) return false;
      const imageExtensions = [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg"];
      const extension = fileName.toLowerCase().substring(fileName.lastIndexOf("."));
      return imageExtensions.includes(extension);
    },

    // 获取文件图标
    getFileIcon(fileName) {
      if (!fileName) return "el-icon-document";

      const extension = fileName.toLowerCase().substring(fileName.lastIndexOf("."));
      const iconMap = {
        ".pdf": "el-icon-document",
        ".doc": "el-icon-document",
        ".docx": "el-icon-document",
        ".xls": "el-icon-s-grid",
        ".xlsx": "el-icon-s-grid",
        ".ppt": "el-icon-present",
        ".pptx": "el-icon-present",
        ".txt": "el-icon-document",
        ".zip": "el-icon-folder-opened",
        ".rar": "el-icon-folder-opened",
      };

      return iconMap[extension] || "el-icon-document";
    },

    // 预览图片
    previewImage(url, name) {
      // 可以使用Element UI的图片预览组件或自定义预览
      this.$alert(`<img src="${url}" style="max-width: 100%; max-height: 400px;" />`, name, {
        dangerouslyUseHTMLString: true,
        showConfirmButton: false,
        showClose: true,
      }).catch(() => {});
    },

    // 下载文件
    downloadFile(url, name) {
      const link = document.createElement("a");
      link.href = url;
      link.download = name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
  },
};
</script>

<style scoped>
.proposal-detail {
  max-height: 600px;
  overflow-y: auto;
}

.info-card,
.content-card,
.process-card {
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
}

.info-card:last-child,
.content-card:last-child,
.process-card:last-child {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 0;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.card-title i {
  margin-right: 8px;
  color: #409eff;
}

.info-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item label {
  font-weight: 600;
  color: #606266;
  margin-right: 10px;
  min-width: 80px;
  flex-shrink: 0;
}

.info-item span {
  color: #303133;
  flex: 1;
}

.content-section {
  margin-bottom: 20px;
}

.content-section:last-child {
  margin-bottom: 0;
}

.content-section h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  font-weight: 600;
  color: #409eff;
  border-left: 3px solid #409eff;
  padding-left: 10px;
}

.content-text {
  margin: 0;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  color: #606266;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.el-tag {
  border-radius: 4px;
  font-weight: 500;
}

/* 标签容器样式 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.tag-item {
  margin: 0;
}

.no-tags {
  color: #909399;
  font-style: italic;
}

/* 附件样式 */
.attachments-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.attachment-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 150px;
}

.image-attachment {
  text-align: center;
}

.attachment-image {
  width: 120px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #ddd;
  cursor: pointer;
  transition: transform 0.2s;
}

.attachment-image:hover {
  transform: scale(1.05);
  border-color: #409eff;
}

.attachment-name {
  margin: 5px 0 0 0;
  font-size: 12px;
  color: #606266;
  text-align: center;
  word-break: break-all;
  line-height: 1.2;
}

.file-attachment {
  display: flex;
  align-items: center;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fafafa;
  min-width: 200px;
}

.file-icon {
  margin-right: 10px;
  font-size: 24px;
  color: #409eff;
}

.file-info {
  flex: 1;
}

.file-name {
  margin: 0 0 5px 0;
  font-size: 14px;
  color: #303133;
  word-break: break-all;
}

/* 表单样式 */
.el-form-item {
  margin-bottom: 20px;
}

.el-textarea__inner {
  resize: vertical;
  min-height: 100px;
}

/* 滚动条样式 */
.proposal-detail::-webkit-scrollbar {
  width: 6px;
}

.proposal-detail::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.proposal-detail::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.proposal-detail::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .el-dialog {
    width: 95% !important;
    margin: 5vh auto !important;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .info-item label {
    margin-bottom: 5px;
    min-width: auto;
  }
}
</style>
