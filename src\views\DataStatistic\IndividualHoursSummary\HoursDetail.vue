<template>
    <div>
        <el-table :data="tableData" border>
            <el-table-column v-for="(config, idx) of tableConfig" :key="idx" v-bind="config"></el-table-column>
        </el-table>
    </div>
</template>

<script>
import service from '@/router/request'

export default {
    name: '',

    data() {
        return {
            data: {},
            tableData: [],
            tableConfig: [
                { label: '录入日期', prop: 'recordDate' },
                { label: '录入工时', prop: 'hours' },
                { label: '任务编号', prop: 'taskNo' },
                { label: '备注', prop: 'remarks' },
            ]
        }
    },

    mounted() {
        this.data = JSON.parse(decodeURIComponent(this.$route.query.data))
        this.getTaskHoursAndTaskList()
    },

    methods: {
        getTaskHoursAndTaskList() {
            service.post('/TaskHours/getIndividualHoursDetail', this.data).then(res => {
                if (res.status == 'succeed') {
                    this.tableData = res.data
                }
            })
        }
    }
}
</script>