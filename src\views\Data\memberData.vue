<template>
  <div>
    <div class="search-bar">
      <el-select v-model="searchParams.field" class="search-item" style="width: 150px">
        <el-option v-for="opt of searchFields" :key="opt.value" v-bind="opt"></el-option>
      </el-select>
      <el-input v-model="searchParams.key" class="search-item" placeholder="请输入搜索值" clearable></el-input>
      <el-button type="primary" class="search-btn" @click="searchData">查询</el-button>
    </div>
    <el-table :data="memberTableData" border>
      <el-table-column v-for="config of memberTableConfig" :key="config.prop" v-bind="config"></el-table-column>
    </el-table>
    <el-pagination
      @size-change="pageSizeChange"
      @current-change="pageNumChange"
      :current-page="paging.pageNum"
      :page-sizes="[5, 10, 20, 40]"
      :page-size="paging.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="paging.total"
    ></el-pagination>
  </div>
</template>

<script>
import service from "../../router/request";
export default {
  name: "memberData",
  data() {
    return {
      searchFields: [
        { label: "姓名", value: "lastname" },
        { label: "工号", value: "loginid" },
        { label: "巴组", value: "G.groname" },
        { label: "小队", value: "ag.groname" },
        { label: "小队编号", value: "tmeno" },
      ],
      searchParams: {
        field: "lastname",
        key: "",
      },
      memberTableData: [],
      memberTableConfig: [
        { label: "姓名", prop: "lastname" },
        { label: "工号", prop: "loginid" },
        { label: "所属巴组", prop: "groupName" },
        { label: "所属小队", prop: "groname" },
        { label: "小队编号", prop: "tmeno", "min-width": 125 },
        { label: "创建时间", prop: "createdate", "min-width": 160 },
      ],
      paging: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
    };
  },
  mounted() {
    this.getMember();
  },
  methods: {
    getMember() {
      service
        .post("/Public/GetMemberForGroup", {
          ...this.paging,
          ...this.searchParams,
        })
        .then((res) => {
          if (res.status == "succeed") {
            this.memberTableData = res.data?.list;
            this.paging.total = res.data?.total;
          }
        });
    },

    searchData() {
      this.paging.pageNum = 1;
      this.getMember();
    },

    pageSizeChange(val) {
      this.paging.pageSize = val;
      this.getMember();
    },

    pageNumChange(val) {
      this.paging.pageNum = val;
      this.getMember();
    },
  },
};
</script>

<style></style>
