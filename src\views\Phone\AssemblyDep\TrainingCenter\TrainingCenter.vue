<template>
  <div class="container">
    <TopNavigationBar title="培训中心"></TopNavigationBar>
    <el-main class="main">
      <div class="grid-container">
        <div v-for="(option, index) in options" :key="index" class="grid-item" @click="navigateTo(option.path)">
          <div class="icon">{{ option.icon || "🔗" }}</div>
          <div class="label">{{ option.label }}</div>
        </div>
      </div>
    </el-main>
  </div>
</template>

<script>
import TopNavigationBar from "@/components/XTopNavigationBar.vue";

export default {
  name: "TrainingCenter",
  components: { TopNavigationBar },
  data() {
    return {
      options: [
        { label: "培训会议", icon: "📅", path: "/trainingMeeting" }, // 日历/会议
        { label: "培训资料", icon: "📚", path: "/trainingMaterials" }, // 资料/书本
      ],
    };
  },

  methods: {
    navigateTo(path) {
      this.$router.push({ path });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding-bottom: env(safe-area-inset-bottom, 20px);
}

.main {
  margin: 0;
  // padding: 20px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.grid-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  padding: 16px;
  .grid-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 16px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #f9f9f9;
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .icon {
      font-size: 24px;
      margin-bottom: 8px;
    }

    .label {
      font-size: 14px;
      color: #333;
    }
  }
}
</style>
