<template>
  <div class="container">
    <TopNavigationBar title="物料领用"></TopNavigationBar>
    <div class="content">
      <div class="options-grid">
        <div
          v-for="(option, index) in options"
          :key="index"
          class="option-card"
          :class="{ disabled: option.permissionAuth && !hasPermission }"
          @click="navigateTo(option)"
        >
          <div class="icon">{{ option.icon }}</div>
          <div class="label">{{ option.label }}</div>
          <div class="description">{{ option.description }}</div>
          <div v-if="option.permissionAuth && !hasPermission" class="lock-icon">🔒</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TopNavigationBar from "@/components/XTopNavigationBar.vue";
import { MessageUtil } from "@/utils/utils";

export default {
  components: { TopNavigationBar },
  name: "MaterialRequisitionIndex",
  data() {
    return {
      options: [
        {
          label: "新建领用单",
          icon: "📝",
          path: "/createRequisition",
          description: "创建新的物料领用申请",
        },
        {
          label: "我的领用单",
          icon: "📋",
          path: "/myRequisitions",
          description: "查看我的领用记录",
        },
        {
          label: "审核管理",
          icon: "✅",
          path: "/requisitionManagement",
          description: "审核和管理所有领用单",
          permissionAuth: true,
        },
      ],
    };
  },
  computed: {
    hasPermission() {
      // 直接访问 store 状态，确保响应式
      const memberRole = this.$store.state.user?.roleId || this.$store.state.user?.roleId || 0;
      return memberRole == 8 || memberRole == 1;
    },
  },
  methods: {
    navigateTo(option) {
      if (option.permissionAuth && !this.hasPermission) {
        return MessageUtil.warning("暂无权限");
      }
      this.$router.push({ path: option.path });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.content {
  flex: 1;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.options-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
  width: 100%;
  max-width: 400px;
}

.option-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    background: rgba(255, 255, 255, 1);
  }

  &:active {
    transform: translateY(-2px);
  }

  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;

    &:hover {
      transform: none;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }
}

.icon {
  font-size: 48px;
  margin-bottom: 16px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.lock-icon {
  position: absolute;
  top: 4px;
  right: 4px;
  font-size: 12px;
  opacity: 0.7;
}

.label {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
}

.description {
  font-size: 14px;
  color: #7f8c8d;
  line-height: 1.4;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .content {
    padding: 16px;
  }

  .options-grid {
    max-width: 100%;
  }

  .option-card {
    padding: 20px;
  }

  .icon {
    font-size: 40px;
    margin-bottom: 12px;
  }

  .label {
    font-size: 18px;
  }

  .description {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .content {
    padding: 12px;
  }

  .option-card {
    padding: 16px;
  }

  .icon {
    font-size: 36px;
    margin-bottom: 10px;
  }

  .label {
    font-size: 16px;
  }
}
</style>
