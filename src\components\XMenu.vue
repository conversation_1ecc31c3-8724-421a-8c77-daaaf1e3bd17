<template>
  <el-menu :default-active="activeMenu" text-color="#5E6E82" active-text-color="#1D7CFA" router>
    <XMenuItems :menuData="menuData" :activeMenu="activeMenu" @menu-click="handleClick" />
  </el-menu>
</template>

<script>
import XMenuItems from "./XMenuItems.vue";

export default {
  name: "TreeMenu",
  components: {
    XMenuItems,
  },
  props: {
    menuData: {
      type: Array,
      required: true,
    },
    activeMenu: {
      type: String,
      default: "",
    },
  },
  methods: {
    handleClick(item) {
      this.$emit("menu-click", item);
    },
  },
};
</script>

<style scoped>
.el-menu {
  border-right: none;
  overflow: auto;
  height: 100%;
}
</style>
