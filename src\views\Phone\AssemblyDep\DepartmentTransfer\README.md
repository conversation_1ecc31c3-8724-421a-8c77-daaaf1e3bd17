# 部门调动模块

## 功能说明

部门调动模块用于管理员工在不同部门之间的调动操作。

## 主要功能

### 1. 部门树形展示

- 按照部门层级关系显示树形结构
- 根部门和子部门分层展示
- 支持部门展开/收起
- 显示每个部门的员工数量（包括子部门）
- 区分直属成员和子部门成员

### 2. 员工搜索

- 支持按员工姓名搜索
- 支持按工号搜索
- 实时搜索结果过滤

### 3. 员工调动

- 选择要调动的员工
- 选择目标部门
- 填写调动原因（可选）
- 确认调动操作

## API 接口

### 获取部门和员工数据

```
GET /api/Department/GetDepartmentsAndEmployees
```

**参数：**

- `departmentIds` (可选): 部门 ID 列表，默认为 [148, 149]

**返回数据结构：**

```json
[
  {
    "Id": 148,
    "Name": "部门名称",
    "SupDepId": null,
    "Members": [
      {
        "LoginId": "员工工号",
        "Name": "员工姓名"
      }
    ]
  },
  {
    "Id": 149,
    "Name": "子部门名称",
    "SupDepId": 148,
    "Members": [
      {
        "LoginId": "员工工号",
        "Name": "员工姓名"
      }
    ]
  }
]
```

### 员工部门调动

```
PUT /api/Department/{resourceId}/transfer?newDepartmentId={newDepartmentId}
```

**参数：**

- `resourceId`: 员工资源 ID（当前使用 LoginId）
- `newDepartmentId`: 目标部门 ID

## 移动端适配

- 响应式设计，支持各种屏幕尺寸
- 触摸友好的交互设计
- 优化的移动端对话框
- 安全区域适配

## 权限控制

部门调动功能需要管理员权限（permissionAuth: true），通过 `havePermissionRole(8)` 进行权限验证。

## 使用说明

1. 在总装部主页面点击"部门调动"进入
2. 查看部门树形结构，点击部门名称展开/收起
3. 根部门显示蓝色边框，子部门显示灰色边框并缩进
4. 直属成员和子部门成员分别显示
5. 使用搜索框快速查找员工（支持跨部门搜索）
6. 点击员工旁边的"调动"按钮
7. 在弹出的对话框中选择目标部门
8. 可选填写调动原因
9. 确认调动操作

## 注意事项

1. 调动操作需要二次确认
2. 调动成功后会自动刷新数据
3. 如果 API 返回错误，会显示具体错误信息
4. 目标部门列表会自动排除当前员工所在部门
