<template>
  <div class="salary-container">
    <TopNavigationBar title="工资条"></TopNavigationBar>

    <el-main class="salary-main">
      <!-- 月份选择器 -->
      <div class="month-selector">
        <div class="selector-wrapper">
          <i class="el-icon-date selector-icon"></i>
          <el-date-picker
            v-model="date"
            type="month"
            format="yyyy年MM月"
            value-format="yyyy-MM"
            placeholder="选择查询月份"
            @change="handleMonthChange"
            class="month-picker"
            :clearable="false"
          ></el-date-picker>
        </div>
      </div>

      <!-- 工资条内容 -->
      <div class="salary-content">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-state">
          <div class="loading-spinner"></div>
          <p>加载中...</p>
        </div>

        <!-- 工资数据 -->
        <div v-else-if="data" class="salary-data">
          <!-- 基本信息卡片 -->
          <div class="info-card">
            <div class="card-header">
              <h3>基本信息</h3>
              <div class="month-badge">{{ formatMonth(date) }}</div>
            </div>
            <div class="info-grid">
              <div class="info-item" v-for="item in basicInfo" :key="item.prop">
                <span class="label">{{ item.label }}</span>
                <span class="value">{{ data[item.prop] || "-" }}</span>
              </div>
            </div>
          </div>

          <!-- 工时工资卡片 -->
          <div class="salary-card">
            <div class="card-header">
              <h3>工时工资明细</h3>
            </div>
            <div class="salary-grid">
              <div class="salary-item" v-for="item in salaryInfo" :key="item.prop">
                <span class="label">{{ item.label }}</span>
                <span class="value" :class="{ highlight: item.highlight }">
                  {{ formatValue(data[item.prop], item.type) }}
                </span>
              </div>
            </div>
          </div>

          <!-- 汇总信息卡片 -->
          <div class="summary-card">
            <div class="card-header">
              <h3>汇总信息</h3>
            </div>
            <div class="summary-grid">
              <div class="summary-item" v-for="item in summaryInfo" :key="item.prop">
                <span class="label">{{ item.label }}</span>
                <span class="value highlight">{{ formatValue(data[item.prop], item.type) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 无数据状态 -->
        <div v-else class="empty-state">
          <div class="empty-icon">📊</div>
          <h3>暂无工资数据</h3>
          <p>该月份暂无工资记录，请选择其他月份查看</p>
        </div>
      </div>
    </el-main>
  </div>
</template>

<script>
import service from "@/router/request";
import TopNavigationBar from "@/components/XTopNavigationBar.vue";

export default {
  name: "SalarySheet",
  components: {
    TopNavigationBar,
  },
  data: () => ({
    date: null,
    loginId: 0,
    data: null,
    loading: false,
    basicInfo: [
      { label: "姓名", prop: "memname" },
      { label: "工号", prop: "memno" },
      { label: "核算月份", prop: "mmonth" },
      { label: "所属巴组", prop: "groname" },
      { label: "巴长", prop: "grohmname" },
      { label: "所属团队", prop: "tmename" },
      { label: "队长", prop: "tmehmname" },
    ],
    salaryInfo: [
      { label: "团队任务工时", prop: "teamTaskManHours", type: "hours" },
      { label: "团队任务工资", prop: "teamTaskWage", type: "currency" },
      { label: "个人任务工时", prop: "individualTaskManHours", type: "hours" },
      { label: "个人任务工资", prop: "individualTaskWage", type: "currency" },
    ],
    summaryInfo: [
      { label: "合计工时", prop: "smhours", type: "hours", highlight: true },
      { label: "合计工资", prop: "smwages", type: "currency", highlight: true },
      { label: "平均工时价", prop: "pphs", type: "currency", highlight: true },
    ],
  }),
  mounted() {
    this.loginId = this.$route.query.loginid || 0;
    this.date = this.$moment().subtract(1, "months").format("yyyy-MM");
    this.GetPeopleAccount();
  },
  methods: {
    handleMonthChange() {
      this.GetPeopleAccount();
    },
    GetPeopleAccount() {
      this.loading = true;
      service
        .get("/AnonymousApi/GetSalarySheet", {
          params: { month: this.date, loginId: this.loginId },
        })
        .then((res) => {
          if (res.status == "succeed") {
            this.data = res.data || null;
            if (this.data) {
              this.data.teamTaskManHours = this.data.tachours;
              this.data.teamTaskWage = this.data.tacwages;
              this.data.individualTaskManHours = this.data.ttmhours + this.data.tpthours;
              this.data.individualTaskWage = this.data.ttmwages + this.data.tptwages;
            }
          }
        })
        .catch((error) => {
          console.error("获取工资数据失败:", error);
          this.$message.error("获取工资数据失败，请稍后重试");
        })
        .finally(() => {
          this.loading = false;
        });
    },
    formatMonth(date) {
      if (!date) return "";
      return this.$moment(date).format("YYYY年MM月");
    },
    formatValue(value, type) {
      if (value === null || value === undefined || value === "") return "-";

      switch (type) {
        case "currency":
          return `¥${Number(value).toFixed(2)}`;
        case "hours":
          return `${Number(value).toFixed(1)}h`;
        default:
          return value;
      }
    },
  },
};
</script>

<style scoped>
.salary-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  /* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: env(safe-area-inset-bottom, 20px); */
}

.salary-main {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin: 0;
}

/* 月份选择器 */
.month-selector {
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.selector-wrapper {
  position: relative;
  max-width: 400px;
  margin: 0 auto;
}

.selector-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  font-size: 16px;
  z-index: 1;
}

.month-picker {
  width: 100%;
}

.month-picker >>> .el-input__inner {
  padding-left: 40px;
  border-radius: 25px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  font-size: 16px;
  height: 50px;
  line-height: 50px;
}

.month-picker >>> .el-input__suffix {
  right: 15px;
}

/* 工资内容区域 */
.salary-content {
  padding: 0 clamp(12px, 3vw, 20px);
  max-width: 800px;
  margin: 0 auto;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: white;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 卡片通用样式 */
.info-card,
.salary-card,
.summary-card {
  background: white;
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #e9ecef;
}

.card-header h3 {
  margin: 0;
  font-size: clamp(16px, 4vw, 20px);
  font-weight: 600;
  color: #333;
}

.month-badge {
  background: #409eff;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

/* 信息网格 */
.info-grid,
.salary-grid,
.summary-grid {
  padding: 20px;
}

.info-item,
.salary-item,
.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.info-item:last-child,
.salary-item:last-child,
.summary-item:last-child {
  border-bottom: none;
}

.label {
  font-size: clamp(14px, 3.5vw, 16px);
  color: #666;
  font-weight: 500;
}

.value {
  font-size: clamp(14px, 3.5vw, 16px);
  color: #333;
  font-weight: 600;
  text-align: right;
}

.value.highlight {
  color: #e6a23c;
  font-weight: bold;
  font-size: clamp(16px, 4vw, 18px);
}

/* 汇总卡片特殊样式 */
.summary-card {
  background: linear-gradient(135deg, #fff7e6 0%, #fff2d9 100%);
  border: 2px solid #ffd591;
}

.summary-card .card-header {
  background: linear-gradient(135deg, #ffd591 0%, #ffcc7a 100%);
}

.summary-card .card-header h3 {
  color: #d46b08;
}

.summary-item {
  padding: 16px 0;
}

.summary-item .label {
  color: #d46b08;
  font-weight: 600;
}

.summary-item .value {
  color: #d46b08;
  font-size: clamp(18px, 4.5vw, 22px);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: white;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.8;
}

.empty-state h3 {
  margin: 0 0 12px 0;
  font-size: clamp(18px, 4.5vw, 24px);
  font-weight: 600;
}

.empty-state p {
  margin: 0;
  font-size: clamp(14px, 3.5vw, 16px);
  opacity: 0.8;
  line-height: 1.5;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .month-selector {
    padding: 16px;
  }

  .salary-content {
    padding: 0 12px;
  }

  .info-card,
  .salary-card,
  .summary-card {
    margin-bottom: 12px;
    border-radius: 8px;
  }

  .card-header {
    padding: 12px 16px;
  }

  .info-grid,
  .salary-grid,
  .summary-grid {
    padding: 16px;
  }

  .info-item,
  .salary-item,
  .summary-item {
    padding: 10px 0;
  }
}

@media (max-width: 480px) {
  .month-selector {
    padding: 12px;
  }

  .month-picker >>> .el-input__inner {
    height: 44px;
    line-height: 44px;
    font-size: 14px;
  }

  .card-header {
    padding: 10px 12px;
  }

  .info-grid,
  .salary-grid,
  .summary-grid {
    padding: 12px;
  }

  .info-item,
  .salary-item,
  .summary-item {
    padding: 8px 0;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .value {
    text-align: left;
    align-self: flex-end;
  }

  .summary-item {
    padding: 12px 0;
  }
}
</style>
