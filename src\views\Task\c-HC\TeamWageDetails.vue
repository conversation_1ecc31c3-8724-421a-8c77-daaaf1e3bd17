<template>
  <div>
    <div class="mb-2 top-bar">
      <el-button @click="$router.back()" size="medium" icon="el-icon-back" circle></el-button>
      <div>
        <el-button @click="printIframeContent" type="primary" plain>打印</el-button>
        <el-button @click="exportTable" type="warning" class="search-btn" plain>导出</el-button>
      </div>
    </div>
    <div style="overflow: hidden; min-width: 1000px">
      <div class="print-content">
        <h1>团队工资汇总</h1>
        <div class="mb-2" style="width: 100%">
          <el-descriptions :column="12" direction="vertical" border>
            <el-descriptions-item v-for="item of teamPayrollSummaryConfig" :key="item.prop" :label="item.label">{{
              teamPayroll[item.prop]
            }}</el-descriptions-item>
          </el-descriptions>
        </div>
        <h1 style="width: 100%; position: relative; text-align: center">
          个人工资明细
          <div class="hidden" style="position: absolute; right: 0; top: -8px"></div>
        </h1>
        <el-table ref="personalPayrollTableRef" :data="personalPayrollTableData" show-summary border class="mb-2">
          <el-table-column label="姓名" prop="staffname" min-width="80"></el-table-column>
          <el-table-column label="出勤工时" prop="timet" min-width="100px"> </el-table-column>
          <el-table-column label="分配百分比" prop="posrate" min-width="95"> </el-table-column>
          <el-table-column label="分配金" prop="poswage" min-width="70"></el-table-column>
          <el-table-column label="当月总额" prop="monthywsun" min-width="80"></el-table-column>
          <el-table-column label="本人签字" min-width="160"></el-table-column>
        </el-table>
        <h1>计件任务明细</h1>
        <el-table
          ref="jiJianTaskTableRef"
          :data="jiJianTaskTableData"
          border
          class="mb-2"
          :max-height="tableMaxHeight"
          show-summary
        >
          <el-table-column
            v-for="(item, idx) of jiJianTaskTableConfig"
            :key="`jiJian-${item.prop}-${idx}`"
            v-bind="item"
          ></el-table-column>
        </el-table>
        <h1>计时任务明细</h1>
        <el-table
          ref="jiShiTaskTableRef"
          :data="jiShiTaskTableData"
          border
          class="mb-2"
          :max-height="tableMaxHeight"
          show-summary
        >
          <el-table-column
            v-for="item of jiShiTaskTableConfig"
            :key="`jiShi-${item.prop}`"
            v-bind="item"
          ></el-table-column>
        </el-table>
        <h1>临时任务明细</h1>
        <el-table
          ref="linShiTaskTableRef"
          :data="linShiTaskTableData"
          border
          class="mb-2"
          :max-height="tableMaxHeight"
          show-summary
        >
          <el-table-column
            v-for="item of linShiTaskTableConfig"
            :key="`linShi-${item.prop}`"
            v-bind="item"
          ></el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 员工弹框 -->
    <el-dialog title="员工列表" :visible.sync="memberDialogTableVisible">
      <el-table
        :data="memberList"
        @current-change="memberTableCurrentChange"
        border
        highlight-current-row
        height="500px"
      >
        <el-table-column prop="memname" label="姓名"></el-table-column>
        <el-table-column prop="jobtitlename" label="职务"></el-table-column>
        <el-table-column prop="memrole" label="角色"></el-table-column>
      </el-table>
      <span slot="footer">
        <el-button @click="memberDialogCancel">取 消</el-button>
        <el-button type="primary" @click="memnerDialogConfirm">确 定</el-button>
      </span>
    </el-dialog>

    <div
      style="
        height: 0;
        overflow: hidden;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        z-index: 3;
        background-color: #fff;
        pointer-events: none;
      "
    >
      <div ref="printContent" id="printContent" class="print-content print">
        <h1>团队工资汇总</h1>
        <table cellpadding="0" cellspacing="0">
          <thead>
            <tr>
              <th
                v-for="config of teamPayrollSummaryConfig"
                :key="config.prop"
                :style="{ width: `${config.width}px`, minWidth: `${config['min-width']}px` }"
              >
                {{ config.label }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td v-for="(config, idx) of teamPayrollSummaryConfig" :key="config.prop + idx">
                {{ teamPayroll[config.prop] }}
              </td>
            </tr>
          </tbody>
        </table>
        <h1>个人工资明细</h1>
        <table class="t1" cellpadding="0" cellspacing="0">
          <thead>
            <tr>
              <th>姓名</th>
              <th>出勤工时</th>
              <th>分配百分比</th>
              <th>分配金</th>
              <th>当月总额</th>
              <th>本人签字</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, idx) of summaryData(personalPayrollTableData, 'staffname')" :key="idx">
              <td>{{ item.staffname }}</td>
              <td>{{ item.timet }}</td>
              <td>{{ item.posrate }}</td>
              <td>{{ item.poswage }}</td>
              <td>{{ item.monthywsun }}</td>
              <td></td>
            </tr>
          </tbody>
        </table>
        <table cellpadding="0" cellspacing="0">
          <tbody>
            <tr>
              <th colspan="1" style="width: 100px; padding: 20px; font-size: 14px">巴长审核</th>
              <td colspan="1"></td>
              <th colspan="1" style="width: 100px; padding: 20px; font-size: 14px">部长审核</th>
              <td colspan="1"></td>
            </tr>
          </tbody>
        </table>
        <template v-if="jiJianTaskTableData && jiJianTaskTableData.length">
          <h1>计件任务明细</h1>
          <table cellpadding="0" cellspacing="0">
            <thead>
              <tr>
                <th v-for="config of jiJianTaskTablePConfig">{{ config.label }}</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, idx) of summaryData(jiJianTaskTableData, 'prono')" :key="idx">
                <td
                  v-for="config of jiJianTaskTablePConfig"
                  :style="{ width: `${config.width}px`, minWidth: `${config['min-width']}px` }"
                >
                  {{ item[config.prop] }}
                </td>
              </tr>
            </tbody>
          </table>
        </template>
        <template v-if="jiShiTaskTableData && jiShiTaskTableData.length">
          <h1>计时任务明细</h1>
          <table cellpadding="0" cellspacing="0">
            <thead>
              <tr>
                <th v-for="config of jiShiTaskTablePConfig">{{ config.label }}</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, idx) of summaryData(jiShiTaskTableData, 'ttdextername')" :key="idx">
                <td
                  v-for="config of jiShiTaskTablePConfig"
                  :style="{ width: `${config.width}px`, minWidth: `${config['min-width']}px` }"
                >
                  {{ item[config.prop] }}
                </td>
              </tr>
            </tbody>
          </table>
        </template>
        <template v-if="linShiTaskTableData && linShiTaskTableData.length">
          <h1>临时任务明细</h1>
          <table cellpadding="0" cellspacing="0">
            <thead>
              <tr>
                <th v-for="config of linShiTaskTablePConfig">{{ config.label }}</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, idx) of summaryData(linShiTaskTableData, 'ttdextername')" :key="idx">
                <td
                  v-for="config of linShiTaskTablePConfig"
                  :style="{ width: `${config.width}px`, minWidth: `${config['min-width']}px` }"
                >
                  {{ item[config.prop] }}
                </td>
              </tr>
            </tbody>
          </table>
        </template>
      </div>
    </div>

    <iframe id="frame" src="" frameborder="0" style="display: none"> </iframe>
  </div>
</template>

<script>
import service from "@/router/request";

import { downloadXLSX } from "@/utils/utils";
import * as XLSX from "xlsx";

export default {
  name: "teamwagedetails",
  data() {
    return {
      teamPayroll: {},
      teamPayrollSummaryConfig: [
        { label: "所属巴组", value: "", prop: "groname", width: "60" },
        { label: "巴长", value: "", prop: "grohmname", width: "40" },
        { label: "团队名称", value: "", prop: "tmename" },
        { label: "队长", value: "", prop: "tmehmname", width: "40" },
        { label: "核算月份", value: "", prop: "mmonth", width: "60" },
        { label: "计时工资", value: "", prop: "ttmwages" },
        { label: "计件工资", value: "", prop: "tacwages" },
        { label: "临时工资", value: "", prop: "tptwages" },
        { label: "合计工时", value: "", prop: "smhours" },
        { label: "合计工资", value: "", prop: "smwages" },
        { label: "岗位分配", value: 0, prop: "allotment", width: "60" },
        { label: "平均时价", value: 0, prop: "averageprice", width: "60" },
      ],
      personalPayrollTableData: [], // 个人工资明细表
      jiJianTaskTableData: [], // 计件任务明细表
      jiJianTaskTableConfig: [
        { label: "工程号", prop: "prono", width: "120" },
        { label: "任务编号", prop: "tadno", width: "120" },
        { label: "部位", prop: "mpaname", "min-width": "80" },
        { label: "组件", prop: "sasname", "min-width": "80" },
        { label: "子件", prop: "ssuname", "min-width": "80" },
        { label: "工序", prop: "wprno", width: "70" },
        { label: "任务金额", prop: "taswage", width: "100" },
        { label: "累计 %", prop: "tadcomp_rate", width: "90" },
        { label: "当月 %", prop: "tadcomp_rate1", width: "90" },
        { label: "剩余 %", prop: "residue", width: "80" },
        { label: "录入工时", prop: "hours", width: "80" },
        { label: "当月金额", prop: "monthAmount", width: "100" },
        { label: "任务备注", prop: "tasdremarks", "min-width": "160" },
      ],
      jiShiTaskTableData: [], // 计时任务明细表
      jiShiTaskTableConfig: [
        { label: "姓名", prop: "ttdextername", width: "90" },
        { label: "任务开始", prop: "ttmsdate", width: "125" },
        { label: "任务结束", prop: "ttmedate", width: "125" },
        { label: "工程号", prop: "ttdprono", width: "140" },
        { label: "任务内容", prop: "ttmcontent", "min-width": "200" },
        { label: "预计工时", prop: "ttdhour", width: "80" },
        { label: "录入工时", prop: "hours", width: "80" },
        { label: "工时价", prop: "hourlyWage", width: "80" },
        { label: "预计工价", prop: "ttdwage", width: "90" },
        { label: "实际工价", prop: "wages", width: "80" },
        { label: "任务备注", prop: "ttdremarks", "min-width": "200" },
      ],
      linShiTaskTableData: [], // 临时任务明细表
      linShiTaskTableConfig: [
        { label: "工程号", prop: "ttdprono", width: "140" },
        { label: "姓名", prop: "ttdextername", width: "90" },
        { label: "任务内容", prop: "ttmcontent", "min-width": "100" },
        { label: "数量", prop: "ttdhour", width: "80" },
        { label: "单价", prop: "ttdprice", width: "80" },
        { label: "工价", prop: "ttdwage", width: "80" },
        { label: "录入工时", prop: "hours", width: "80" },
        { label: "任务备注", prop: "ttdremarks", "min-width": "100" },
        { label: "任务开始", prop: "ttmsdate", width: "125" },
        { label: "任务结束", prop: "ttmedate", width: "125" },
      ],
      memberList: [],
      memberDialogTableVisible: false,
      memberTableCurrentRow: null,
      tableMaxHeight: "500",
      isExport: false,

      jiJianTaskTablePConfig: [
        { label: "工程号", prop: "prono", width: "80" },
        { label: "部位", prop: "mpaname", width: "50" },
        { label: "组件", prop: "sasname", width: "50" },
        { label: "子件", prop: "ssuname", width: "50" },
        { label: "工序", prop: "wprno", width: "50" },
        { label: "任务金额", prop: "taswage", width: "30" },
        { label: "累计 %", prop: "tadcomp_rate", width: "30" },
        { label: "当月 %", prop: "tadcomp_rate1", width: "30" },
        { label: "剩余 %", prop: "residue", width: "30" },
        { label: "录入工时", prop: "hours", width: "30" },
        { label: "当月金额", prop: "monthAmount", width: "60" },
        { label: "任务备注", prop: "tasdremarks", "min-width": "80" },
      ],
      jiShiTaskTablePConfig: [
        { label: "姓名", prop: "ttdextername", width: "40" },
        { label: "任务开始", prop: "ttmsdate", width: "70" },
        { label: "任务结束", prop: "ttmedate", width: "70" },
        { label: "工程号", prop: "ttdprono", width: "70" },
        { label: "任务内容", prop: "ttmcontent" },
        { label: "预计工时", prop: "ttdhour", width: "30" },
        { label: "录入工时", prop: "hours", width: "30" },
        { label: "工时价", prop: "hourlyWage", width: "30" },
        { label: "预计工价", prop: "ttdwage", width: "40" },
        { label: "实际工价", prop: "wages", width: "40" },
        { label: "任务备注", prop: "ttdremarks" },
      ],
      linShiTaskTablePConfig: [
        { label: "工程号", prop: "ttdprono", width: "70" },
        { label: "姓名", prop: "ttdextername", width: "40" },
        { label: "任务内容", prop: "ttmcontent" },
        { label: "数量", prop: "ttdhour", width: "30" },
        { label: "单价", prop: "ttdprice", width: "30" },
        { label: "工价", prop: "ttdwage", width: "30" },
        { label: "录入工时", prop: "hours", width: "30" },
        { label: "任务备注", prop: "ttdremarks" },
        { label: "任务开始", prop: "ttmsdate", width: "70" },
        { label: "任务结束", prop: "ttmedate", width: "70" },
      ],
    };
  },
  mounted() {
    this.init();
  },

  computed: {},

  methods: {
    init() {
      this.teamPayroll = JSON.parse(decodeURIComponent(this.$route.query.teamPayroll));
      this.getPermonwage();
      this.getJiJian();
      this.getJiShi();
      this.getLinShi();
    },

    //
    getPermonwage() {
      service
        .post("/TeamAccount/GetPermonwage", {
          tme: this.teamPayroll.tmeno,
          tmonth: this.teamPayroll.mmonth,
        })
        .then((res) => {
          if (res.status == "succeed") {
            this.personalPayrollTableData = res.data.list;
            this.personalPayrollTableData.forEach((item, idx) => {
              item.timet = +item.timet;
              item.mgrade = +item.mgrade;
            });
          }
        });
    },

    timetChange(row = null) {
      if (row) {
        row.timet = +row.timet;
        row.timet == 0 && (row.poswage = 0);
      }
      let totalHour = this.personalPayrollTableData.reduce((val, item) => (val += +item.timet), 0);
      this.personalPayrollTableData.forEach(
        (item) =>
          (item.monthywsun = totalHour
            ? (
                ((this.teamPayroll.smwages - this.teamPayroll.allotment) / totalHour) * item.timet +
                +item.poswage
              ).toFixed(2)
            : 0)
      );
    },

    posrateChange(row) {
      row.posrate = +row.posrate;
      row.poswage = row.timet ? (this.teamPayroll.tacwages * row.posrate * 0.01).toFixed(2) : 0;
      this.teamPayroll.allotment = this.personalPayrollTableData.reduce((val, item) => (val += +item.poswage), 0);
      this.teamPayroll.allotment = this.teamPayroll.allotment.toFixed(2);
      this.teamPayroll.averageCurrentPrice = (
        (this.teamPayroll.smwages - this.teamPayroll.allotment) /
        +this.teamPayroll.smhours
      ).toFixed(2);
      this.timetChange();
    },

    // /api/TeamAccount/GetJiJian
    getJiJian() {
      service
        .post("/TeamAccount/GetJiJian", {
          tme: this.teamPayroll.tmeno,
          tmonth: this.teamPayroll.mmonth,
        })
        .then((res) => {
          if (res.status == "succeed") {
            (res.data?.list || []).forEach((item) => {
              item.residue = 100 - item.tadcomp_rate;

              item.monthAmount = parseFloat((item.taswage * item.tadcomp_rate1 * 0.01).toFixed(4));
            });
            this.jiJianTaskTableData = res.data.list;
          }
        });
    },

    getJiShi() {
      service
        .post("/TeamAccount/GetJiShi", {
          tme: this.teamPayroll.tmeno,
          tmonth: this.teamPayroll.mmonth,
        })
        .then((res) => {
          if (res.status == "succeed") {
            this.jiShiTaskTableData = res.data;
            this.jiShiTaskTableData.forEach((row) => {
              row.ttdwage = row.hourlyWage * row.ttdhour;
              row.ttdhour = parseFloat(row.ttdhour);
              row.ttdwage = parseFloat(row.ttdwage);
            });
          }
        });
    },

    getLinShi() {
      service
        .post("/TeamAccount/GetLinShi", {
          tme: this.teamPayroll.tmeno,
          tmonth: this.teamPayroll.mmonth,
        })
        .then((res) => {
          if (res.status == "succeed") {
            this.linShiTaskTableData = res.data;
            this.linShiTaskTableData.forEach((row) => {
              row.ttdhour = parseFloat(row.ttdhour);
              row.ttdprice = parseFloat(row.ttdprice);
              row.ttdwage = parseFloat(row.ttdwage);
            });
          }
        });
    },

    addPersonalPayroll() {
      service.post("/TeamAccount/GetTmembers", {}).then((res) => {
        if (res.status == "succeed") {
          this.memberList = res.data;
          this.memberDialogTableVisible = true;
        }
      });
    },

    memberTableCurrentChange(row) {
      this.memberTableCurrentRow = row;
    },

    memberDialogCancel() {
      this.memberTableCurrentRow = null;
      this.memberDialogTableVisible = false;
    },

    memnerDialogConfirm() {
      service
        .post("/TeamAccount/InsertPermonwage", [
          {
            rolename: this.memberTableCurrentRow.memrole,
            staffname: this.memberTableCurrentRow.memname,
            staffno: this.memberTableCurrentRow.memno,
            tme_no: this.teamPayroll.tmeno,
            tmonth: this.teamPayroll.mmonth,
          },
        ])
        .then((res) => {
          if (res.status == "succeed") {
            this.getPermonwage();
            this.$message.success("添加成功");
          } else {
            this.$message.error(res.message);
          }
        });

      this.memberDialogTableVisible = false;
    },

    printIframeContent() {
      let printFrame = document.getElementById("frame");
      let style = `@page{size:auto;margin:15px}.print-content{display:flex;flex-direction:column;align-items:center;height:fit-content}table{width:100%}table,td,th{border:1px solid #000;border-collapse:collapse}th,td{text-align:left;padding:5px}th{font-weight:bold;font-size:14px;background-color:#c0c4cc;-webkit-print-color-adjust:exact}td{font-size:12px}tr{page-break-inside:avoid}.t1 td{font-size:12px;padding:15px 5px}.print-content{display:flex;flex-direction:column;align-items:center;height:fit-content} h1{font-size:18px;margin:0;text-align:center}`;
      let content =
        `<html><head><title>Print</title><style>${style}</style></head><body>` +
        document.getElementById("printContent").innerHTML +
        "</body></html>";
      printFrame.contentWindow.document.open();
      printFrame.contentWindow.document.write(content);
      printFrame.contentWindow.document.close();
      printFrame.contentWindow.focus();
      printFrame.contentWindow.print();
    },

    summaryData(data, prop) {
      let obj = {};
      if (data?.length) {
        for (let key in data[0]) {
          if (key == "tadcomp_rate" || key == "tadcomp_rate1" || key == "residue") continue;
          let s = data.reduce((val, item) => (val += +item[key]), 0);
          !isNaN(s) && (obj[key] = Math.round(s * 100) / 100);
        }
      }
      obj[prop] = "合计";
      return obj && [...data, obj];
    },

    exportTable() {
      this.isExport = true;
      this.$nextTick(() => {
        let ws = XLSX.utils.table_to_sheet(this.$refs.personalPayrollTableRef.$el, { origin: { c: 0, r: 1 } });
        let jiJianTaskTableR = this.personalPayrollTableData.length + 2 + 1 + 1;
        XLSX.utils.sheet_add_dom(ws, this.$refs.jiJianTaskTableRef.$el, {
          origin: { c: 0, r: jiJianTaskTableR },
        });
        let jiShiTaskTableR = jiJianTaskTableR + this.jiJianTaskTableData.length + 1 + 1;
        XLSX.utils.sheet_add_dom(ws, this.$refs.jiShiTaskTableRef.$el, {
          origin: { c: 0, r: jiShiTaskTableR },
        });
        let linShiTaskTableR = jiShiTaskTableR + this.jiShiTaskTableData.length + 1 + 1;
        XLSX.utils.sheet_add_dom(ws, this.$refs.linShiTaskTableRef.$el, {
          origin: { c: 0, r: linShiTaskTableR },
        });
        ws.A1 = { t: "s", v: "个人工资明细" };
        ws[`A${jiJianTaskTableR}`] = { t: "s", v: "计件任务明细" };
        ws[`A${jiShiTaskTableR}`] = { t: "s", v: "计时任务明细" };
        ws[`A${linShiTaskTableR}`] = { t: "s", v: "临时任务明细" };
        let wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws);
        downloadXLSX(wb, "template");
        this.isExport = false;
      });
      //
    },
  },
};
</script>

<style scoped>
.print-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: fit-content;
}

table {
  width: 100%;
}

table,
td,
th {
  border: 1px solid #000;
  border-collapse: collapse;
}

th,
td {
  text-align: left;
  padding: 5px;
}

th {
  font-weight: bold;
  font-size: 14px;
  background-color: #c0c4cc;
  -webkit-print-color-adjust: exact;
}

td {
  font-size: 12px;
}

tr {
  page-break-inside: avoid;
}

.t1 td {
  font-size: 12px;
  padding: 15px 5px;
}

.print-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: fit-content;
}

.print h1 {
  font-size: 18px;
  margin: 0 !important;
}

@media print {
  table {
    width: 100%;
  }

  table,
  td,
  th {
    border: 1px solid #000;
    border-collapse: collapse;
  }

  th,
  td {
    text-align: left;
    padding: 5px;
  }

  th {
    font-weight: bold;
    font-size: 14px;
    background-color: #c0c4cc;
    -webkit-print-color-adjust: exact;
  }

  td {
    font-size: 10px;
  }

  tr {
    page-break-inside: avoid;
  }

  .t1 td {
    font-size: 12px;
    padding: 15px 5px;
  }

  .print-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: fit-content;
  }

  .print h1 {
    font-size: 18px;
    margin: 0 !important;
  }
}

.top-bar {
  display: flex;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 1;
}
</style>
