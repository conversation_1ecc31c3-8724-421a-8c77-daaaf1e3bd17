-- 创建物料领用表
CREATE TABLE MaterialRequisitions (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    ApplicantId NVARCHAR(50) NOT NULL,                -- 申请人ID
    ApplicantName NVARCHAR(50) NOT NULL,              -- 申请人姓名
    RequisitionType TINYINT NOT NULL,                 -- 领用类型：1=新领, 2=以旧换新
    MaterialId INT NOT NULL,                          -- 物品ID
    MaterialName NVARCHAR(100) NOT NULL,              -- 物品名称
    Quantity INT NOT NULL,                            -- 数量
    Unit NVARCHAR(20) NOT NULL,                       -- 单位
    Purpose NVARCHAR(500) NOT NULL,                   -- 用途说明
    ExpectedUseDate DATE NOT NULL,                    -- 预计使用时间
    OldMaterialCondition TINYINT NULL,                -- 旧物品状态：1=损坏, 2=磨损, 3=过期, 0=其他
    OldMaterialDescription NVARCHAR(500) NULL,        -- 旧物品描述
    Remark NVARCHAR(500) NULL,                        -- 备注
    Attachments NVARCHAR(MAX) NULL,                   -- 附件（JSON格式存储）
    Status TINYINT NOT NULL,                          -- 状态：1=待审核, 2=已通过, 3=已拒绝, 4=已发放
    CreateTime DATETIME NOT NULL,                     -- 创建时间
    ApproveTime DATETIME NULL,                        -- 审核时间
    ApproveRemark NVARCHAR(500) NULL,                 -- 审核意见
    ApproverId NVARCHAR(50) NULL,                     -- 审核人ID
    ApproverName NVARCHAR(50) NULL,                   -- 审核人姓名
    DistributionTime DATETIME NULL,                   -- 发放时间
    DistributorId NVARCHAR(50) NULL,                  -- 发放人ID
    DistributorName NVARCHAR(50) NULL,                -- 发放人姓名
    CONSTRAINT CK_MaterialRequisitions_RequisitionType CHECK (RequisitionType IN (1, 2)),
    CONSTRAINT CK_MaterialRequisitions_OldMaterialCondition CHECK (OldMaterialCondition IS NULL OR OldMaterialCondition IN (0, 1, 2, 3)),
    CONSTRAINT CK_MaterialRequisitions_Status CHECK (Status IN (1, 2, 3, 4))
);

-- 创建物品表
CREATE TABLE Materials (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    Name NVARCHAR(100) NOT NULL,                      -- 物品名称
    Type TINYINT NOT NULL,                            -- 物品类型：1=劳保用品, 2=工具, 3=文具
    Unit NVARCHAR(20) NOT NULL,                       -- 单位
    Remark NVARCHAR(500) NULL,                        -- 备注
    IsActive BIT NOT NULL DEFAULT 1,                  -- 是否启用：1=启用, 0=禁用
    CreateTime DATETIME NOT NULL DEFAULT GETDATE(),   -- 创建时间
    UpdateTime DATETIME NOT NULL DEFAULT GETDATE(),   -- 更新时间
    CONSTRAINT CK_Materials_Type CHECK (Type IN (1, 2, 3))
);

-- 创建索引
CREATE INDEX IX_MaterialRequisitions_ApplicantId ON MaterialRequisitions(ApplicantId);
CREATE INDEX IX_MaterialRequisitions_Status ON MaterialRequisitions(Status);
CREATE INDEX IX_MaterialRequisitions_CreateTime ON MaterialRequisitions(CreateTime);
CREATE INDEX IX_MaterialRequisitions_MaterialId ON MaterialRequisitions(MaterialId);
CREATE INDEX IX_MaterialRequisitions_RequisitionType ON MaterialRequisitions(RequisitionType);

CREATE INDEX IX_Materials_Name ON Materials(Name);
CREATE INDEX IX_Materials_Type ON Materials(Type);
CREATE INDEX IX_Materials_IsActive ON Materials(IsActive);

-- 添加示例数据
INSERT INTO Materials (Name, Type, Unit, Remark, IsActive)
VALUES 
    (N'安全帽', 1, N'顶', N'工地用安全帽', 1),
    (N'防护手套', 1, N'双', N'耐磨防护手套', 1),
    (N'安全鞋', 1, N'双', N'防砸防刺穿安全鞋', 1),
    (N'电工工具套装', 2, N'套', N'包含螺丝刀、钳子等工具', 1),
    (N'手电钻', 2, N'台', N'充电式手电钻', 1),
    (N'扳手套装', 2, N'套', N'多规格扳手套装', 1),
    (N'A4纸', 3, N'包', N'500张/包', 1),
    (N'签字笔', 3, N'支', N'黑色中性笔', 1),
    (N'文件夹', 3, N'个', N'A4文件收纳夹', 1);

-- 添加存储过程：获取物品列表
CREATE PROCEDURE sp_GetMaterialList
    @Search NVARCHAR(100) = NULL,
    @Type TINYINT = NULL,
    @Status BIT = NULL,
    @Page INT = 1,
    @PageSize INT = 20,
    @SortField NVARCHAR(50) = 'Id',
    @SortOrder NVARCHAR(4) = 'ASC'
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @SQL NVARCHAR(MAX);
    DECLARE @Params NVARCHAR(MAX);
    DECLARE @WhereClause NVARCHAR(MAX) = ' WHERE 1=1';
    
    -- 构建WHERE子句
    IF @Search IS NOT NULL AND @Search <> ''
    BEGIN
        SET @WhereClause = @WhereClause + ' AND (Name LIKE ''%'' + @Search + ''%'' OR Remark LIKE ''%'' + @Search + ''%'')';
    END
    
    IF @Type IS NOT NULL
    BEGIN
        SET @WhereClause = @WhereClause + ' AND Type = @Type';
    END
    
    IF @Status IS NOT NULL
    BEGIN
        SET @WhereClause = @WhereClause + ' AND IsActive = @Status';
    END
    
    -- 计算总记录数
    DECLARE @CountSQL NVARCHAR(MAX) = 'SELECT @TotalCount = COUNT(*) FROM Materials' + @WhereClause;
    DECLARE @TotalCount INT = 0;
    
    SET @Params = N'@Search NVARCHAR(100), @Type TINYINT, @Status BIT, @TotalCount INT OUTPUT';
    EXEC sp_executesql @CountSQL, @Params, @Search, @Type, @Status, @TotalCount OUTPUT;
    
    -- 构建排序和分页
    DECLARE @OrderByClause NVARCHAR(100) = ' ORDER BY ' + @SortField + ' ' + @SortOrder;
    DECLARE @OffsetClause NVARCHAR(100) = ' OFFSET ' + CAST((@Page - 1) * @PageSize AS NVARCHAR) + ' ROWS FETCH NEXT ' + CAST(@PageSize AS NVARCHAR) + ' ROWS ONLY';
    
    -- 构建最终查询
    SET @SQL = 'SELECT Id, Name, Type, Unit, Remark, IsActive, CreateTime, UpdateTime FROM Materials' + 
               @WhereClause + @OrderByClause + @OffsetClause;
    
    -- 执行查询
    EXEC sp_executesql @SQL, @Params, @Search, @Type, @Status;
    
    -- 返回总记录数
    SELECT @TotalCount AS TotalCount;
END;

-- 添加存储过程：获取物料领用统计
CREATE PROCEDURE sp_GetMaterialRequisitionStats
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        SUM(CASE WHEN Status = 1 THEN 1 ELSE 0 END) AS Pending,
        SUM(CASE WHEN Status = 2 THEN 1 ELSE 0 END) AS Approved,
        SUM(CASE WHEN Status = 3 THEN 1 ELSE 0 END) AS Rejected,
        SUM(CASE WHEN Status = 4 THEN 1 ELSE 0 END) AS Distributed,
        COUNT(*) AS Total
    FROM MaterialRequisitions;
END;
