using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace TaskManagement.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class MaterialRequisitionController : ControllerBase
    {
        private readonly ILogger<MaterialRequisitionController> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _connectionString;

        public MaterialRequisitionController(ILogger<MaterialRequisitionController> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
            _connectionString = _configuration.GetConnectionString("DefaultConnection");
        }

        // 创建领用单
        [HttpPost("Create")]
        public async Task<IActionResult> Create([FromBody] MaterialRequisitionCreateModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new { status = "failed", message = "请求数据无效" });
                }

                using (IDbConnection db = new SqlConnection(_connectionString))
                {
                    string sql = @"
                        INSERT INTO MaterialRequisitions (
                            ApplicantId, ApplicantName, RequisitionType, MaterialId, MaterialName,
                            Quantity, Unit, Purpose, OldMaterialCondition,
                            OldMaterialDescription, Remark, Attachments, Status, CreateTime
                        ) VALUES (
                            @ApplicantId, @ApplicantName, @RequisitionType, @MaterialId, @MaterialName,
                            @Quantity, @Unit, @Purpose, @OldMaterialCondition,
                            @OldMaterialDescription, @Remark, @Attachments, 1, GETDATE()
                        );
                        SELECT CAST(SCOPE_IDENTITY() as int)";

                    var parameters = new
                    {
                        ApplicantId = User.Identity.Name, // 从当前用户获取
                        ApplicantName = model.ApplicantName ?? User.Claims.FirstOrDefault(c => c.Type == "FullName")?.Value,
                        model.RequisitionType,
                        model.MaterialId,
                        model.MaterialName,
                        model.Quantity,
                        model.Unit,
                        model.Purpose,
                        model.OldMaterialCondition,
                        model.OldMaterialDescription,
                        model.Remark,
                        model.Attachments
                    };

                    int id = await db.QuerySingleAsync<int>(sql, parameters);

                    return Ok(new { status = "succeed", data = id });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建领用单失败");
                return StatusCode(500, new { status = "failed", message = "服务器内部错误" });
            }
        }

        // 获取我的领用单
        [HttpGet("GetMyRequisitions")]
        public async Task<IActionResult> GetMyRequisitions([FromQuery] MaterialRequisitionQueryModel query)
        {
            try
            {
                using (IDbConnection db = new SqlConnection(_connectionString))
                {
                    string whereClause = "WHERE ApplicantId = @ApplicantId";
                    var parameters = new DynamicParameters();
                    parameters.Add("@ApplicantId", User.Identity.Name);

                    if (query.Status.HasValue)
                    {
                        whereClause += " AND Status = @Status";
                        parameters.Add("@Status", query.Status.Value);
                    }

                    string sql = $@"
                        SELECT
                            Id, ApplicantId, ApplicantName, RequisitionType, MaterialId,
                            MaterialName, Quantity, Unit, Purpose,
                            OldMaterialCondition, OldMaterialDescription, Remark, Attachments,
                            Status, CreateTime, ApproveTime, ApproveRemark, ApproverId, ApproverName,
                            DistributionTime, DistributorId, DistributorName
                        FROM MaterialRequisitions
                        {whereClause}
                        ORDER BY CreateTime DESC";

                    var result = await db.QueryAsync<MaterialRequisitionViewModel>(sql, parameters);

                    return Ok(new { status = "succeed", data = result });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取我的领用单失败");
                return StatusCode(500, new { status = "failed", message = "服务器内部错误" });
            }
        }

        // 获取所有领用单（管理员用）
        [HttpGet("GetAllRequisitions")]
        [Authorize(Roles = "Admin,Manager")]
        public async Task<IActionResult> GetAllRequisitions([FromQuery] MaterialRequisitionQueryModel query)
        {
            try
            {
                using (IDbConnection db = new SqlConnection(_connectionString))
                {
                    string whereClause = "WHERE 1=1";
                    var parameters = new DynamicParameters();

                    if (query.Status.HasValue)
                    {
                        whereClause += " AND Status = @Status";
                        parameters.Add("@Status", query.Status.Value);
                    }

                    if (!string.IsNullOrEmpty(query.Search))
                    {
                        whereClause += " AND (ApplicantName LIKE @Search OR MaterialName LIKE @Search OR Purpose LIKE @Search)";
                        parameters.Add("@Search", $"%{query.Search}%");
                    }

                    string orderClause = "ORDER BY CreateTime DESC";
                    if (!string.IsNullOrEmpty(query.SortField))
                    {
                        orderClause = $"ORDER BY {query.SortField} {(query.SortOrder == "desc" ? "DESC" : "ASC")}";
                    }

                    string sql = $@"
                        SELECT
                            Id, ApplicantId, ApplicantName, RequisitionType, MaterialId,
                            MaterialName, Quantity, Unit, Purpose,
                            OldMaterialCondition, OldMaterialDescription, Remark, Attachments,
                            Status, CreateTime, ApproveTime, ApproveRemark, ApproverId, ApproverName,
                            DistributionTime, DistributorId, DistributorName
                        FROM MaterialRequisitions
                        {whereClause}
                        {orderClause}";

                    var result = await db.QueryAsync<MaterialRequisitionViewModel>(sql, parameters);

                    return Ok(new { status = "succeed", data = result });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有领用单失败");
                return StatusCode(500, new { status = "failed", message = "服务器内部错误" });
            }
        }

        // 审核领用单
        [HttpPost("Approve")]
        [Authorize(Roles = "Admin,Manager")]
        public async Task<IActionResult> Approve([FromBody] MaterialRequisitionApproveModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new { status = "failed", message = "请求数据无效" });
                }

                using (IDbConnection db = new SqlConnection(_connectionString))
                {
                    // 首先检查领用单是否存在且状态为待审核
                    string checkSql = "SELECT Status FROM MaterialRequisitions WHERE Id = @Id";
                    int? status = await db.QuerySingleOrDefaultAsync<int?>(checkSql, new { model.Id });

                    if (!status.HasValue)
                    {
                        return NotFound(new { status = "failed", message = "领用单不存在" });
                    }

                    if (status.Value != 1) // 1 = 待审核
                    {
                        return BadRequest(new { status = "failed", message = "只能审核待审核状态的领用单" });
                    }

                    // 更新领用单状态
                    string sql = @"
                        UPDATE MaterialRequisitions
                        SET Status = @Result,
                            ApproveTime = GETDATE(),
                            ApproveRemark = @Remark,
                            ApproverId = @ApproverId,
                            ApproverName = @ApproverName
                        WHERE Id = @Id";

                    var parameters = new
                    {
                        model.Id,
                        model.Result, // 2 = 已通过, 3 = 已拒绝
                        model.Remark,
                        ApproverId = User.Identity.Name,
                        ApproverName = User.Claims.FirstOrDefault(c => c.Type == "FullName")?.Value
                    };

                    await db.ExecuteAsync(sql, parameters);

                    // 审核通过后状态直接为已通过(2)，等待文员确认发放

                    return Ok(new { status = "succeed" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "审核领用单失败");
                return StatusCode(500, new { status = "failed", message = "服务器内部错误" });
            }
        }

        // 确认发放物品
        [HttpPost("ConfirmDistribution")]
        [Authorize(Roles = "Admin,Clerk")]
        public async Task<IActionResult> ConfirmDistribution([FromBody] MaterialRequisitionDistributionModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new { status = "failed", message = "请求数据无效" });
                }

                using (IDbConnection db = new SqlConnection(_connectionString))
                {
                    // 首先检查领用单是否存在且状态为待发放
                    string checkSql = "SELECT Status FROM MaterialRequisitions WHERE Id = @Id";
                    int? status = await db.QuerySingleOrDefaultAsync<int?>(checkSql, new { model.Id });

                    if (!status.HasValue)
                    {
                        return NotFound(new { status = "failed", message = "领用单不存在" });
                    }

                    if (status.Value != 2) // 2 = 已通过
                    {
                        return BadRequest(new { status = "failed", message = "只能确认已通过状态的领用单" });
                    }

                    // 更新领用单状态为已发放
                    string sql = @"
                        UPDATE MaterialRequisitions
                        SET Status = 4, -- 4 = 已发放
                            DistributionTime = GETDATE(),
                            DistributorId = @DistributorId,
                            DistributorName = @DistributorName
                        WHERE Id = @Id";

                    var parameters = new
                    {
                        model.Id,
                        DistributorId = User.Identity.Name,
                        DistributorName = User.Claims.FirstOrDefault(c => c.Type == "FullName")?.Value
                    };

                    await db.ExecuteAsync(sql, parameters);

                    return Ok(new { status = "succeed" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "确认发放物品失败");
                return StatusCode(500, new { status = "failed", message = "服务器内部错误" });
            }
        }

        // 删除领用单
        [HttpPost("Delete")]
        [Authorize(Roles = "Admin,Manager")]
        public async Task<IActionResult> Delete([FromBody] MaterialRequisitionDeleteModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new { status = "failed", message = "请求数据无效" });
                }

                using (IDbConnection db = new SqlConnection(_connectionString))
                {
                    string sql = "DELETE FROM MaterialRequisitions WHERE Id = @Id";
                    await db.ExecuteAsync(sql, new { model.Id });

                    return Ok(new { status = "succeed" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除领用单失败");
                return StatusCode(500, new { status = "failed", message = "服务器内部错误" });
            }
        }

        // 获取统计数据
        [HttpGet("GetStats")]
        [Authorize(Roles = "Admin,Manager")]
        public async Task<IActionResult> GetStats()
        {
            try
            {
                using (IDbConnection db = new SqlConnection(_connectionString))
                {
                    string sql = @"
                        SELECT
                            SUM(CASE WHEN Status = 1 THEN 1 ELSE 0 END) AS Pending,
                            SUM(CASE WHEN Status = 2 THEN 1 ELSE 0 END) AS Approved,
                            SUM(CASE WHEN Status = 3 THEN 1 ELSE 0 END) AS Rejected,
                            SUM(CASE WHEN Status = 4 THEN 1 ELSE 0 END) AS Distributed,
                            COUNT(*) AS Total
                        FROM MaterialRequisitions";

                    var stats = await db.QuerySingleAsync(sql);

                    return Ok(new { status = "succeed", data = stats });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取统计数据失败");
                return StatusCode(500, new { status = "failed", message = "服务器内部错误" });
            }
        }
    }
}
