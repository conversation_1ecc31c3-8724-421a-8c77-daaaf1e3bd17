:root {
    --border-color: rgb(0, 0, 0);
    --background-color: rgb(255, 255, 255);
    /* rgba(236, 245, 255, .9) */
}

/* body {
    background-color: var(--background-color) !important;
}
.el-aside * {
    background-color: var(--background-color) !important;
}
.el-aside {
    background-color: var(--background-color) !important;
} */

.el-table--border {
    border-color: var(--border-color);
}

.el-table td.el-table__cell {
    border-color: var(--border-color);
}

.el-table::before {
    background-color: var(--border-color);
}

.el-table--border::after {
    background-color: var(--border-color);
}

.el-table--border .el-table__cell {
    border-color: var(--border-color);
}

.el-table th.el-table__cell.is-leaf {
    border-color: var(--border-color);
}

.el-descriptions .is-bordered .el-descriptions-item__cell {
    border-color: var(--border-color);
}

.el-descriptions-item__label.is-bordered-label {
    color: black;
}

.el-table {
    color: black;
  }
/* .el-table__fixed::before {
    background-color: var(--border-color);
} */

/* .el-table__fixed-right::before {
    content: none;
}

.el-table__fixed-left::before {
    content: none;
} */

.el-table th.el-table__cell {
    background-color: #c0c4cc !important;
}

.el-table thead {
    color: #2a2a2b;
}
/* .el-table--scrollable-y */
 .el-table__body-wrapper {
    overflow-y: auto !important;
}

.el-checkbox__inner {
    border-color: #4a4b4d !important;
}

.el-table__body tr.current-row>td.el-table__cell {
    background-color: rgb(198, 226, 255) !important;
}


.el-calendar-table td.is-selected {
    color: #409EFF;
}
.el-calendar-table td.is-selected .el-calendar-day{
    background-color: #daecff;
}


.el-main {
    padding: 0;
    margin: 20px;
}

.el-message-box__wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
}