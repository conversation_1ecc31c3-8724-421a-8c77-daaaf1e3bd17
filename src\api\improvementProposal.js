/**
 * 精益改善提案相关API
 */
import service from "@/router/request";

// API接口路径
const API_BASE = "/ImprovementProposal";

/**
 * 获取提案列表
 * @param {Object} params 查询参数
 * @param {number} params.page 页码
 * @param {number} params.pageSize 每页数量
 * @param {string} params.status 状态
 * @param {string} params.title 标题
 * @param {string} params.proposer 提案人
 * @param {string} params.startDate 开始日期
 * @param {string} params.endDate 结束日期
 * @param {string} params.loginId 登录ID
 * @returns {Promise} 返回提案列表数据
 */
export function getProposalList(params = {}) {
  const {
    page = 1,
    pageSize = 10,
    status = "",
    title = "",
    proposer = "",
    startDate = "",
    endDate = "",
    loginId = "",
  } = params;

  return service({
    url: `${API_BASE}/GetProposalList`,
    method: "get",
    params: {
      page,
      pageSize,
      status,
      title,
      proposer,
      startDate,
      endDate,
      loginId,
    },
  });
}

/**
 * 获取提案详情
 * @param {number} id 提案ID
 * @returns {Promise} 返回提案详情数据
 */
export function getProposalDetail(id) {
  return service({
    url: `${API_BASE}/GetProposalDetail/${id}`,
    method: "get",
  });
}

/**
 * 处理提案
 * @param {Object} data 处理数据
 * @param {number} data.id 提案ID
 * @param {string} data.review 审核意见
 * @param {number} data.status 处理后状态
 * @param {string} data.processor 处理人
 * @param {string} data.implementationDepartment 实施部门
 * @param {string} data.finishDate 完成日期
 * @returns {Promise} 返回处理结果
 */
export function processProposal(data) {
  return service({
    url: `${API_BASE}/ProcessProposal`,
    method: "post",
    data: {
      id: data.id,
      review: data.review,
      status: data.status,
      processor: data.processor,
      implementationDepartment: data.implementationDepartment,
      finishDate: data.finishDate,
      processedAt: new Date().toISOString(),
    },
  });
}

/**
 * 批量处理提案
 * @param {Array} proposals 提案列表
 * @param {Object} processData 处理数据
 * @returns {Promise} 返回批量处理结果
 */
export function batchProcessProposals(proposals, processData) {
  return service({
    url: `${API_BASE}/BatchProcessProposals`,
    method: "post",
    data: {
      proposalIds: proposals.map((p) => p.id),
      ...processData,
      processedAt: new Date().toISOString(),
    },
  });
}

/**
 * 导出提案数据
 * @param {Object} params 导出参数
 * @returns {Promise} 返回导出结果
 */
export function exportProposals(params = {}) {
  return service({
    url: `${API_BASE}/ExportProposals`,
    method: "get",
    params,
    responseType: "blob", // 用于文件下载
  });
}

/**
 * 获取提案统计数据
 * @param {Object} params 统计参数
 * @returns {Promise} 返回统计数据
 */
export function getProposalStatistics(params = {}) {
  return service({
    url: `${API_BASE}/GetStatistics`,
    method: "get",
    params,
  });
}

/**
 * 获取提案状态选项
 * @returns {Array} 状态选项列表
 */
export function getStatusOptions() {
  return [
    { label: "全部", value: "" },
    { label: "待处理", value: "1" },
    { label: "已通过", value: "2" },
    { label: "已拒绝", value: "3" },
  ];
}

/**
 * 获取紧急程度选项
 * @returns {Array} 紧急程度选项列表
 */
export function getUrgencyOptions() {
  return [
    { label: "正常", value: 1, color: "#67C23A" },
    { label: "重要", value: 2, color: "#E6A23C" },
    { label: "紧急", value: 3, color: "#F56C6C" },
  ];
}

/**
 * 获取难度等级选项
 * @returns {Array} 难度等级选项列表
 */
export function getDifficultyOptions() {
  return [
    { label: "简单", value: 1, color: "#67C23A" },
    { label: "中等", value: 2, color: "#E6A23C" },
    { label: "困难", value: 3, color: "#F56C6C" },
  ];
}

/**
 * 格式化提案数据用于显示
 * @param {Object} proposal 提案数据
 * @returns {Object} 格式化后的提案数据
 */
export function formatProposalForDisplay(proposal) {
  const urgencyOptions = getUrgencyOptions();
  const difficultyOptions = getDifficultyOptions();
  const statusOptions = getStatusOptions();

  const urgency = urgencyOptions.find((u) => u.value === proposal.urgency);
  const difficulty = difficultyOptions.find((d) => d.value === proposal.difficulty);
  const status = statusOptions.find((s) => s.value === proposal.status.toString());

  return {
    ...proposal,
    urgencyText: urgency?.label || "未知",
    urgencyColor: urgency?.color || "#909399",
    difficultyText: difficulty?.label || "未知",
    difficultyColor: difficulty?.color || "#909399",
    statusText: status?.label || "未知",
    createdAtFormatted: proposal.createdAt ? new Date(proposal.createdAt).toLocaleString() : "",
  };
}

// 导出所有API方法
export default {
  getProposalList,
  getProposalDetail,
  processProposal,
  batchProcessProposals,
  exportProposals,
  getProposalStatistics,
  getStatusOptions,
  getUrgencyOptions,
  getDifficultyOptions,
  formatProposalForDisplay,
};
