<template>
  <div class="home">
    <div class="search-bar">
      <div class="search-item">
        <span class="label">任务类型：</span>
        <el-select v-model="taskType" @change="taskTypeChange" filterable placeholder="请选择">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </div>
      <div class="search-item" style="width: 500px">
        <span class="label">搜索内容：</span>
        <el-select v-model="fieldName" @change="serach" class="mr-1" style="width: 300px">
          <el-option v-for="opt of taskType == 'TAC'
            ? pieceFieldOptions
            : taskType == 'PFT'
              ? performanceFieldOptions
              : timeFieldOptions" :key="opt.value" v-bind="opt"></el-option>
        </el-select>
        <el-input v-model="fieldValue" filterable placeholder="请输入" clearable></el-input>
      </div>
      <div class="search-item" style="width: 500px">
        <span class="label">时间类型：</span>
        <el-select v-model="timeType" @change="serach" class="mr-1">
          <el-option v-for="item of taskType == 'TAC'
            ? pieceDateTypeOptions
            : taskType == 'PFT'
              ? performanceDateTypeOptions
              : timeDateTypeOptions" :key="item.value" :value="item.value" :label="item.label"></el-option>
        </el-select>
        <span class="label">日期：</span>
        <el-date-picker v-model="date" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="yyyy-MM-dd" :clearable="false"></el-date-picker>
      </div>
      <el-button class="search-btn" type="primary" @click="serach">搜索</el-button>
    </div>
    <!-- Table -->
    <div>
      <el-table ref="tableRef" :data="tableData" border max-height="700">
        <template v-if="taskType == 'TAC'">
          <el-table-column prop="planno" label="计划单号" min-width="160" key="planno"></el-table-column>
          <el-table-column prop="prono" label="工程编码" min-width="120" key="prono"></el-table-column>
          <el-table-column prop="mmomodel" label="机型型号" min-width="70" key="mmomodel"></el-table-column>
          <el-table-column prop="proname" label="机型名称" min-width="135" key="proname"></el-table-column>
          <el-table-column prop="groname" label="分配巴组" min-width="80" key="groname"></el-table-column>
          <el-table-column prop="grohmname" label="管理巴长" min-width="80" key="grohmname"></el-table-column>
          <el-table-column prop="assignor" label="任务创建" min-width="65" key="assignor"></el-table-column>
          <el-table-column prop="assigntime" label="创建日期" min-width="160" key="assigntime"></el-table-column>
          <el-table-column prop="adstatusn" label="任务状态" min-width="70" key="adstatusn"></el-table-column>
          <el-table-column prop="tasno" label="任务工单编号" min-width="120" key="tasno"></el-table-column>
          <el-table-column prop="tagno" label="任务组号" min-width="120" key="tagno"></el-table-column>
          <el-table-column prop="apno" label="装配工单" min-width="130" key="apno"></el-table-column>
          <el-table-column prop="apdmdate" label="装配交期" min-width="95" key="apdmdate"></el-table-column>
          <el-table-column prop="apremarks" label="装配备注" min-width="150" key="apremarks"
            show-overflow-tooltip></el-table-column>
        </template>
        <template v-else-if="taskType == 'PFT'">
          <el-table-column prop="loginid" label="工号" key="loginid"></el-table-column>
          <el-table-column prop="lastname" label="姓名" key="lastname"></el-table-column>
          <el-table-column prop="departmentname" label="部门" key="departmentname"></el-table-column>
          <el-table-column prop="basic_performance" label="绩效金额" key="basic_performance"></el-table-column>
          <el-table-column prop="acc_month" label="结算月份" key="acc_month"></el-table-column>
          <el-table-column prop="work_days" label="工作天数" key="work_days"></el-table-column>
          <el-table-column prop="assessment_days" label="考核天数" key="assessment_days"></el-table-column>
          <el-table-column prop="assessment_grade" label="考核分数" key="assessment_grade"></el-table-column>
          <el-table-column prop="performance_wage" label="应得金额" key="performance_wage"></el-table-column>
          <el-table-column prop="created_time" label="创建时间" key="created_time" min-width="160"></el-table-column>
          <el-table-column prop="remarks" label="备注" key="remarks" min-width="160"></el-table-column>
        </template>
        <template v-else>
          <el-table-column prop="ttmno" label="任务工单编号" width="120"></el-table-column>
          <el-table-column prop="ttmcontent" label="任务内容" min-width="160"></el-table-column>
          <el-table-column prop="ttmcreater" label="任务创建者" width="65"></el-table-column>
          <!-- <el-table-column prop="ttmflag" label="任务状态" width="50">
            <template v-slot="scope">{{
              scope.row.ttmflag == 0 ? "正常" : "作废"
            }}</template>
          </el-table-column> -->
          <el-table-column prop="ttmremarks" label="任务备注" min-width="160"></el-table-column>
          <el-table-column prop="ttmsdate" label="任务开始" width="155"></el-table-column>
          <el-table-column prop="ttmedate" label="任务结束" width="155"></el-table-column>
          <el-table-column prop="ttmcreated" label="创建日期" width="155"></el-table-column>
        </template>
        <el-table-column label="操作" fixed="right" width="150">
          <div slot="default" slot-scope="scope" style="display: flex; justify-content: space-around">
            <el-link v-if="taskType != 'PFT'" @click="toDetailsPage(scope.row)" type="primary">查看详情</el-link>
            <el-link v-else @click="performanceForm = scope.row; performanceTaskVisible = true;"
              type="primary">编辑</el-link>
            <el-tooltip content="将会删除该任务工单以及工单下的所有任务" :disabled="taskType == 'PFT'">
              <el-link v-if="taskType != 'TAC'" @click="deleteTimeTask(scope.row)" type="danger">删除</el-link>
            </el-tooltip>
          </div>
        </el-table-column>
      </el-table>
      <el-pagination style="width: 75%" @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="currentPage" :page-sizes="[5, 10, 20, 40]" :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper" :total="totalData"></el-pagination>
    </div>

    <el-dialog :visible.sync="performanceTaskVisible" title="编辑">
      <el-form ref="performanceForm" :model="performanceForm" :inline="true">
        <el-row class="mb-2">
          <el-col :span="6">
            工号：{{ performanceForm.loginid }}
          </el-col>
          <el-col :span="6">
            姓名：{{ performanceForm.lastname }}
          </el-col>
          <el-col :span="6">
            绩效金额：{{ performanceForm.basic_performance }}
          </el-col>
          <el-col :span="6">
            结算月份：{{ performanceForm.acc_month }}
          </el-col>
        </el-row>
        <el-form-item label="工作天数">
          <el-input v-model="performanceForm.work_days" type="number" @input="computeWage()"></el-input>
        </el-form-item>
        <el-form-item label="考核天数">
          <el-input v-model="performanceForm.assessment_days" type="number" @input="computeWage()"></el-input>
        </el-form-item>
        <el-form-item label="考核分数">
          <el-input v-model="performanceForm.assessment_grade" type="number" @input="computeWage()"></el-input>
        </el-form-item>
        <el-form-item label="应得金额">
          {{ performanceForm.performance_wage }}
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="performanceTaskVisible = false;">取 消</el-button>
        <el-button type="primary" @click="updatePerformanceTask">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import service from "@/router/request";

export default {
  name: "plansearch",
  data() {
    const currentDate = this.$moment().format("yyyy-MM-DD");
    const startDate = this.$moment()
      .subtract(12, "months")
      .format("yyyy-MM-DD");
    return {
      timeType: "ttmcreated",
      date: [startDate, currentDate],
      options: [
        {
          value: "TTM",
          label: "计时任务",
        },
        {
          value: "TPT",
          label: "临时任务",
        },
        {
          value: "TAC",
          label: "计件任务",
        },
        {
          value: "PFT",
          label: "绩效任务",
        },
      ],
      timeDateTypeOptions: [
        { value: "ttmcreated", label: "创建日期" },
        { value: "ttmsdate", label: "开始日期" },
        { value: "ttmedate", label: "结束日期" },
      ],
      pieceDateTypeOptions: [{ value: "assigntime", label: "创建日期" }],
      performanceDateTypeOptions: [
        { value: "created_time", label: "创建日期" },
      ],
      taskType: "TTM", //
      tableData: [],
      totalData: 0,
      currentPage: 1, // 初始页
      pageSize: 10, // 每页的数据
      fieldName: "ttmcontent",
      fieldValue: "",
      timeFieldOptions: [
        { label: "任务内容", value: "ttmcontent" },
        { label: "任务创建者", value: "lastname" },
        { label: "任务编号", value: "ttmno" },
      ],
      pieceFieldOptions: [
        { label: "计划单号", value: "planno" },
        { label: "机型编码", value: "prono" },
        { label: "机型", value: "mmomodel" },
        { label: "任务编号", value: "tasno" },
      ],
      performanceFieldOptions: [
        { label: "任务成员工号", value: "loginid" },
        { label: "任务成员名", value: "lastname" },
        { label: "部门", value: "departmentname" },
      ],
      performanceTaskVisible: false,
      performanceForm: {}
    };
  },

  mounted() {
    this.taskTypeChange(this.taskType);
    // this.selectplan();
  },

  methods: {
    handleSizeChange(size) {
      this.pageSize = size;
      this.selectplan();
    },

    handleCurrentChange(page) {
      this.currentPage = page;
      this.selectplan();
    },

    serach() {
      this.currentPage = 1;
      this.selectplan();
    },

    taskTypeChange(value) {
      this.fieldName =
        value == "TAC"
          ? this.pieceFieldOptions[0].value
          : value == "PFT"
            ? this.performanceFieldOptions[0].value
            : this.timeFieldOptions[0].value;
      this.timeType =
        value == "TAC"
          ? this.pieceDateTypeOptions[0].value
          : value == "PFT"
            ? this.performanceDateTypeOptions[0].value
            : this.timeDateTypeOptions[0].value;
      this.serach();
    },

    selectplan() {
      service
        .post(
          this.taskType == "TAC"
            ? "/TaskCheck/getAssigngro"
            : this.taskType == "PFT"
              ? "/Performance/getPerformanceTasks"
              : "/TaskCheck/getTimeTask",
          {
            taskType: this.taskType,
            startDate: this.date[0],
            endDate: this.date[1],
            dateFieldName: this.timeType,
            pageNum: this.currentPage,
            pageSize: this.pageSize,
            fieldName: this.fieldName,
            fieldValue: this.fieldValue,
          }
        )
        .then((res) => {
          if (res.status == "succeed") {
            this.tableData = res.data?.list;
            this.totalData = res.data?.total;
          }
        });
    },

    toDetailsPage(row) {
      let routerNmae = "";
      switch (this.taskType) {
        case "TAC":
          routerNmae = "计件派单详情";
          break;
        case "TTM":
          routerNmae = "计时任务";
          break;
        case "TPT":
          routerNmae = "临时任务";
          break;
      }
      this.$router.push({
        name: routerNmae,
        query: {
          rowData: encodeURIComponent(JSON.stringify(row)),
          isCheck: true,
        },
      });
    },

    deleteTimeTask(row) {
      this.$confirm(this.taskType == 'PFT' ? '确定要删除该任务吗?' : "将会删除该工单下的所有任务，确定要删除该任务工单吗?", "提示", { type: "warning" })
        .then(() => {
          let url =
            this.taskType == "PFT"
              ? "/Performance/deletePerformanceTask"
              : "/JiShi/deleteJiShiMain";
          let param =
            this.taskType == "PFT" ? row : { ttmno: row.ttmno };
          let delReq = () => service.post(url, param).then((res) => {
            if (res.status == "succeed") {
              this.selectplan();
              this.$message.success("删除成功！");
            } else {
              this.$message.error("删除失败：", res.err);
            }
          });
          this.taskType != "PFT" ? service.get('/TaskHours/getTaskHoursListForTTM', { params: { ttmno: row.ttmno } }).then(res => {
            if (res.status == 'succeed') {
              if (res.data?.length) {
                this.$confirm(`工单下任务已产生日报，删除产生日报的任务将会删除其所关联的日报，确定要删除吗？`, "提示", {
                  confirmButtonText: "确认",
                  cancelButtonText: "取消",
                  type: "warning"
                }).then(() => delReq()).catch(() => { })
              } else {
                delReq()
              }
            }
          }) : delReq()
        })
        .catch(() => { });
    },

    computeWage() {
      this.performanceForm.performance_wage =
        this.performanceForm.basic_performance
          ? (
            (this.performanceForm.basic_performance / this.performanceForm.work_days) *
            this.performanceForm.assessment_days *
            this.performanceForm.assessment_grade *
            0.01
          ).toFixed(2)
          : 0;
    },

    updatePerformanceTask() {
      service.post("/Performance/updatePerformanceTask", this.performanceForm).then(res => {
        if (res.status == "succeed") {
          this.performanceTaskVisible = false
          this.$message.success("编辑成功！")
        } else {
          this.$message.error("编辑失败：", res.err)
        }
      })
    }
  },
};
</script>
<style></style>
