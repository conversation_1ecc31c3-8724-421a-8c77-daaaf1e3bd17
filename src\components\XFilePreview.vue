<template>
  <div class="file-preview-container">
    <!-- 文件预览区域 -->
    <div class="preview-content" v-loading.lock="loading">
      <!-- 图片预览 -->
      <div v-if="isImage" class="image-preview">
        <el-image :src="fileUrl" :preview-src-list="[fileUrl]" fit="contain" @error="handleImageError">
          <div slot="error" class="image-error">
            <i class="el-icon-picture-outline"></i>
            <p>图片加载失败</p>
          </div>
        </el-image>
      </div>

      <!-- PDF预览 -->
      <div v-else-if="isPDF" class="pdf-preview">
        <iframe :src="fileUrl" frameborder="0"></iframe>
        <div v-if="!pdfSupported" class="pdf-not-supported">
          <el-alert title="您的浏览器不支持直接预览PDF文件" type="warning" :closable="false"></el-alert>
          <el-button type="primary" @click="downloadFile">下载文件</el-button>
        </div>
      </div>

      <!-- 视频预览 -->
      <div v-else-if="isVideo" class="video-preview">
        <video controls width="100%">
          <source :src="fileUrl" :type="videoType" />
          <div class="video-error">
            <el-alert title="您的浏览器不支持该视频格式" type="warning" :closable="false"></el-alert>
            <el-button type="primary" @click="downloadFile">下载文件</el-button>
          </div>
        </video>
      </div>

      <!-- 音频预览 -->
      <div v-else-if="isAudio" class="audio-preview">
        <audio controls width="100%">
          <source :src="fileUrl" :type="audioType" />
          <div class="audio-error">
            <el-alert title="您的浏览器不支持该音频格式" type="warning" :closable="false"></el-alert>
            <el-button type="primary" @click="downloadFile">下载文件</el-button>
          </div>
        </audio>
      </div>

      <!-- 文本预览 -->
      <div v-else-if="isText" class="text-preview">
        <pre class="text-content">{{ textContent }}</pre>
      </div>

      <!-- 未知类型文件 -->
      <div v-else class="unknown-preview">
        <el-alert :title="'暂不支持预览' + fileExt + '类型的文件'" type="info" :closable="false"></el-alert>
        <el-button type="primary" @click="downloadFile">下载文件</el-button>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="preview-actions">
      <el-button @click="downloadFile" icon="el-icon-download">下载文件</el-button>
      <el-button @click="closePreview" icon="el-icon-close">关闭</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: "FilePreview",
  props: {
    // 文件ID或路径
    fileId: {
      type: [String, Number],
      required: false,
    },
    // 文件URL
    fileUrl: {
      type: String,
      required: false,
    },
    // 文件名
    fileName: {
      type: String,
      default: "",
    },
    // 是否显示关闭按钮
    showClose: {
      type: Boolean,
      default: true,
    },
    // 自定义请求头
    headers: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      loading: true,
      // fileExt: "",
      textContent: "",
      pdfSupported: true,
      loadingTarget: null,
    };
  },
  computed: {
    fileExt() {
      const lastDotIndex = this.fileName.lastIndexOf(".");
      return lastDotIndex > 0 ? this.fileName.substring(lastDotIndex + 1) : "";
    },
    // 判断文件类型
    isImage() {
      return ["jpg", "jpeg", "png", "gif", "bmp", "webp"].includes(this.fileExt.toLowerCase());
    },
    isPDF() {
      return this.fileExt.toLowerCase() === "pdf";
    },
    isVideo() {
      return ["mp4", "webm", "ogg"].includes(this.fileExt.toLowerCase());
    },
    isAudio() {
      return ["mp3", "wav", "ogg"].includes(this.fileExt.toLowerCase());
    },
    isText() {
      return ["txt", "json", "xml", "html", "js", "css", "md"].includes(this.fileExt.toLowerCase());
    },
    // 获取视频类型
    videoType() {
      if (this.fileExt.toLowerCase() === "mp4") return "video/mp4";
      if (this.fileExt.toLowerCase() === "webm") return "video/webm";
      if (this.fileExt.toLowerCase() === "ogg") return "video/ogg";
      return "";
    },
    // 获取音频类型
    audioType() {
      if (this.fileExt.toLowerCase() === "mp3") return "audio/mpeg";
      if (this.fileExt.toLowerCase() === "wav") return "audio/wav";
      if (this.fileExt.toLowerCase() === "ogg") return "audio/ogg";
      return "";
    },
  },
  mounted() {
    this.loadingTarget = this.$el.querySelector(".preview-content");

    // 如果是文本文件，需要先获取文本内容
    if (this.isText) {
      this.loadTextContent();
    } else {
      this.loading = false;
    }
  },
  methods: {
    // 加载文本内容
    loadTextContent() {
      if (!this.fileUrl && !this.fileId) {
        this.textContent = "没有提供文件URL或文件ID";
        this.loading = false;
        return;
      }

      const url = this.fileUrl || `/api/files/${this.fileId}/content`;

      fetch(url, {
        headers: this.headers,
      })
        .then((response) => {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          return response.text();
        })
        .then((text) => {
          this.textContent = text;
          this.loading = false;
        })
        .catch((error) => {
          console.error("获取文本内容失败:", error);
          this.textContent = "加载文本内容失败: " + error.message;
          this.loading = false;
        });
    },

    // 处理图片加载错误
    handleImageError() {
      console.error("图片加载失败:", this.fileUrl);
    },

    // 下载文件
    // downloadFile() {
    //   if (!this.fileUrl && !this.fileId) {
    //     this.$message.error("没有提供文件URL或文件ID");
    //     return;
    //   }

    //   const url = this.fileUrl || `/api/files/${this.fileId}/download`;
    //   const a = document.createElement("a");
    //   a.href = url;
    //   a.download = this.fileName || "file";
    //   document.body.appendChild(a);
    //   a.click();
    //   document.body.removeChild(a);
    // },

    async downloadFile() {
      if (!this.fileUrl && !this.fileId) {
        this.$message.error("没有提供文件URL或文件ID");
        return;
      }

      const response = await fetch(this.fileUrl, { headers: { origin: window.location.origin } });
      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);

      if (window.navigator?.msSaveBlob) {
        // For IE and Edge
        window.navigator.msSaveBlob(blob, this.fileName || "file");
        return;
      }

      const link = document.createElement("a");
      link.href = blobUrl;
      link.download = this.fileName || "file";
      link.click();
      URL.revokeObjectURL(blobUrl);
    },

    // 关闭预览
    closePreview() {
      this.$emit("close");
    },
  },
};
</script>

<style scoped>
.file-preview-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.preview-content {
  flex: 1;
  min-height: 300px;
  overflow: auto;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 15px;
  position: relative;
}

.loading-mask {
  position: relative;
  height: 100%;
}

.image-preview,
.pdf-preview,
.video-preview,
.audio-preview,
.text-preview,
.unknown-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.image-preview img,
.el-image {
  max-width: 100%;
  max-height: 100%;
}

.image-error,
.pdf-not-supported,
.video-error,
.audio-error,
.unknown-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  text-align: center;
}

.pdf-preview iframe {
  width: 100%;
  height: 100%;
  min-height: 500px;
}

.text-content {
  width: 100%;
  max-height: 100%;
  overflow: auto;
  padding: 15px;
  background-color: #fff;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  font-family: Consolas, Monaco, monospace;
  font-size: 14px;
}

.preview-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
}

:deep(.el-loading-spinner) {
  display: flex;
  justify-content: center;
}
</style>
