<template>
  <div class="daily-work-container">
    <!-- 顶部日历 -->
    <div ref="calendarRef" class="calendar-section">
      <div id="date" class="date-display">{{ $moment(date).format("yyyy-MM-DD") }}</div>
      <el-calendar v-model="date" class="mobile-calendar">
        <template v-slot:dateCell="{ date, data }">
          <div class="calendar-cell">
            <span class="date-number">{{ data.day.split("-")[2].replace(/^0+/, "") }}</span>
            <i
              v-if="hoursExceptionList.some((item) => item.date == data.day)"
              class="el-icon-warning status-icon exception"
            ></i>
            <i v-else-if="recordDates.includes(data.day)" class="el-icon-circle-check status-icon completed"></i>
          </div>
        </template>
      </el-calendar>
      <div class="calendar-toggle" @click="toggle">
        <i id="spread-icon" class="el-icon-arrow-down"></i>
      </div>
    </div>

    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">工作日报</h2>
      <div class="date-info">{{ $moment(date).format("MM月DD日 dddd") }}</div>
    </div>
    <!-- 主要内容区域 -->
    <div class="content-section">
      <!-- 用户信息卡片 -->
      <div class="user-info-card">
        <div class="user-info">
          <div class="info-item">
            <span class="label">姓名</span>
            <span class="value">{{ $store.state.user.name }}</span>
          </div>
          <div class="info-item">
            <span class="label">工号</span>
            <span class="value">{{ $store.state.user.loginId }}</span>
          </div>
        </div>
        <div v-if="exceptionData" class="audit-section">
          <el-button @click="commitAudit" size="small" type="primary" class="audit-btn"> 提交审核 </el-button>
        </div>
      </div>
      <!-- 计时任务 -->
      <div class="task-card">
        <div class="task-header">
          <h4 class="task-title">计时任务</h4>
          <button @click="openTimeDialog(null, 'TTM')" class="add-task-btn">
            <i class="el-icon-plus"></i>
          </button>
        </div>
        <div v-for="(task, idx) of timeTasks" :key="task.ttdno + idx" class="desc-container">
          <span class="sequence-number">{{ idx + 1 }}</span>
          <div style="display: flex; position: relative; padding: 0 30px 0 10px">
            <div style="position: relative">
              <el-descriptions :column="1">
                <el-descriptions-item
                  v-for="(config, idx) of timeTaskDescriptionConfig"
                  :key="config.prop + idx"
                  :label="config.label"
                  >{{ task[config.prop] }}</el-descriptions-item
                >
                <el-descriptions-item
                  v-if="task.showException"
                  label="工时异常"
                  labelStyle="color: red;"
                  contentStyle="color: red;"
                  >{{ exceptionData?.reason }}</el-descriptions-item
                >
              </el-descriptions>
              <img v-if="task.taskHours" src="../../assets/record.png" class="record-img" alt="" />
              <div class="time-btn">
                <el-button @click="openTimeDialog(task)" type="text">录工时</el-button>
                <el-button @click="deleteTask(task)" type="text" style="color: red">删除</el-button>
              </div>
            </div>
            <!-- 工时历史 -->
            <div class="time-history-container" :style="{ width: task.timeHistoryWidth }">
              <div class="spread-icon">
                <i
                  @click="timeHistorySpread(task)"
                  class="el-icon-d-arrow-left"
                  :style="{
                    transform: `rotate(${task.timeHistoryWidth == '30px' ? '0deg' : '180deg'})`,
                  }"
                ></i>
              </div>
              <div
                class="history-box"
                :style="{
                  width: task.timeHistoryWidth == '30px' ? '0' : 'auto',
                }"
              >
                <el-timeline v-if="task.timeHistory?.length" :reverse="true" class="time-line">
                  <el-timeline-item v-for="(item, index) in task.timeHistory" :key="index" :timestamp="item.recordDate">
                    录入{{ item.hours }}个工时
                  </el-timeline-item>
                </el-timeline>
                <span v-else style="white-space: nowrap">暂无数据</span>
              </div>
            </div>
          </div>
        </div>
        <el-empty v-if="!timeTasks.length" description="暂无任务"></el-empty>
      </div>
      <!-- 临时 v-if="temporaryTasks.length"-->
      <el-card class="mb-1" shadow="never">
        <div slot="header" class="card-header">
          <h4 style="margin: 0">临时任务</h4>
          <!-- <i @click="openTimeDialog(null, 'TPT')" class="el-icon-circle-plus-outline" style="color: #409eff"></i> -->
        </div>
        <div v-for="(task, idx) of temporaryTasks" :key="task.ttdno + idx" class="desc-container">
          <span class="sequence-number">{{ idx + 1 }}</span>
          <div style="display: flex; position: relative; padding-right: 30px">
            <div style="position: relative">
              <el-descriptions :column="1">
                <el-descriptions-item
                  v-for="(config, idx) of temporaryTaskDescriptionConfig"
                  :key="config.prop + idx"
                  :label="config.label"
                  >{{ task[config.prop] }}</el-descriptions-item
                >
                <el-descriptions-item
                  v-if="task.showException"
                  label="工时异常"
                  labelStyle="color: red;"
                  contentStyle="color: red;"
                  >{{ exceptionData.reason }}</el-descriptions-item
                >
              </el-descriptions>
              <img v-if="task.taskHours" src="../../assets/record.png" class="record-img" alt="" />
              <div class="time-btn">
                <el-button @click="openTimeDialog(task)" type="text">录工时</el-button>
                <el-button @click="deleteTask(task)" type="text" style="color: red">删除</el-button>
              </div>
            </div>
            <!-- 工时历史 -->
            <div class="time-history-container" :style="{ width: task.timeHistoryWidth }">
              <div class="spread-icon">
                <i
                  @click="timeHistorySpread(task)"
                  class="el-icon-d-arrow-left"
                  :style="{
                    transform: `rotate(${task.timeHistoryWidth == '30px' ? '0deg' : '180deg'})`,
                  }"
                ></i>
              </div>
              <div
                class="history-box"
                :style="{
                  width: task.timeHistoryWidth == '30px' ? '0' : 'auto',
                }"
              >
                <el-timeline v-if="task.timeHistory?.length" :reverse="true" class="time-line">
                  <el-timeline-item v-for="(item, index) in task.timeHistory" :key="index" :timestamp="item.recordDate">
                    录入{{ item.hours }}个工时
                  </el-timeline-item>
                </el-timeline>
                <span v-else style="white-space: nowrap">暂无数据</span>
              </div>
            </div>
          </div>
        </div>
        <el-empty v-if="!temporaryTasks.length" description="暂无任务"></el-empty>
      </el-card>
      <!-- 计件  v-if="pieceTasks.length"-->
      <el-card shadow="never">
        <h4 slot="header" style="margin: 0">计件任务</h4>
        <div v-for="(task, idx) of pieceTasks" :key="task.tadno + idx" class="desc-container">
          <span class="sequence-number">{{ idx + 1 }}</span>
          <div style="display: flex; position: relative; padding-right: 30px">
            <div style="position: relative">
              <el-descriptions :column="1">
                <el-descriptions-item
                  v-for="(config, idx) of pieceTaskDescriptionConfig"
                  :key="config.prop + idx"
                  :label="config.label"
                  >{{ task[config.prop] }}</el-descriptions-item
                >
                <el-descriptions-item
                  v-if="task.showException"
                  label="工时异常"
                  labelStyle="color: red;"
                  contentStyle="color: red;"
                  >{{ exceptionData?.reason }}</el-descriptions-item
                >
              </el-descriptions>
              <img v-if="task.taskHours" src="../../assets/record.png" class="record-img" alt="" />
              <div class="time-btn">
                <el-button @click="openTimeDialog(task)" type="text">录工时</el-button>
                <el-button @click="deleteTask(task)" type="text" style="color: red">删除</el-button>
              </div>
            </div>
            <!-- 工时历史 -->
            <div class="time-history-container" :style="{ width: task.timeHistoryWidth }">
              <div class="spread-icon">
                <i
                  @click="timeHistorySpread(task)"
                  class="el-icon-d-arrow-left"
                  :style="{
                    transform: `rotate(${task.timeHistoryWidth == '30px' ? '0deg' : '180deg'})`,
                  }"
                ></i>
              </div>
              <div
                class="history-box"
                :style="{
                  width: task.timeHistoryWidth == '30px' ? '0' : 'auto',
                }"
              >
                <el-timeline v-if="task.timeHistory?.length" :reverse="true" class="time-line">
                  <el-timeline-item v-for="(item, index) in task.timeHistory" :key="index" :timestamp="item.recordDate">
                    录入{{ item.hours }}个工时
                  </el-timeline-item>
                </el-timeline>
                <span v-else style="white-space: nowrap">暂无数据</span>
              </div>
            </div>
          </div>
        </div>
        <el-empty v-if="!pieceTasks.length" description="暂无任务"></el-empty>
      </el-card>
    </div>

    <!-- 搜索 -->
    <div class="search-box">
      <el-input
        ref="searchInputRef"
        v-if="isSearch"
        v-model="prono"
        @blur="searchInputBlur"
        placeholder="请输入工程号筛选任务"
        style="width: 200px"
        clearable
      >
        <el-button slot="append" @click="search" icon="el-icon-search"></el-button>
      </el-input>
      <img v-else src="../../../src/assets/icons/search.png" alt="搜索" style="width: 40px" @click="clickSearch" />
    </div>

    <el-dialog
      :visible.sync="timeDialogVisible"
      title="录入工时"
      :close-on-click-modal="false"
      @close="timeDialogClose"
      width="80%"
    >
      <el-form ref="timeFormRef" :model="timeForm" :rules="timeFormRules">
        <el-form-item label="工程编码" prop="ttdprono">
          <el-select
            v-model="timeForm.ttdprono"
            filterable
            remote
            reserve-keyword
            multiple
            placeholder="请输入工程编码搜索选择"
            :remote-method="getProNos"
            :loading="loading"
            :disabled="!!timeForm.taskNo"
          >
            <el-option v-for="(item, index) of proNos" :key="item + index" :label="item" :value="item"></el-option>
          </el-select>
        </el-form-item>
        <template v-if="!timeForm.taskNo || timeForm.taskNo.includes('TTD')">
          <el-form-item label="任务内容" prop="ttmcontent">
            <el-select v-model="timeForm.ttmcontent" placeholder="请选择任务内容" :disabled="!!timeForm.taskNo">
              <el-option v-for="opt of contentOptions" :key="opt" :label="opt" :value="opt"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="任务备注" prop="ttdremarks">
            <el-input
              v-model="timeForm.ttdremarks"
              placeholder="请输入任务备注"
              type="textarea"
              autosize
              :disabled="!!timeForm.taskNo"
            ></el-input>
          </el-form-item>
          <el-form-item label="结算类型" prop="ttdsmtype">
            <el-select v-model="timeForm.ttdsmtype" :disabled="!!timeForm.taskNo">
              <el-option label="个人结算" value="P"></el-option>
              <el-option label="团队结算" value="T"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="工时价">
            <el-input v-model="$store.state.user.hourlyWage" :disabled="true"></el-input>
          </el-form-item>
        </template>
        <template v-else>
          <el-form-item label="部位" prop="mpaname">
            <el-input v-model="timeForm.mpaname" :disabled="true"></el-input>
          </el-form-item>
          <el-form-item label="组件" prop="sasname">
            <el-input v-model="timeForm.sasname" :disabled="true"></el-input>
          </el-form-item>
          <el-form-item label="子件" prop="ssuname">
            <el-input v-model="timeForm.ssuname" :disabled="true"></el-input>
          </el-form-item>
        </template>
        <el-form-item label="工时" prop="ttdhour">
          <el-input
            v-model="timeForm.ttdhour"
            placeholder="请输入工时"
            @input="(value) => (timeForm.ttdhour = value.replace(/[^0-9.]/g, ''))"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button @click="timeDialogClose">取 消</el-button>
        <el-button type="primary" @click="recordTime">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import service from "../../router/request";
import { debounce, isMobile } from "@/utils/utils";

export default {
  props: { loginid: "" },
  data() {
    const validateHours = (rule, value, callback) => {
      if (!value) callback(new Error("工时不能为空"));
      else if (value.indexOf(".") != -1 && value.split(".").length > 2) callback(new Error("工时格式不正确"));
      else if (value.indexOf(".") != -1 && value.split(".")[1].length > 2) callback(new Error("工时只能保留两位小数"));
      else callback();
    };
    return {
      date: new Date(),
      prono: "",
      isSearch: false,
      pieceTasks: [],
      timeTasks: [],
      temporaryTasks: [],
      pieceTaskDescriptionConfig: [
        { label: "工程号", prop: "prono" },
        { label: "任务编号", prop: "tadno" },
        { label: "任务工单编号", prop: "tas_no" },
        { label: "部位", prop: "mpaname" },
        { label: "组件", prop: "sasname" },
        { label: "子件", prop: "ssuname" },

        { label: "工时", prop: "manhours" },
        { label: "工时价", prop: "mhprice" },
        { label: "数量", prop: "tasqty" },
        { label: "总工价", prop: "taswage" },

        { label: "任务备注", prop: "tasdremarks" },
        { label: "任务交期", prop: "tasddeldate" },
      ],
      timeTaskDescriptionConfig: [
        { label: "工程号", prop: "ttdprono" },
        { label: "任务编号", prop: "ttdno" },
        { label: "任务工单编号", prop: "ttm_no" },
        { label: "任务内容", prop: "ttmcontent" },
        { label: "预计工时", prop: "ttdhour" },
        { label: "工时价", prop: "hourlyWage" },
        // { label: "总工价", prop: "ttdwage" },
        { label: "任务备注", prop: "ttdremarks" },
        { label: "开始时间", prop: "ttmsdate" },
        { label: "结束时间", prop: "ttmedate" },
      ],
      temporaryTaskDescriptionConfig: [
        { label: "工程号", prop: "ttdprono" },
        { label: "任务编号", prop: "ttdno" },
        { label: "任务工单编号", prop: "ttm_no" },
        { label: "任务内容", prop: "ttmcontent" },
        { label: "数量", prop: "ttdhour" },
        { label: "单价", prop: "ttdprice" },
        { label: "总工价", prop: "ttdwage" },
        { label: "任务备注", prop: "ttdremarks" },
        { label: "开始时间", prop: "ttmsdate" },
        { label: "结束时间", prop: "ttmedate" },
      ],
      calendarSpread: false,
      timeDialogVisible: false,
      timeForm: {
        taskNo: "",
        ttdprono: "",
        ttmcontent: "",
        ttdhour: "",
        ttdremarks: "",
        ttdsmtype: "P",
      },
      timeFormRules: {
        ttdprono: [{ required: true, message: "请选择工程编码", trigger: "blur" }],
        ttmcontent: [{ required: true, message: "请填写任务内容", trigger: "change" }],
        ttdhour: [
          // { required: true, message: "请填写工时", trigger: "blur" },
          // { type: 'number', message: '工时必须为数字' }
          { validator: validateHours, trigger: "blur", required: true },
        ],
      },
      loading: false,
      proNos: [], // 工程编码
      recordDates: [], // 当月已录工时日期数据
      task: null,
      hoursExceptionList: [],
      hoursSucceedList: [],
      todayTaskHours: [],
      contentOptions: ["装配", "接电", "调试", "接气", "打包发货", "更新优化", "异常处理", "卫生清理", "出差", "其他"],
    };
  },

  mounted() {
    this.getAllTask(true);
    this.getProNos("");
    this.getRecordDates();
    this.getTaskHoursAuditList();

    // 初始化页面标题位置（日历默认是折叠状态）
    this.$nextTick(() => {
      const pageHeader = document.querySelector(".page-header");
      if (pageHeader) {
        pageHeader.style.paddingTop = "80px"; // 折叠状态的初始值
      }
    });
  },

  watch: {
    date(_new, _old) {
      if (this.$moment(_new).format("yyyy-MM") != this.$moment(_old).format("yyyy-MM")) {
        service
          .get("/DailyReport/getHourlyWage", {
            params: {
              loginId: this.$store.state.user.loginId,
              month: this.$moment(this.date).format("yyyy-MM"),
            },
          })
          .then((res) => {
            if (res.status == "succeed") this.$store.state.user.hourlyWage = res.data.hourlyWage;
          });
      }
    },
  },

  computed: {
    exceptionData() {
      let obj = this.hoursExceptionList.find((item) => item.date == this.$moment(this.date).format("yyyy-MM-DD"));
      return obj;
    },
  },

  methods: {
    async getAllTask(isFirst = false) {
      const res = await service.get("/DailyReport/getAllTask", {
        params: {
          loginId: this.$store.state.user.loginId,
          date: this.$moment(this.date).format("yyyy-MM-DD"),
          prono: this.prono,
        },
      });
      if (res.status == "succeed") {
        this.pieceTasks = res.data.pieceTasks;
        this.timeTasks = res.data.timeTasks;
        this.temporaryTasks = res.data.temporaryTasks;
        //
        isFirst && this.$watch("date", this.watchDate, { immediate: true });
        //
        this.pieceTasks.forEach((item) => this.$set(item, "timeHistoryWidth", "30px"));
        this.timeTasks.forEach((item) => this.$set(item, "timeHistoryWidth", "30px"));
        this.temporaryTasks.forEach((item) => this.$set(item, "timeHistoryWidth", "30px"));
      }
      return res;
    },

    async watchDate(newVal, oldVal) {
      oldVal && (await this.getAllTask());

      this.getTaskHoursList(this.$moment(newVal).format("yyyy-MM-DD"));
      let newDate = this.$moment(newVal).format("yyyy-MM");
      let oldDate = this.$moment(oldVal).format("yyyy-MM");
      if (newDate != oldDate) {
        this.getRecordDates();
        this.getTaskHoursAuditList();
      }
    },

    getTaskHoursList(date) {
      service
        .post("/TaskHours/getTaskHoursList", {
          loginId: this.$store.state.user.loginId,
          date,
        })
        .then((res) => {
          if (res.status == "succeed") {
            if (res.data?.length) {
              this.todayTaskHours = res.data;
              this.pieceTasks.forEach((item) => {
                let taskHours = res.data.find((taskHours) => taskHours.taskNo == item.tadno);
                this.$set(item, "showException", taskHours && this.exceptionData);
                this.$set(item, "taskHours", taskHours);
              });
              this.timeTasks.forEach((item) => {
                let taskHours = res.data.find((taskHours) => taskHours.taskNo == item.ttdno);
                this.$set(item, "showException", taskHours && this.exceptionData);
                this.$set(item, "taskHours", taskHours);
              });
              this.temporaryTasks.forEach((item) => {
                let taskHours = res.data.find((taskHours) => taskHours.taskNo == item.ttdno);
                this.$set(item, "showException", taskHours && this.exceptionData);
                this.$set(item, "taskHours", taskHours);
              });
            }
          }
        });
    },

    toggle() {
      this.calendarSpread = !this.calendarSpread;
      this.$refs.calendarRef
        .querySelector("#spread-icon")
        .setAttribute("style", `transform: rotate(${this.calendarSpread ? "180deg" : "0deg"})`);
      this.$refs.calendarRef
        .querySelector("#date")
        .setAttribute("style", `height: ${this.calendarSpread ? "0px" : "40px"}`);
      let calendarHeaderHeight = this.$refs.calendarRef.querySelector(".el-calendar__header").offsetHeight;
      let calendarBodyHeight = this.$refs.calendarRef.querySelector(".el-calendar__body").offsetHeight;
      this.$refs.calendarRef
        .querySelectorAll(".el-calendar")[0]
        .setAttribute(
          "style",
          `height: ${this.calendarSpread ? `${calendarHeaderHeight + calendarBodyHeight}px` : "0px"}`
        );

      // 动态调整页面标题的位置
      this.$nextTick(() => {
        const pageHeader = document.querySelector(".page-header");
        if (pageHeader) {
          const calendarHeight = this.calendarSpread
            ? 40 + calendarHeaderHeight + calendarBodyHeight + 30 // 日期显示 + 日历头部 + 日历主体 + 切换按钮
            : 70; // 折叠时只有日期显示和切换按钮
          pageHeader.style.paddingTop = `${calendarHeight + 10}px`; // 额外10px间距
        }
      });
    },

    openTimeDialog(task = null, taskType = "") {
      if (this.hoursSucceedList.some((item) => item.date == this.$moment(this.date).format("yyyy-MM-DD")))
        return this.$message.warning({
          message: "当天工时已被审核通过，不可改动！",
          customClass: "phone-message-tip",
        });
      this.task = task;
      if (task) {
        this.timeDialogVisible = true;

        let taskNo = task.ttdno || task.tadno;
        let cb = (data = null) => {
          this.$nextTick(() => {
            if (taskNo.includes("TTD")) {
              this.timeForm.ttdprono = task.ttdprono.split(",");
              this.timeForm.ttmcontent = task.ttmcontent;
              this.timeForm.ttdremarks = task.ttdremarks;
              this.timeForm.ttdsmtype = task.ttdsmtype;
            } else {
              this.timeForm.ttdprono = task.prono.split(",");
              this.timeForm.mpaname = task.mpaname;
              this.timeForm.sasname = task.sasname;
              this.timeForm.ssuname = task.ssuname;
            }
            this.timeForm.taskNo = taskNo;
            this.timeForm.ttdhour = data?.hours || "";
            this.timeForm.taskHoursId = data?.id || 0;
          });
        };
        task.taskHours ? cb(task.taskHours) : cb();
      } else {
        this.timeDialogVisible = true;
        this.timeForm.taskType = taskType;
      }
    },

    recordTime() {
      this.$refs.timeFormRef.validate((valid) => {
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: "录入中.....",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          let recordDate = this.$moment(this.date).format("yyyy-MM-DD");
          let params = {
            loginId: this.$store.state.user.loginId,
            taskNo: this.timeForm.taskNo,
            hours: this.timeForm.ttdhour,
            recordDate,
            taskHoursId: this.timeForm.taskHoursId,
            tmeno: this.$store.state.user.teamNo,
          };
          if (!this.timeForm.taskNo)
            params = {
              loginId: this.$store.state.user.loginId,
              hours: this.timeForm.ttdhour,
              ...this.timeForm,
              ttdprono: this.timeForm.ttdprono?.join(",") || "",
              recordDate,
              tmeno: this.$store.state.user.teamNo,
            };
          service.post("/TaskHours/recordTime", params).then(async (res) => {
            loading.close();
            if (res.status == "succeed") {
              if (this.task) {
                this.task.timeHistory && this.getTaskHoursHistory(this.task);
                this.getTaskHours(this.task);
              } else {
                await this.getAllTask();
                this.getTaskHoursList(recordDate);
              } // 添加任务
              this.$message({
                message: "录入成功",
                type: "success",
                customClass: "phone-message-tip",
              });

              !this.recordDates.some((item) => item == recordDate) && this.recordDates.push(recordDate);
              this.timeDialogClose();
            } else {
              this.$message({
                message: "录入失败" + res.message,
                type: "error",
                customClass: "phone-message-tip",
              });
            }
          });
        }
      });
    },

    getTaskHours(task) {
      service
        .post("/TaskHours/getTaskHours", {
          taskNo: task.ttdno || task.tadno,
          recordDate: this.$moment(this.date).format("yyyy-MM-DD"),
        })
        .then((res) => {
          if (res.status == "succeed") {
            this.$set(task, "taskHours", res.data);
          }
        });
    },

    getProNos(key) {
      this.loading = true;
      service.get("/TaskHours/getProNos", { params: { key } }).then((res) => {
        if (res.status == "succeed") {
          this.loading = false;
          this.proNos = res.data;
        }
      });
    },

    timeDialogClose() {
      this.timeForm.taskNo = "";
      this.timeForm.taskHoursId = 0;
      this.$refs.timeFormRef.resetFields();
      this.timeDialogVisible = false;
    },

    timeHistorySpread(task) {
      task.timeHistoryWidth == "30px" && !task.timeHistory?.length && this.getTaskHoursHistory(task);
      task.timeHistoryWidth = task.timeHistoryWidth == "30px" ? "100%" : "30px";
    },

    getTaskHoursHistory(task) {
      service
        .post("/TaskHours/getTaskHoursList", {
          taskNo: task.ttdno || task.tadno,
        })
        .then((res) => {
          if (res.status == "succeed" && res.data) {
            this.$set(task, "timeHistory", res.data);
          }
        });
    },

    getRecordDates() {
      service
        .get("/TaskHours/getRecordDates", {
          params: {
            month: this.$moment(this.date).format("yyyy-MM"),
            loginId: this.$store.state.user.loginId,
          },
        })
        .then((res) => {
          if (res.status == "succeed") {
            this.recordDates = res.data || [];
            this.renderingIconColor("check");
          }
        });
    },

    getTaskHoursAuditList() {
      service
        .post("/TaskHoursAudit/getTaskHoursAuditList", {
          status: null,
          loginId: this.$store.state.user.loginId,
          month: this.$moment(this.date).format("yyyy-MM"),
        })
        .then((res) => {
          if (res.status == "succeed") {
            this.hoursExceptionList = res.data.filter((item) => item.status == 2);
            this.hoursSucceedList = res.data.filter((item) => item.status == 1);
            this.renderingIconColor("warning");
          }
        });
    },

    commitAudit() {
      service
        .post("/TaskHoursAudit/taskHoursAudit", {
          id: this.exceptionData.id,
          status: 0,
        })
        .then((res) => {
          if (res.status == "succeed") {
            this.hoursExceptionList.splice(
              this.hoursExceptionList.findIndex((item) => item.id == this.exceptionData.id),
              1
            );
            this.pieceTasks.forEach((item) => item.showException && (item.showException = false));
            this.timeTasks.forEach((item) => item.showException && (item.showException = false));
            this.temporaryTasks.forEach((item) => item.showException && (item.showException = false));
            this.$message.success({
              message: "提交成功",
              customClass: "phone-message-tip",
            });
          }
        });
    },

    renderingIconColor(type) {
      let nodeArr;
      let color;
      this.$nextTick(() => {
        if (type == "check") {
          nodeArr = document.getElementsByClassName("el-icon-circle-check");
          color = "#44a0ff7a";
        }
        if (type == "warning") {
          nodeArr = document.getElementsByClassName("el-icon-warning");
          color = "red";
        }
        for (let index = 0; index < nodeArr.length; index++) {
          const node = nodeArr[index];
          node.style.setProperty("color", color, "important");
        }
      });
    },

    clickSearch() {
      this.isSearch = true;
      this.$nextTick(() => {
        this.$refs.searchInputRef.focus();
      });
    },

    searchInputBlur() {
      setTimeout(() => (this.isSearch = false), 0);
    },

    async search() {
      const res = await this.getAllTask();
      if (res.status == "succeed") {
        this.$message.success({
          message: "搜索成功",
          customClass: "phone-message-tip",
        });
      }
    },

    deleteTask(task) {
      let date = this.$moment(this.date).format("yyyy-MM-DD");
      if (
        this.hoursExceptionList.some((item) => item.date == date) ||
        this.hoursSucceedList.some((item) => item.date == date)
      )
        return this.$message.warning({ message: "日报已审，不可删除", customClass: "phone-message-tip" });

      this.$confirm("确定要删除该日报吗?", "提示", { type: "warning" })
        .then(() => {
          service
            .post("/TaskHours/deleteTaskHours", { taskHoursId: task.taskHours.id, taskNo: task.taskHours.taskNo })
            .then((res) => {
              if (res.status == "succeed") {
                this.getAllTask();
                this.$message({
                  message: "删除成功",
                  customClass: isMobile() ? "phone-message-tip" : "",
                  type: "success",
                });
              } else {
                this.$message({ message: res.err, customClass: isMobile() ? "phone-message-tip" : "", type: "error" });
              }
            });
        })
        .catch(() => {});
    },
  },
};
</script>

<style scoped>
.container {
  background: #f0f0f0;
  min-height: 100vh;
  margin-top: 78px;
}

.title {
  margin: 0;
  text-align: center;
  padding: 20px;
}

>>> .el-descriptions .is-bordered .el-descriptions-item__cell {
  border-color: rgba(215, 215, 217, 0.9);
}

.desc-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.sequence-number {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 34px;
  height: 36px;
  background: #409eff;
  border-radius: 50%;
  font-size: 16px;
  line-height: 16px;
  font-weight: bold;
  color: #fff;
  margin: 10px 0;
}

.date {
  text-align: center;
  background-color: #fff;
  transition: all 0.5s;
  height: 40px;
  line-height: 40px;
  overflow: hidden;
}

.down-icon {
  background-color: #fff;
  padding: 10px 0;
  text-align: center;
}

.el-calendar {
  height: 0;
  overflow: hidden;
  transition: all 0.5s;
}

>>> .el-calendar__body {
  padding: 12px 20px 0;
}

>>> .el-calendar-table .el-calendar-day {
  height: auto;
}

.time-btn {
  border-bottom: 1px solid #d7d7d7;
  width: 100%;
  text-align: center;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

>>> .el-form-item__label {
  line-height: 1;
  margin-bottom: 5px;
}

>>> .el-form-item__content {
  line-height: 1;
}

>>> .el-timeline-item__wrapper {
  white-space: nowrap;
}

.time-history-container {
  display: flex;
  transition: all 0.5s;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: #fff;
}

.spread-icon {
  width: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.history-box {
  overflow: hidden auto;
  flex: 1;
}

.calendar-cell {
  position: relative;
  margin: 0;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hours-exception {
  color: red !important;
}

>>> .current .calendar-cell {
  color: #000000;
}

.circle-check {
  position: absolute;
  font-size: 34px;
  width: 100%;
  opacity: 0.4;
}

.el-icon-warning {
  position: absolute;

  /* font-size: 34px; */
  font-size: 10px;
  top: 0;
  right: 0;
}

.time-line {
  padding: 5px;
}

.record-img {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0.5;
}

.search-box {
  display: flex;
  position: fixed;
  z-index: 1;
  top: 50%;
  transform: translateY(-50%);
  right: 10px;
  background-color: #ebebeb;
  border-radius: 50%;
}

>>> .search-box .el-input__inner {
  border-radius: 20px 0 0 20px;
}

>>> .search-box .el-input-group__append {
  border-radius: 0px 20px 20px 0px;
}

/* 移动端优化样式 */
.daily-work-container {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  padding-bottom: env(safe-area-inset-bottom, 20px);
}

/* 日历区域优化 */
.calendar-section {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: white;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.date-display {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: bold;
  font-size: clamp(16px, 4vw, 20px);
  padding: 12px 0;
  text-align: center;
  transition: height 0.3s ease;
}

.mobile-calendar {
  transition: height 0.3s ease;
  overflow: hidden;
}

.calendar-toggle {
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  transition: all 0.3s ease;
}

.calendar-toggle:hover {
  transform: translateX(-50%) scale(1.1);
}

.calendar-toggle i {
  transition: transform 0.3s ease;
}

.calendar-cell {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px 4px;
  min-height: 40px;
}

.date-number {
  font-size: clamp(12px, 3vw, 16px);
  font-weight: 500;
}

.status-icon {
  position: absolute;
  font-size: 12px;

  &.exception {
    top: 2px;
    right: 2px;
    color: #f56c6c;
  }

  &.completed {
    bottom: 2px;
    right: 2px;
    color: #67c23a;
  }
}

/* 页面标题优化 */
.page-header {
  text-align: center;
  padding: 80px 20px 20px;

  .page-title {
    font-size: clamp(1.5rem, 5vw, 2rem);
    font-weight: bold;
    color: #333;
    margin: 0 0 8px 0;
  }

  .date-info {
    font-size: clamp(0.9rem, 3vw, 1.1rem);
    color: #666;
    margin: 0;
  }
}

/* 内容区域优化 */
.content-section {
  padding: 0 clamp(12px, 3vw, 20px);
  max-width: 800px;
  margin: 0 auto;
}

/* 用户信息卡片优化 */
.user-info-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.user-info {
  display: flex;
  justify-content: space-around;
  margin-bottom: 12px;
}

.info-item {
  text-align: center;
  flex: 1;
}

.info-item .label {
  display: block;
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.info-item .value {
  display: block;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.audit-section {
  text-align: right;
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

.audit-btn {
  border-radius: 20px;
  padding: 8px 20px;
}

/* 任务卡片优化 */
.task-card {
  background: white;
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.task-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #e9ecef;
}

.task-title {
  margin: 0;
  font-size: clamp(14px, 3.5vw, 18px);
  font-weight: 600;
  color: #333;
}

.add-task-btn {
  background: #409eff;
  color: white;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-task-btn:hover {
  background: #66b1ff;
  transform: scale(1.1);
}

.add-task-btn:active {
  transform: scale(0.95);
}

.add-task-btn i {
  font-size: 16px;
}

/* 移动端响应式优化 */
@media (max-width: 768px) {
  .page-header {
    padding: 75px 16px 16px;
  }

  .content-section {
    padding: 0 12px;
  }

  .user-info-card,
  .task-card {
    margin-bottom: 12px;
    border-radius: 8px;
  }

  .task-header {
    padding: 12px;
  }

  .add-task-btn {
    width: 28px;
    height: 28px;
  }

  .add-task-btn i {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .calendar-section {
    font-size: 14px;
  }

  .date-display {
    padding: 10px 0;
  }

  .page-header {
    padding: 70px 12px 12px;
  }

  .user-info {
    flex-direction: column;
    gap: 8px;
  }

  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .info-item:last-child {
    border-bottom: none;
  }

  .info-item .label,
  .info-item .value {
    display: inline;
    margin: 0;
  }
}
</style>
