<template>
  <div>
    <div ref="searchBarRef" class="search-bar">
      <div class="search-item" style="width: 450px">
        <span class="label">搜索内容：</span>
        <el-select v-model="fieldName" class="mr-1">
          <el-option v-for="opt of fieldOptions" :key="opt.value" v-bind="opt"></el-option>
        </el-select>
        <el-input v-model="fieldValue" filterable placeholder="请输入" clearable></el-input>
      </div>
      <el-button type="primary" class="search-btn" @click="serach">搜索</el-button>
    </div>
    <el-table v-if="maxTableHeight" :data="tableData" border :max-height="maxTableHeight">
      <el-table-column v-for="item of tableConfig" :key="item.prop" v-bind="item"></el-table-column>
      <el-table-column label="操作" fixed="right">
        <template v-slot="scope">
          <el-link type="primary" @click="assess(scope.row)">评估</el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      ref="paginationRef"
      style="width: 75%"
      @size-change="pageSizeChange"
      @current-change="pageNumChange"
      :current-page="paging.pageNum"
      :page-sizes="[5, 10, 20, 40]"
      :page-size="paging.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="paging.total"
    ></el-pagination>
  </div>
</template>

<script>
import service from "@/router/request";
export default {
  name: "monthlyassess",
  data() {
    return {
      tableData: [],
      tableConfig: [
        { label: "计划单号", prop: "planno", "min-width": "160" },
        { label: "工程编码", prop: "prono", "min-width": "120" },
        { label: "机型型号", prop: "mmomodel" },
        { label: "机型名称", prop: "proname", "min-width": "110" },
        { label: "任务创建者", prop: "assignor", width: "70" },
        {
          label: "任务备注",
          prop: "remarkse",
          "min-width": "200",
          showOverflowTooltip: true
        },
        { label: "任务编号", prop: "tasno", width: "120" },
        { label: "任务组号", prop: "tagno", width: "120" },
        { label: "创建日期", prop: "assigntime", "min-width": "160" },
        { label: "任务状态", prop: "adstatusn" },

        { label: "装配工单", prop: "apno", width: "125" },
        { label: "装配交期", prop: "apdmdate", width: "100" },
        {
          label: "装配备注",
          prop: "apremarks",
          "min-width": "150",
          showOverflowTooltip: true
        }
      ],
      fieldName: "planno",
      fieldValue: "",
      fieldOptions: [
        { label: "计划单号", value: "planno" },
        { label: "机型编码", value: "prono" },
        { label: "机型", value: "mmomodel" },
        { label: "任务编号", value: "tasno" }
      ],
      paging: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      maxTableHeight: 0
    };
  },

  mounted() {
    this.getTask();
    this.computedMaxTableHeight();
  },

  methods: {
    serach() {
      this.paging.pageNum = 1;
      this.getTask();
    },

    getTask() {
      service
        .post("/TaskCheck/getAssigngro", {
          ...this.paging,
          fieldName: this.fieldName,
          fieldValue: this.fieldValue
        })
        .then(res => {
          if (res.status == "succeed") {
            this.tableData = res.data?.list;
            this.paging.total = res.data?.total;
          }
        });
    },

    pageSizeChange(val) {
      this.paging.pageSize = val;
      this.getTask();
    },

    pageNumChange(val) {
      this.paging.pageNum = val;
      this.getTask();
    },

    assess(row) {
      this.$router.push({
        name: "monthlyAssessDetails",
        query: { taskInfo: encodeURIComponent(JSON.stringify(row)) }
      });
    },

    computedMaxTableHeight() {
      let main = document.getElementById('main');
      this.maxTableHeight = main.clientHeight - this.$refs.searchBarRef.clientHeight - this.$refs.paginationRef.$el.clientHeight - 20 - 40;
    }
  }
};
</script>

<style>
.search-bar {
  margin-bottom: 20px;
}
.search-bar > div {
  display: flex;
  justify-content: center;
  margin-bottom: 10px;
}
.search-bar .search-item {
  margin-right: 10px;
}
</style>
