<template>
  <div class="container">
    <TopNavigationBar title="新建领用单"></TopNavigationBar>
    <div class="form-container">
      <el-form :model="form" :rules="rules" ref="formRef" label-position="top" size="small">
        <el-form-item label="领用类型" prop="requisitionType">
          <el-radio-group v-model="form.requisitionType" @change="handleTypeChange">
            <el-radio :label="1">新领</el-radio>
            <el-radio :label="2">以旧换新</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="选择物品" prop="materialId">
          <el-select
            v-model="form.materialId"
            placeholder="请选择物品"
            style="width: 100%"
            @change="handleMaterialChange"
            filterable
            remote
            reserve-keyword
            :remote-method="loadMaterialList"
            :loading="materialLoading"
          >
            <template slot="prefix">
              <i class="el-icon-search"></i>
            </template>
            <el-option
              v-for="item in materialList"
              :key="item.id"
              :label="`${item.name} (${item.unit})`"
              :value="item.id"
            >
              <div class="material-option">
                <div class="material-name">{{ item.name }}</div>
                <div class="material-info">{{ item.unit }} | {{ item.remark || "无备注" }}</div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="物品数量" prop="quantity">
          <el-input-number
            v-model="form.quantity"
            :min="1"
            :max="9999"
            placeholder="请输入数量"
            style="width: 100%"
          ></el-input-number>
        </el-form-item>

        <div v-if="selectedMaterial" class="material-info-display">
          <div class="info-item">
            <label>物品名称:</label>
            <span>{{ selectedMaterial.name }}</span>
          </div>
          <div class="info-item">
            <label>单位:</label>
            <span>{{ selectedMaterial.unit }}</span>
          </div>
          <div v-if="selectedMaterial.remark" class="info-item">
            <label>备注:</label>
            <span>{{ selectedMaterial.remark }}</span>
          </div>
        </div>

        <!-- 以旧换新时的旧物品信息 -->
        <div v-if="form.requisitionType === 2" class="old-material-section">
          <h4 class="section-title">旧物品信息</h4>

          <el-form-item label="旧物品状态" prop="oldMaterialCondition">
            <el-select v-model="form.oldMaterialCondition" placeholder="请选择旧物品状态">
              <el-option label="损坏" :value="1"></el-option>
              <el-option label="磨损" :value="2"></el-option>
              <el-option label="过期" :value="3"></el-option>
              <el-option label="其他" :value="0"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="旧物品描述" prop="oldMaterialDescription">
            <el-input
              type="textarea"
              v-model="form.oldMaterialDescription"
              placeholder="请描述旧物品的具体情况（如损坏程度、使用时间等）"
              rows="3"
            ></el-input>
          </el-form-item>
        </div>

        <el-form-item label="用途说明" prop="purpose">
          <el-input type="textarea" v-model="form.purpose" placeholder="请详细说明物品用途" rows="4"></el-input>
        </el-form-item>

        <el-form-item label="备注">
          <el-input type="textarea" v-model="form.remark" placeholder="其他需要说明的信息（选填）" rows="3"></el-input>
        </el-form-item>
      </el-form>

      <div class="form-actions">
        <el-button @click="resetForm">重置</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting"> 提交申请 </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import TopNavigationBar from "@/components/XTopNavigationBar.vue";
import service from "@/router/request";
import { MessageUtil } from "@/utils/utils";

export default {
  components: { TopNavigationBar },
  name: "CreateRequisition",
  data() {
    return {
      submitting: false,
      materialList: [], // 物品列表
      selectedMaterial: null, // 选中的物品
      form: {
        requisitionType: 1, // 1=新领, 2=以旧换新
        materialId: "",
        quantity: 1,
        purpose: "",
        oldMaterialCondition: null, // 旧物品状态
        oldMaterialDescription: "", // 旧物品描述
        remark: "",
      },
      rules: {
        requisitionType: [{ required: true, message: "请选择领用类型", trigger: "change" }],
        materialId: [{ required: true, message: "请选择物品", trigger: "change" }],
        quantity: [
          { required: true, message: "请输入数量", trigger: "blur" },
          { type: "number", min: 1, message: "数量必须大于0", trigger: "blur" },
        ],
        purpose: [
          { required: true, message: "请输入用途说明", trigger: "blur" },
          { min: 5, max: 200, message: "用途说明长度在5-200个字符", trigger: "blur" },
        ],
        oldMaterialCondition: [
          {
            validator: (_, value, callback) => {
              if (this.form.requisitionType === 2 && !value) {
                callback(new Error("请选择旧物品状态"));
              } else {
                callback();
              }
            },
            trigger: "change",
          },
        ],
        oldMaterialDescription: [
          {
            validator: (_, value, callback) => {
              if (this.form.requisitionType === 2) {
                if (!value) {
                  callback(new Error("请描述旧物品情况"));
                } else if (value.length < 10 || value.length > 500) {
                  callback(new Error("旧物品描述长度在10-500个字符"));
                } else {
                  callback();
                }
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
      },
      materialLoading: false,
    };
  },

  mounted() {
    this.loadMaterialList();
  },

  methods: {
    // 加载物品列表
    async loadMaterialList(search) {
      this.materialLoading = true;
      try {
        const response = await service.get("/Material", { params: { search, isActive: 1 } });
        if (response.status === "succeed") {
          this.materialList = response.data.data || [];
        } else {
          MessageUtil.error("获取物品列表失败");
        }
      } catch (error) {
        MessageUtil.error("获取物品列表失败");
      } finally {
        this.materialLoading = false;
      }
    },

    // 处理领用类型变化
    handleTypeChange(type) {
      // 切换类型时清空旧物品相关字段
      if (type === 1) {
        this.form.oldMaterialCondition = null;
        this.form.oldMaterialDescription = "";
      }
    },

    // 处理物品选择变化
    handleMaterialChange(materialId) {
      this.selectedMaterial = this.materialList.find((item) => item.id === materialId);
    },
    // 提交表单
    submitForm() {
      this.$refs.formRef.validate(async (valid) => {
        if (valid) {
          this.submitting = true;
          try {
            const response = await service.post("/MaterialRequisition/Create", this.form);
            if (response.status === "succeed") {
              MessageUtil.success("领用申请提交成功！");
              // this.$router.push("/myRequisitions");
            } else {
              MessageUtil.error("提交失败，请稍后重试！");
            }
          } catch (error) {
            console.error("提交领用申请失败:", error);
            MessageUtil.error("提交失败，请稍后重试！");
          } finally {
            this.submitting = false;
          }
        } else {
          MessageUtil.error("请完善表单信息！");
        }
      });
    },

    // 重置表单
    resetForm() {
      this.$refs.formRef.resetFields();
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.form-container {
  flex: 1;
  padding: 16px;
  max-width: 600px;
  margin: 0 auto;
  width: 100%;
}

.el-form {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-actions {
  margin-top: 20px;
  text-align: center;
  display: flex;
  gap: 16px;
  justify-content: center;

  .el-button {
    min-width: 100px;
  }
}

/* 物品选择相关样式 */
.material-option {
  display: flex;
  justify-content: space-between;
  .material-name {
    font-weight: 600;
    color: #303133;
    margin-bottom: 2px;
  }

  .material-info {
    font-size: 12px;
    color: #909399;
  }
}

.material-info-display {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;

  .info-item {
    display: flex;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    label {
      min-width: 80px;
      font-weight: 600;
      color: #606266;
      margin-right: 12px;
    }

    span {
      color: #303133;
      flex: 1;
    }
  }
}

/* 旧物品信息区域样式 */
.old-material-section {
  margin-top: 20px;
  padding: 16px;
  background-color: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 6px;

  .section-title {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #606266;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 8px;
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .form-container {
    padding: 12px;
  }

  .el-form {
    padding: 16px;
  }

  .form-actions {
    flex-direction: column;

    .el-button {
      width: 100%;
    }
  }
}

/* 搜索图标样式 */
.el-select .el-icon-search {
  color: #c0c4cc;
  font-size: 14px;
}
</style>
