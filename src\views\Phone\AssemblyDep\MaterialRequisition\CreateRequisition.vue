<template>
  <div class="container">
    <TopNavigationBar title="新建领用单"></TopNavigationBar>
    <div class="form-container">
      <el-form :model="form" :rules="rules" ref="formRef" label-position="top" size="small">
        <el-form-item label="物品名称" prop="materialName">
          <el-input
            v-model="form.materialName"
            placeholder="请输入物品名称"
            clearable
          ></el-input>
        </el-form-item>

        <el-form-item label="物品数量" prop="quantity">
          <el-input-number
            v-model="form.quantity"
            :min="1"
            :max="9999"
            placeholder="请输入数量"
            style="width: 100%"
          ></el-input-number>
        </el-form-item>

        <el-form-item label="单位" prop="unit">
          <el-select v-model="form.unit" placeholder="请选择单位" style="width: 100%">
            <el-option label="个" value="个"></el-option>
            <el-option label="件" value="件"></el-option>
            <el-option label="套" value="套"></el-option>
            <el-option label="盒" value="盒"></el-option>
            <el-option label="包" value="包"></el-option>
            <el-option label="箱" value="箱"></el-option>
            <el-option label="米" value="米"></el-option>
            <el-option label="公斤" value="公斤"></el-option>
            <el-option label="升" value="升"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="用途说明" prop="purpose">
          <el-input
            type="textarea"
            v-model="form.purpose"
            placeholder="请详细说明物品用途"
            rows="4"
          ></el-input>
        </el-form-item>

        <el-form-item label="预计使用时间" prop="expectedUseDate">
          <el-date-picker
            v-model="form.expectedUseDate"
            type="date"
            placeholder="选择预计使用时间"
            style="width: 100%"
            value-format="yyyy-MM-dd"
          ></el-date-picker>
        </el-form-item>

        <el-form-item label="紧急程度" prop="urgency">
          <el-radio-group v-model="form.urgency">
            <el-radio :label="1">普通</el-radio>
            <el-radio :label="2">紧急</el-radio>
            <el-radio :label="3">特急</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="附件">
          <el-upload
            ref="uploadRef"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :on-remove="handleRemoveFile"
            :file-list="fileList"
            multiple
            :limit="5"
            accept=".jpg,.jpeg,.png,.pdf,.doc,.docx,.xls,.xlsx"
          >
            <el-button size="small" type="primary">
              <i class="el-icon-upload"></i> 上传附件
            </el-button>
            <div slot="tip" class="el-upload__tip">
              支持jpg/png/pdf/doc/xls格式，最多5个文件，每个文件不超过10MB
            </div>
          </el-upload>
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            type="textarea"
            v-model="form.remark"
            placeholder="其他需要说明的信息（选填）"
            rows="3"
          ></el-input>
        </el-form-item>
      </el-form>

      <div class="form-actions">
        <el-button @click="resetForm">重置</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          提交申请
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import TopNavigationBar from "@/components/XTopNavigationBar.vue";
import service from "@/router/request";
import { MessageUtil } from "@/utils/utils";

export default {
  components: { TopNavigationBar },
  name: "CreateRequisition",
  data() {
    return {
      submitting: false,
      form: {
        materialName: "",
        quantity: 1,
        unit: "",
        purpose: "",
        expectedUseDate: "",
        urgency: 1,
        remark: "",
        attachments: "",
      },
      rules: {
        materialName: [
          { required: true, message: "请输入物品名称", trigger: "blur" },
          { min: 2, max: 50, message: "物品名称长度在2-50个字符", trigger: "blur" }
        ],
        quantity: [
          { required: true, message: "请输入数量", trigger: "blur" },
          { type: "number", min: 1, message: "数量必须大于0", trigger: "blur" }
        ],
        unit: [
          { required: true, message: "请选择单位", trigger: "change" }
        ],
        purpose: [
          { required: true, message: "请输入用途说明", trigger: "blur" },
          { min: 5, max: 200, message: "用途说明长度在5-200个字符", trigger: "blur" }
        ],
        expectedUseDate: [
          { required: true, message: "请选择预计使用时间", trigger: "change" }
        ],
        urgency: [
          { required: true, message: "请选择紧急程度", trigger: "change" }
        ]
      },
      fileList: [],
      uploadUrl: process.env.VUE_APP_API_BASE_URL + "/upload",
      uploadHeaders: {
        // 这里可以添加认证头等
      },
    };
  },
  methods: {
    // 提交表单
    submitForm() {
      this.$refs.formRef.validate(async (valid) => {
        if (valid) {
          this.submitting = true;
          try {
            // 处理附件数据
            this.form.attachments = this.fileList.map(file => ({
              name: file.name,
              url: file.url || file.response?.url
            }));

            const response = await service.post("/MaterialRequisition/Create", this.form);
            if (response.status === "succeed") {
              MessageUtil.success("领用申请提交成功！");
              this.$router.push("/myRequisitions");
            } else {
              MessageUtil.error("提交失败，请稍后重试！");
            }
          } catch (error) {
            console.error("提交领用申请失败:", error);
            MessageUtil.error("提交失败，请稍后重试！");
          } finally {
            this.submitting = false;
          }
        } else {
          MessageUtil.error("请完善表单信息！");
        }
      });
    },

    // 重置表单
    resetForm() {
      this.$refs.formRef.resetFields();
      this.fileList = [];
      this.form.attachments = "";
    },

    // 文件上传成功
    handleUploadSuccess(response, file, fileList) {
      this.fileList = fileList;
      MessageUtil.success("文件上传成功！");
    },

    // 文件上传失败
    handleUploadError(error, file, fileList) {
      console.error("文件上传失败:", error);
      MessageUtil.error("文件上传失败，请重试！");
    },

    // 移除文件
    handleRemoveFile(file, fileList) {
      this.fileList = fileList;
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.form-container {
  flex: 1;
  padding: 16px;
  max-width: 600px;
  margin: 0 auto;
  width: 100%;
}

.el-form {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-actions {
  margin-top: 20px;
  text-align: center;
  display: flex;
  gap: 16px;
  justify-content: center;

  .el-button {
    min-width: 100px;
  }
}

.el-upload__tip {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .form-container {
    padding: 12px;
  }

  .el-form {
    padding: 16px;
  }

  .form-actions {
    flex-direction: column;
    
    .el-button {
      width: 100%;
    }
  }
}
</style>
