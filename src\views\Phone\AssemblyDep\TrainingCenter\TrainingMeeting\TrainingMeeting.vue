<template>
  <div class="font-inter bg-base-200 text-neutral min-h-screen flex flex-col">
    <TopNavigationBar title="培训会议"></TopNavigationBar>
    <main class="flex-grow container mx-auto px-4 py-6">
      <SearchBar v-model="searchText" placeholder="搜索会议标题或主讲人" @search="search"></SearchBar>
      <div class="space-y-4">
        <div
          v-for="meeting in trainingMeetingList"
          :key="meeting.id"
          class="rounded-xl overflow-hidden shadow-card transition-all duration-300 hover:shadow-card-hover"
          :class="getCardGradientClass(meeting.status)"
        >
          <div class="p-4">
            <div class="flex justify-between items-start mb-3">
              <h3 class="font-bold text-lg">{{ meeting.title }}</h3>
              <span class="px-2 py-1 rounded-full text-xs font-medium" :class="getStatusBadgeClass(meeting.status)">
                {{ getStatusText(meeting.status) }}
              </span>
            </div>
            <div class="flex items-center text-gray-600 text-sm mb-2">
              <i class="fa fa-user-o mr-2 text-primary"></i>
              <span>{{ meeting.speakerName }}</span>
            </div>
            <div class="flex items-center text-gray-600 text-sm mb-3">
              <i class="fa fa-calendar-o mr-2 text-primary"></i>
              <span>{{ meeting.planTime }}</span>
            </div>
            <div
              v-if="meeting.attachments && meeting.attachments.length > 0"
              class="flex items-center text-gray-600 text-sm mb-3"
            >
              <i class="fa fa-paperclip mr-2 text-primary"></i>
              <span>{{ meeting.attachments.split(",").length }}个附件</span>
            </div>
            <div class="flex justify-between items-center">
              <button
                @click="openEditDialog(meeting)"
                class="view-meeting-btn text-primary text-sm font-medium flex items-center"
              >
                <i class="fa fa-eye mr-1"></i>
                <span>详情</span>
              </button>
              <button
                v-if="
                  meeting.status !== 2 &&
                  (meeting.creator == $store.state.user.loginId ||
                    meeting.speaker == $store.state.user.loginId ||
                    havePermissionRole(8))
                "
                @click="openStatusDialog(meeting)"
                class="status-btn px-3 py-1 rounded-lg text-sm font-medium"
                :class="getButtonClass(meeting.status)"
              >
                {{ meeting.status === 0 ? "开始会议" : "结束会议" }}
              </button>
            </div>
          </div>
        </div>
      </div>
      <div class="flex justify-center items-center py-6">
        <button
          @click="loadMoreMeetings"
          class="px-6 py-2 bg-white border border-base-300 rounded-lg text-neutral hover:bg-base-300 transition-colors"
        >
          加载更多
        </button>
      </div>
    </main>
    <button
      v-if="havePermissionRole([1, 2, 8])"
      @click="openAddDialog"
      class="fixed bottom-20 right-6 bg-primary rounded-full w-14 h-14 shadow-lg flex items-center justify-center z-20"
    >
      <i class="fa fa-plus text-2xl"></i>
    </button>
    <el-dialog :visible.sync="statusDialogVisible" title="确认会议状态" width="80%">
      <el-form :model="statusForm">
        <el-form-item label="时间">
          <el-date-picker
            v-model="statusForm.time"
            type="datetime"
            placeholder="选择时间"
            :picker-options="pickerOptions"
            value-format="yyyy-MM-dd HH-mm-ss"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div>
          <el-button @click="statusDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="updateMeetingStatus">
            {{ statusForm.meeting?.status === 0 ? "开始" : "结束" }}会议
          </el-button>
        </div>
      </template>
    </el-dialog>
    <TrainingMeetingDialog
      :visible.sync="dialogVisible"
      :initialData="initialData"
      @confirm="search"
    ></TrainingMeetingDialog>
  </div>
</template>

<script>
import service from "@/router/request";
import TrainingMeetingDialog from "./TrainingMeetingDialog.vue";
import TopNavigationBar from "@/components/XTopNavigationBar.vue";
import SearchBar from "@/components/XSearchBar.vue";
import store from "@/store";
import { havePermissionRole } from "@/utils/permission";

export default {
  components: { TrainingMeetingDialog, TopNavigationBar, SearchBar },
  data() {
    return {
      trainingMeetingList: [],
      statusDialogVisible: false,
      dialogVisible: false,
      initialData: {
        title: "",
        speaker: "",
        participants: [],
        planTime: "",
        attachments: [],
      },
      statusForm: {
        meeting: null,
        time: "",
      },
      searchText: "",
      page: 1,
      pageSize: 10,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7 * 5 * 365;
        },
      },
    };
  },
  created() {
    this.getTrainingMeetingList().then((data) => {
      this.trainingMeetingList = data;
    });
  },
  methods: {
    havePermissionRole,
    async getTrainingMeetingList() {
      const res = await service.post("/Training/getTrainingMeetingList", {
        key: this.searchText,
        pageNum: this.page,
        pageSize: this.pageSize,
        loginId: store.state.user.loginId,
        roleId: store.state.user.roleId,
      });
      return res.status == "succeed" ? res.data : [];
    },
    openAddDialog() {
      this.initialData = null;
      this.dialogVisible = true;
    },
    openEditDialog(meeting) {
      this.initialData = {
        id: meeting.id,
        title: meeting.title,
        speaker: meeting.speaker,
        participants: meeting.participants?.split(",") || [],
        planTime: meeting.planTime,
        attachments: meeting.attachments
          ? meeting.attachments?.split(",").map((url, idx) => ({
              name: `附件${idx + 1}${url.substring(url.lastIndexOf("."))}`,
              url: service.getUri({ url }).replace("/api", ""),
            }))
          : [],
      };
      this.dialogVisible = true;
    },
    openStatusDialog(meeting) {
      this.statusForm = {
        meeting: { ...meeting },
        time: this.$moment().format("yyyy-MM-DD HH:mm:ss"),
      };
      this.statusDialogVisible = true;
    },
    updateMeetingStatus() {
      if (!this.statusForm.time) return MessageUtil.warning("请选择时间");
      this.statusForm.meeting.status++;
      service
        .post("/Training/updateTrainingMeetingStatus", {
          id: this.statusForm.meeting.id,
          status: this.statusForm.meeting.status,
          time: this.statusForm.time,
        })
        .then((res) => {
          if (res.status == "succeed") {
            this.statusDialogVisible = false;
            this.search();
            MessageUtil.success("会议状态已更新");
          } else {
            MessageUtil.error("会议状态更新失败");
          }
        });
    },
    search() {
      this.page = 1;
      this.getTrainingMeetingList().then((data) => {
        this.trainingMeetingList = data;
      });
    },
    loadMoreMeetings() {
      this.page++;
      this.getTrainingMeetingList().then((data) => {
        if (data.length) {
          this.trainingMeetingList = [...this.trainingMeetingList, ...data];
          MessageUtil.success("数据加载成功");
        } else {
          MessageUtil.warning("没有更多数据");
        }
      });
    },
    getStatusText(status) {
      switch (status) {
        case 0:
          return "未开始";
        case 1:
          return "进行中";
        case 2:
          return "已结束";
        default:
          return "未知状态";
      }
    },
    getStatusBadgeClass(status) {
      switch (status) {
        case 0:
          return "bg-gray-100 text-gray-600";
        case 1:
          return "bg-green-100 text-green-600";
        case 2:
          return "bg-red-100 text-red-600";
        default:
          return "bg-gray-100 text-gray-600";
      }
    },
    getButtonClass(status) {
      return status === 0
        ? "bg-green-100 text-green-600 hover:bg-green-200 transition-colors"
        : "bg-red-100 text-red-600 hover:bg-red-200 transition-colors";
    },
    getCardGradientClass(status) {
      switch (status) {
        case 0:
          return "card-gradient-0";
        case 1:
          return "card-gradient-1";
        case 2:
          return "card-gradient-2";
        default:
          return "card-gradient-0";
      }
    },
  },
};
</script>

<style scoped>
@layer utilities {
  .content-auto {
    content-visibility: auto;
  }
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  .card-gradient-0 {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  }
  .card-gradient-1 {
    background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
  }
  .card-gradient-2 {
    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
  }
}
</style>
