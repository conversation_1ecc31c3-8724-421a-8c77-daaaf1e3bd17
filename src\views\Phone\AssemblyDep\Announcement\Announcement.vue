<template>
  <div class="container">
    <TopNavigationBar title="部门通告配置"></TopNavigationBar>
    <div class="announcement">
      <div class="announcement-form">
        <el-form :model="form" :rules="rules" ref="formRef" label-position="top" size="small">
          <el-form-item label="通告类型" prop="type">
            <el-select v-model="form.type" placeholder="请选择通告类型" style="width: 100%; max-width: 300px">
              <el-option v-for="item in announcementTypes" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>

          <el-form-item label="重要性" prop="priority">
            <el-select v-model="form.priority" placeholder="请选择重要性" style="width: 100%; max-width: 300px">
              <el-option v-for="item in priorityOptions" :key="item.value" :label="item.label" :value="item.value">
                <span :style="{ color: item.color, fontWeight: item.value >= 3 ? 'bold' : 'normal' }">
                  {{ item.icon }} {{ item.label }}
                </span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="通告内容" prop="content">
            <el-input type="textarea" v-model="form.content" placeholder="请输入通告内容" rows="5"></el-input>
          </el-form-item>
          <el-form-item label="关联成员" prop="relatedMembers">
            <el-select
              v-model="form.relatedMembers"
              multiple
              filterable
              remote
              :remote-method="getMemberList"
              :loading="memberLoading"
              placeholder="请选择关联成员"
              style="width: 100%; max-width: 300px"
            >
              <el-option v-for="item in memberList" :key="item.loginId" :label="item.name" :value="item.loginId" />
            </el-select>
          </el-form-item>
          <el-form-item label="开始时间" prop="startTime">
            <el-date-picker
              v-model="form.startTime"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              format="yyyy-MM-dd HH:mm:ss"
              placeholder="选择开始时间"
              style="width: 100%; max-width: 300px"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="结束时间" prop="endTime">
            <el-date-picker
              v-model="form.endTime"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              format="yyyy-MM-dd HH:mm:ss"
              placeholder="选择结束时间"
              style="width: 100%; max-width: 300px"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm">提交</el-button>
            <el-button @click="resetForm">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import TopNavigationBar from "@/components/XTopNavigationBar.vue";
import service from "@/router/request";
import { MessageUtil } from "@/utils/utils";

export default {
  components: { TopNavigationBar },
  name: "Announcement",
  data() {
    return {
      form: {
        type: null,
        priority: 1, // 默认为普通
        content: "",
        relatedMembers: [],
        startTime: "",
        endTime: "",
      },
      rules: {
        type: [{ required: true, message: "请选择通告类型", trigger: "change" }],
        priority: [{ required: true, message: "请选择重要性", trigger: "change" }],
        content: [{ required: true, message: "请输入通告内容", trigger: "blur" }],
        startTime: [{ required: true, message: "请选择开始时间", trigger: "change" }],
        endTime: [{ required: true, message: "请选择结束时间", trigger: "change" }],
      },
      announcementTypes: [
        { label: "表彰", value: 1 },
        { label: "公示", value: 2 },
      ],
      priorityOptions: [
        {
          label: "普通",
          value: 1,
          color: "#409EFF",
          icon: "📢",
          description: "常规通告",
        },
        {
          label: "重要",
          value: 2,
          color: "#E6A23C",
          icon: "⚠️",
          description: "需要关注的重要信息",
        },
        {
          label: "紧急",
          value: 3,
          color: "#F56C6C",
          icon: "🚨",
          description: "紧急重要通知",
        },
      ],
      memberLoading: false,
      memberList: [],
    };
  },
  created() {
    this.getMemberList("");
  },
  methods: {
    getMemberList(key) {
      this.memberLoading = true;
      service.post("/Member/getMemberList", { key, pageNum: 1, pageSize: 20 }).then((res) => {
        if (res.status == "succeed") {
          this.memberList = res.data;
          this.memberLoading = false;
        }
      });
    },
    submitForm() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          service
            .post("/Announcement/Add", { ...this.form, relatedMembers: this.form.relatedMembers?.join(",") || "" })
            .then((response) => {
              if (response.status === "succeed") {
                MessageUtil.success("通告提交成功！");
              } else {
                MessageUtil.error("通告提交失败，请稍后重试！");
              }
            });
        } else {
          MessageUtil.error("请完善表单信息！");
          return false;
        }
      });
    },
    resetForm() {
      this.$refs.formRef.resetFields();
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.announcement {
  // min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: clamp(12px, 3vw, 20px);
  flex: 1;

  .top-bar {
    display: flex;
    justify-content: flex-start;
    padding: 20px 0;
    position: relative;
    z-index: 1;

    .el-button {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      backdrop-filter: blur(10px);

      &:hover {
        background: rgba(255, 255, 255, 0.3);
      }
    }
  }

  h2 {
    text-align: center;
    color: white;
    font-size: clamp(1.5rem, 4vw, 2rem);
    margin: 20px 0 30px 0;
    font-weight: 600;
  }
}

.announcement-form {
  max-width: 600px;
  margin: 0 auto;
  background: white;
  padding: clamp(16px, 4vw, 24px);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);

  // 表单项优化
  .el-form-item {
    margin-bottom: 20px;

    .el-form-item__label {
      font-weight: 600;
      color: #333;
      font-size: clamp(14px, 3.5vw, 16px);
    }

    .el-input,
    .el-select,
    .el-date-picker {
      width: 100%;

      .el-input__inner,
      .el-select__input {
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        padding: 12px 16px;
        font-size: clamp(14px, 3.5vw, 16px);
        transition: all 0.3s ease;

        &:focus {
          border-color: #409eff;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }
      }
    }

    .el-textarea {
      .el-textarea__inner {
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        padding: 12px 16px;
        font-size: clamp(14px, 3.5vw, 16px);
        line-height: 1.5;
        resize: vertical;
        min-height: 100px;

        &:focus {
          border-color: #409eff;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }
      }
    }
  }

  // 按钮组优化
  .el-form-item:last-child {
    margin-bottom: 0;
    text-align: center;

    .el-button {
      padding: 12px 24px;
      border-radius: 25px;
      font-size: clamp(14px, 3.5vw, 16px);
      font-weight: 500;
      margin: 0 8px;
      min-width: 100px;

      &.el-button--primary {
        background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
        border: none;

        &:hover {
          background: linear-gradient(135deg, #66b1ff 0%, #409eff 100%);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        }
      }

      &:not(.el-button--primary) {
        background: #f5f5f5;
        border: 1px solid #e0e0e0;
        color: #666;

        &:hover {
          background: #e0e0e0;
          border-color: #d0d0d0;
        }
      }
    }
  }
}

// 移动端优化
@media (max-width: 768px) {
  .announcement {
    padding: 12px;

    .top-bar {
      padding: 16px 0;
    }

    h2 {
      margin: 16px 0 24px 0;
    }
  }

  .announcement-form {
    padding: 16px;
    border-radius: 8px;

    .el-form-item {
      margin-bottom: 16px;

      .el-input__inner,
      .el-select__input,
      .el-textarea__inner {
        padding: 10px 12px;
      }

      .el-textarea__inner {
        min-height: 80px;
      }
    }

    .el-form-item:last-child {
      .el-button {
        padding: 10px 20px;
        margin: 0 4px;
        min-width: 80px;
      }
    }
  }
}

@media (max-width: 480px) {
  .announcement-form {
    .el-form-item:last-child {
      .el-button {
        display: block;
        width: 100%;
        margin: 8px 0;
      }
    }
  }
}
</style>
