<template>
  <div class="material-management">
    <div class="page-header">
      <h2>物品管理</h2>
      <el-button type="primary" @click="showAddDialog">
        <i class="el-icon-plus"></i>
        新增物品
      </el-button>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="search-section">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-input
            v-model="searchText"
            placeholder="搜索物品名称"
            prefix-icon="el-icon-search"
            clearable
            @input="handleSearch"
          ></el-input>
        </el-col>
        <el-col :span="6">
          <el-select v-model="filterStatus" placeholder="状态" clearable @change="handleFilter">
            <el-option label="全部" value=""></el-option>
            <el-option label="启用" value="1"></el-option>
            <el-option label="禁用" value="0"></el-option>
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select v-model="filterUnit" placeholder="单位" clearable @change="handleFilter">
            <el-option label="全部" value=""></el-option>
            <el-option v-for="unit in unitOptions" :key="unit" :label="unit" :value="unit"></el-option>
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button @click="resetFilters">重置</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table
        :data="materialList"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="id" label="ID" width="80" sortable="custom"></el-table-column>
        <el-table-column prop="name" label="物品名称" min-width="150" sortable="custom">
          <template slot-scope="scope">
            <span class="material-name">{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="unit" label="单位" width="100" sortable="custom"></el-table-column>
        <el-table-column prop="remark" label="备注" min-width="200">
          <template slot-scope="scope">
            <span class="remark-text">{{ scope.row.remark || "无备注" }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="isActive" label="状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.isActive ? 'success' : 'danger'">
              {{ scope.row.isActive ? "启用" : "禁用" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" sortable="custom">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" width="180" sortable="custom">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.updateTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" @click="editMaterial(scope.row)">编辑</el-button>
            <el-button size="mini" :type="scope.row.isActive ? 'warning' : 'success'" @click="toggleStatus(scope.row)">
              {{ scope.row.isActive ? "禁用" : "启用" }}
            </el-button>
            <el-button size="mini" type="danger" @click="deleteMaterial(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        ></el-pagination>
      </div>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      :close-on-click-modal="false"
      @close="resetForm"
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="物品名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入物品名称" maxlength="100"></el-input>
        </el-form-item>

        <el-form-item label="单位" prop="unit">
          <el-select v-model="form.unit" placeholder="请选择单位" style="width: 100%" allow-create filterable>
            <el-option v-for="unit in unitOptions" :key="unit" :label="unit" :value="unit"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            type="textarea"
            v-model="form.remark"
            placeholder="请输入备注信息（可选）"
            :rows="4"
            maxlength="500"
            show-word-limit
          ></el-input>
        </el-form-item>

        <el-form-item label="状态" prop="isActive">
          <el-radio-group v-model="form.isActive">
            <el-radio :label="true">启用</el-radio>
            <el-radio :label="false">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          {{ isEdit ? "更新" : "创建" }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import service from "@/router/request";
import { MessageUtil } from "@/utils/utils";

export default {
  name: "MaterialManagement",
  data() {
    return {
      loading: false,
      submitting: false,
      searchText: "",
      filterStatus: "",
      filterUnit: "",
      materialList: [],

      // 分页
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },

      // 排序
      sortField: "",
      sortOrder: "",

      // 对话框
      dialogVisible: false,
      isEdit: false,
      form: {
        id: null,
        name: "",
        unit: "",
        remark: "",
        isActive: true,
      },

      // 表单验证
      rules: {
        name: [
          { required: true, message: "请输入物品名称", trigger: "blur" },
          { min: 2, max: 100, message: "物品名称长度在2-100个字符", trigger: "blur" },
        ],
        unit: [{ required: true, message: "请选择单位", trigger: "change" }],
        isActive: [{ required: true, message: "请选择状态", trigger: "change" }],
      },

      // 单位选项
      unitOptions: ["个", "件", "套", "盒", "包", "箱", "米", "公斤", "升", "张", "支", "台", "部", "本", "卷"],

      searchTimer: null,
    };
  },

  computed: {
    dialogTitle() {
      return this.isEdit ? "编辑物品" : "新增物品";
    },
  },

  mounted() {
    this.loadMaterialList();
  },

  beforeDestroy() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
  },

  methods: {
    // 加载物品列表
    async loadMaterialList() {
      this.loading = true;
      try {
        const params = {
          page: this.pagination.currentPage,
          pageSize: this.pagination.pageSize,
          search: this.searchText,
          status: this.filterStatus,
          unit: this.filterUnit,
          sortField: this.sortField,
          sortOrder: this.sortOrder,
        };

        // 移除空参数
        Object.keys(params).forEach((key) => {
          if (params[key] === "" || params[key] === null || params[key] === undefined) {
            delete params[key];
          }
        });

        const response = await service.get("/Material/GetMaterialList", { params });
        if (response.status === "succeed") {
          this.materialList = response.data.items || [];
          this.pagination.total = response.data.total || 0;
        } else {
          MessageUtil.error("获取物品列表失败");
        }
      } catch (error) {
        console.error("获取物品列表失败:", error);
        MessageUtil.error("获取物品列表失败");
      } finally {
        this.loading = false;
      }
    },

    // 搜索处理
    handleSearch() {
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }

      this.searchTimer = setTimeout(() => {
        this.pagination.currentPage = 1;
        this.loadMaterialList();
      }, 300);
    },

    // 筛选处理
    handleFilter() {
      this.pagination.currentPage = 1;
      this.loadMaterialList();
    },

    // 重置筛选
    resetFilters() {
      this.searchText = "";
      this.filterStatus = "";
      this.filterUnit = "";
      this.pagination.currentPage = 1;
      this.loadMaterialList();
    },

    // 排序处理
    handleSortChange({ column, prop, order }) {
      this.sortField = prop;
      this.sortOrder = order === "ascending" ? "asc" : order === "descending" ? "desc" : "";
      this.loadMaterialList();
    },

    // 分页大小变化
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.pagination.currentPage = 1;
      this.loadMaterialList();
    },

    // 当前页变化
    handleCurrentChange(val) {
      this.pagination.currentPage = val;
      this.loadMaterialList();
    },

    // 显示新增对话框
    showAddDialog() {
      this.isEdit = false;
      this.resetForm();
      this.dialogVisible = true;
    },

    // 编辑物品
    editMaterial(row) {
      this.isEdit = true;
      this.form = {
        id: row.id,
        name: row.name,
        unit: row.unit,
        remark: row.remark || "",
        isActive: row.isActive,
      };
      this.dialogVisible = true;
    },

    // 切换状态
    toggleStatus(row) {
      const action = row.isActive ? "禁用" : "启用";
      this.$confirm(`确定要${action}物品"${row.name}"吗？`, "确认操作", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          try {
            const response = await service.post("/Material/ToggleStatus", {
              id: row.id,
              isActive: !row.isActive,
            });

            if (response.status === "succeed") {
              MessageUtil.success(`${action}成功！`);
              this.loadMaterialList();
            } else {
              MessageUtil.error(`${action}失败，请稍后重试！`);
            }
          } catch (error) {
            console.error(`${action}失败:`, error);
            MessageUtil.error(`${action}失败，请稍后重试！`);
          }
        })
        .catch(() => {
          // 用户取消操作
        });
    },

    // 删除物品
    deleteMaterial(row) {
      this.$confirm(`确定要删除物品"${row.name}"吗？删除后不可恢复！`, "确认删除", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          try {
            const response = await service.post("/Material/Delete", { id: row.id });
            if (response.status === "succeed") {
              MessageUtil.success("删除成功！");
              this.loadMaterialList();
            } else {
              MessageUtil.error("删除失败，请稍后重试！");
            }
          } catch (error) {
            console.error("删除失败:", error);
            MessageUtil.error("删除失败，请稍后重试！");
          }
        })
        .catch(() => {
          // 用户取消删除
        });
    },

    // 提交表单
    submitForm() {
      this.$refs.formRef.validate(async (valid) => {
        if (valid) {
          this.submitting = true;
          try {
            const url = this.isEdit ? "/Material/Update" : "/Material/Create";
            const response = await service.post(url, this.form);

            if (response.status === "succeed") {
              MessageUtil.success(`${this.isEdit ? "更新" : "创建"}成功！`);
              this.dialogVisible = false;
              this.loadMaterialList();
            } else {
              MessageUtil.error(`${this.isEdit ? "更新" : "创建"}失败，请稍后重试！`);
            }
          } catch (error) {
            console.error(`${this.isEdit ? "更新" : "创建"}失败:`, error);
            MessageUtil.error(`${this.isEdit ? "更新" : "创建"}失败，请稍后重试！`);
          } finally {
            this.submitting = false;
          }
        } else {
          MessageUtil.error("请完善表单信息！");
        }
      });
    },

    // 重置表单
    resetForm() {
      this.form = {
        id: null,
        name: "",
        unit: "",
        remark: "",
        isActive: true,
      };

      if (this.$refs.formRef) {
        this.$refs.formRef.resetFields();
      }
    },

    // 格式化日期时间
    formatDateTime(dateString) {
      if (!dateString) return "";
      const date = new Date(dateString);
      return date.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.material-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  h2 {
    margin: 0;
    color: #303133;
    font-size: 24px;
    font-weight: 600;
  }
}

.search-section {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .el-table {
    .material-name {
      font-weight: 600;
      color: #303133;
    }

    .remark-text {
      color: #606266;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      line-height: 1.4;
    }
  }
}

.pagination-section {
  padding: 20px;
  text-align: right;
  border-top: 1px solid #ebeef5;
}

.dialog-footer {
  text-align: center;
}

/* 表格行悬停效果 */
.el-table {
  ::v-deep .el-table__row {
    &:hover {
      background-color: #f5f7fa;
    }
  }
}

/* 状态标签样式优化 */
.el-tag {
  font-weight: 500;
}

/* 操作按钮样式 */
.el-button--mini {
  padding: 5px 10px;
  font-size: 12px;
}

/* 表单样式优化 */
.el-form {
  .el-form-item {
    margin-bottom: 22px;
  }

  .el-textarea {
    .el-textarea__inner {
      resize: vertical;
      min-height: 80px;
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .material-management {
    padding: 15px;
  }

  .page-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;

    h2 {
      font-size: 20px;
    }
  }

  .search-section {
    .el-row {
      .el-col {
        margin-bottom: 10px;
      }
    }
  }
}

@media (max-width: 768px) {
  .material-management {
    padding: 10px;
  }

  .page-header {
    padding: 15px;
  }

  .search-section {
    padding: 15px;
  }

  .table-section {
    overflow-x: auto;
  }

  .pagination-section {
    text-align: center;

    .el-pagination {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
    }
  }
}

/* 加载状态优化 */
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
}

/* 空状态样式 */
.el-table__empty-block {
  padding: 60px 0;

  .el-table__empty-text {
    color: #909399;
    font-size: 16px;
  }
}

/* 对话框样式优化 */
.el-dialog {
  .el-dialog__header {
    padding: 20px 20px 10px;
    border-bottom: 1px solid #ebeef5;
  }

  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__footer {
    padding: 10px 20px 20px;
    border-top: 1px solid #ebeef5;
  }
}

/* 搜索框样式 */
.el-input {
  .el-input__inner {
    border-radius: 6px;
  }
}

.el-select {
  width: 100%;

  .el-input__inner {
    border-radius: 6px;
  }
}

/* 按钮样式优化 */
.el-button {
  border-radius: 6px;
  font-weight: 500;

  &.el-button--primary {
    background-color: #409eff;
    border-color: #409eff;

    &:hover {
      background-color: #66b1ff;
      border-color: #66b1ff;
    }
  }

  &.el-button--success {
    background-color: #67c23a;
    border-color: #67c23a;

    &:hover {
      background-color: #85ce61;
      border-color: #85ce61;
    }
  }

  &.el-button--warning {
    background-color: #e6a23c;
    border-color: #e6a23c;

    &:hover {
      background-color: #ebb563;
      border-color: #ebb563;
    }
  }

  &.el-button--danger {
    background-color: #f56c6c;
    border-color: #f56c6c;

    &:hover {
      background-color: #f78989;
      border-color: #f78989;
    }
  }
}
</style>
