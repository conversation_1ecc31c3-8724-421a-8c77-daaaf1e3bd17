<template>
  <div>
    <div ref="topBarRef" class="top-bar mb-2">
      <el-button @click="goBack" icon="el-icon-back" circle></el-button>
      <h1 style="flex: 1;">机型内容详情</h1>
      <el-button type="primary" plain @click="Auditthrough">审核通过</el-button>
    </div>
    <el-descriptions ref="descriptionsRef" direction="vertical" class="mb-2" :column="6" border>
      <el-descriptions-item label="机型名称">{{ rowData.mmoname }}</el-descriptions-item>
      <el-descriptions-item label="所属巴组">{{ rowData.grono }}</el-descriptions-item>
      <el-descriptions-item label="机型型号">{{ rowData.mmomodel }}</el-descriptions-item>
      <el-descriptions-item label="当前状态">
        {{
        rowData.mmosflag === 0
        ? "停用"
        : rowData.mmosflag === 1
        ? "整机未审"
        : rowData.mmosflag === 2
        ? "变更未审"
        : rowData.mmosflag === 3
        ? "新增未审"
        : rowData.mmosflag === 4
        ? "正常"
        : ""
        }}
      </el-descriptions-item>
      <el-descriptions-item label="录入姓名">{{ rowData.createName }}</el-descriptions-item>
      <el-descriptions-item label="产品描述">{{ rowData.mmoremarks }}</el-descriptions-item>
    </el-descriptions>
    <el-table
      v-if="tableMaxHeight"
      :data="tableData"
      border
      :header-cell-style="{ textAlign: 'center', backgroundColor: '#c5e3ef' }"
      :max-height="tableMaxHeight"
    >
      <el-table-column prop="mpaname" label="部位" align="center"></el-table-column>
      <el-table-column prop="sasname" label="组件" align="center"></el-table-column>
      <el-table-column prop="ssuname" label="子件" align="center"></el-table-column>
      <el-table-column prop="wprno" label="工序" align="center"></el-table-column>
      <el-table-column prop="manhours" label="工时" align="center"></el-table-column>
      <el-table-column prop="mhprice" label="工时价" align="center"></el-table-column>
      <el-table-column prop="swsflagstatus" label="状态" align="center">
        <template v-slot="scope">
          <span
            :style="{
                color: scope.row.swsflagstatus == '审核' ? 'red' : 'green',
              }"
          >{{ scope.row.swsflagstatus }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="swremarks" label="备注" align="center"></el-table-column>
    </el-table>
  </div>
</template>

<script>
import service from "@/router/request";

export default {
  name: "shenheaudit",
  data() {
    return {
      // 表格数据
      rowData: [],
      tableData: [],
      tableMaxHeight: 0,
    };
  },

  mounted() {
    this.rowData = JSON.parse(localStorage.getItem("rowData"));
    this.getdata();
    this.computeTableMaxHeight();
  },

  computed: {},
  methods: {
    //产品部件信息
    getdata() {
      service
        .post("/Chanpin/Getmpadetail", { mmo_no: this.rowData.mmono, swsflag: '1' })
        .then(res => {
          if (res.status == "succeed") {
            this.tableData = res.data;
          }
        });
    },

    //删除部件信息
    shanchu(row) {
      const cbano = row.cbano; // 获取部件ID
      // 弹出确认弹窗
      this.$confirm("确定要删除该部件吗?该部件数据将会消失！", "提示", {
        type: "warning"
      })
        .then(() => {
          // 用户点击了确定按钮，发送 DELETE 请求到后端接口
          service
            .post("/Chanpin/Deletempadetail", {
              params: {
                // 将部件ID作为参数
                cbano: cbano
              }
              // eslint-disable-next-line no-unused-vars
            })
            .then(res => {
              if (res.status == "succeed") {
                // 请求成功的处理逻辑
                // 根据后端返回的数据，做出相应操作
                console.log("删除部件成功");
                this.getdata();
              }
            })
            .catch(error => {
              // 请求失败的处理逻辑
              // 输出错误信息或进行错误处理
              console.error("删除部件失败:", error);
            });
        })
        .catch(() => {
          // 用户点击了取消按钮
          console.log("取消删除部件操作");
        });
    },

    Auditthrough() {
      service
        .post("/ShenHe/Updateflag?mmono=" + this.rowData.mmono)
        .then(res => {
          if (res.status == "succeed") {
            this.$message.success("审核已通过");
            this.$router.back();
          } else {
            this.$message.error("审核未成功");
          }
        });
    },

    goBack() {
      this.$router.back();
    },

    computeTableMaxHeight() {
      let main = document.getElementById('main');
      this.tableMaxHeight = main.clientHeight - this.$refs.topBarRef.clientHeight - this.$refs.descriptionsRef.$el.clientHeight - 20 - 20 - 40;
    }
  }
};
</script>

<style scoped>
.top-bar {
  display: flex;
  align-items: center;
  text-align: center;
}
.top-bar h1 {
  margin: 0;
}
</style>
