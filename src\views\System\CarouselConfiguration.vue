<template>
  <div class="carousel-configuration">
    <h2>首页轮播图配置</h2>
    <el-button type="primary" @click="openAddDialog" class="add-button">添加</el-button>
    <el-table :data="fileList" style="width: 100%">
      <el-table-column prop="index" label="序号" width="100"></el-table-column>
      <el-table-column prop="name" label="图片名称" align="center" width="300"></el-table-column>
      <el-table-column label="图片预览" align="center">
        <template #default="scope">
          <img :src="scope.row.url" alt="图片" class="preview-image" />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="danger" size="small" @click="handleRemove(scope.$index)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加/编辑图片弹框 -->
    <el-dialog :title="isEditing ? '编辑轮播图' : '添加轮播图'" :visible.sync="addDialogVisible">
      <el-form :model="newItem" :rules="rules" ref="addForm" label-width="80px">
        <el-form-item label="序号" prop="index">
          <el-input v-model="newItem.index" type="number" />
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="newItem.name" />
        </el-form-item>
        <el-form-item label="图片" prop="url">
          <el-upload
            action=""
            ref="upload"
            list-type="picture-card"
            :on-change="handleImageChange"
            :file-list="uploadFileList"
            :auto-upload="false"
          >
            <i class="el-icon-plus"></i>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import service from "@/router/request";

export default {
  name: "CarouselConfiguration",
  data() {
    return {
      fileList: [],
      addDialogVisible: false,
      isEditing: false,
      editIndex: null,
      newItem: {
        id: 0,
        index: null,
        name: "",
        url: "",
      },
      uploadFileList: [],
      rules: {
        index: [{ required: true, message: "请输入序号", trigger: "blur" }],
        name: [{ required: true, message: "请输入名称", trigger: "blur" }],
      },
    };
  },
  created() {
    this.GetCarouselList();
  },
  methods: {
    async GetCarouselList() {
      try {
        const response = await service.get("/Carousel/GetCarouselList");
        this.fileList = response.data.map((item, index) => {
          return {
            ...item,
            url: service.getUri({ url: item.url }).replace("/api", ""),
          };
        });
      } catch (error) {
        this.$message.error("获取配置数据失败！");
      }
    },
    handleImageChange(file, fileList) {
      this.uploadFileList = [fileList[fileList.length - 1]]; // 只保留最新上传的文件
    },
    handleRemove(index) {
      this.$confirm("确定删除该轮播配置项吗？", "提示", {
        type: "warning",
      }).then(() => {
        const item = this.fileList[index];
        service
          .delete(`/Carousel/Delete/${item.id}`)
          .then(() => {
            this.fileList.splice(index, 1);
            this.$message.success("删除成功！");
          })
          .catch(() => {
            this.$message.error("删除失败！");
          });
      });
    },
    handleEdit(row) {
      this.isEditing = true;
      this.addDialogVisible = true;
      this.newItem = { ...row };
      this.editIndex = this.fileList.findIndex((item) => item.id === row.id);
      this.uploadFileList = [
        {
          name: row.name,
          url: row.url,
        },
      ];

      this.$nextTick(() => {
        this.$refs.addForm.clearValidate();
      });
    },
    openAddDialog() {
      this.isEditing = false;
      this.addDialogVisible = true;
      this.newItem = { id: 0, index: null, name: "", url: "" };
      this.uploadFileList = [];

      this.$nextTick(() => {
        this.$refs.addForm.resetFields();
      });
    },
    async submitForm() {
      this.$refs.addForm.validate(async (valid, fields) => {
        if (valid) {
          try {
            if (this.uploadFileList.length > 0 && this.uploadFileList[0].raw) {
              // 如果重新上传了图片，则发起上传图片请求
              const formData = new FormData();
              formData.append("files", this.uploadFileList[0].raw);
              const response = await service.post("/Upload/UploadImage", formData);
              this.newItem.url = response.data[0] || "";
            }

            if (this.isEditing) {
              const uri = service.getUri().replace("/api", "");
              // 编辑模式，调用更新接口
              const updateRes = await service.post(`/Carousel/Update`, {
                ...this.newItem,
                url: this.newItem.url.replace(uri, ""),
              });
              if (updateRes.status != "succeed") {
                this.$message.error("更新失败！");
                return;
              }
            } else {
              // 添加模式，调用新增接口
              const addResponse = await service.post("/Carousel/Add", this.newItem);
              if (addResponse.status != "succeed") {
                this.$message.error("添加失败！");
                return;
              }
            }
            this.GetCarouselList();
            this.addDialogVisible = false;
            this.$message.success(this.isEditing ? "配置已更新！" : "图片上传并配置成功！");
          } catch (error) {
            this.$message.error("图片上传或保存失败！");
          }
        } else {
          this.$message.error("请填写完整信息！");
        }
      });
    },
  },
};
</script>

<style scoped>
.carousel-configuration {
  padding: 20px;
  background-color: #f9f9f9;
  min-height: 100vh;
}

h2 {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 20px;
  text-align: center;
  color: #333;
}

.add-button {
  margin-bottom: 20px;
}

.el-table {
  margin-bottom: 20px;
}

.preview-image {
  /* width: 80px; */
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.el-button {
  margin-right: 10px;
}

.dialog-footer {
  text-align: right;
}

.el-upload {
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  padding: 10px;
  background-color: #fafafa;
}

.el-upload:hover {
  border-color: #409eff;
}

.el-upload i {
  font-size: 28px;
  color: #8c8c8c;
}

.el-upload .el-icon-plus {
  font-size: 28px;
  color: #8c8c8c;
}

.el-upload-list {
  margin-top: 10px;
}

.el-form-item {
  margin-bottom: 20px;
}
</style>
