using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Dapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace TaskManagement.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Roles = "Admin,Manager")]
    public class MaterialController : ControllerBase
    {
        private readonly ILogger<MaterialController> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _connectionString;

        public MaterialController(ILogger<MaterialController> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
            _connectionString = _configuration.GetConnectionString("DefaultConnection");
        }

        // 获取物品列表（分页）
        [HttpGet("GetMaterialList")]
        public async Task<IActionResult> GetMaterialList([FromQuery] MaterialQueryModel query)
        {
            try
            {
                using (IDbConnection db = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@Search", query.Search);
                    parameters.Add("@Type", query.Type);
                    parameters.Add("@Status", query.Status);
                    parameters.Add("@Page", query.Page ?? 1);
                    parameters.Add("@PageSize", query.PageSize ?? 20);
                    parameters.Add("@SortField", query.SortField ?? "Id");
                    parameters.Add("@SortOrder", query.SortOrder ?? "ASC");

                    using (var multi = await db.QueryMultipleAsync("sp_GetMaterialList", parameters, commandType: CommandType.StoredProcedure))
                    {
                        var items = await multi.ReadAsync<MaterialViewModel>();
                        var totalCount = await multi.ReadSingleAsync<int>();

                        var result = new
                        {
                            items = items,
                            total = totalCount
                        };

                        return Ok(new { status = "succeed", data = result });
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取物品列表失败");
                return StatusCode(500, new { status = "failed", message = "服务器内部错误" });
            }
        }

        // 获取简单物品列表（用于下拉选择）
        [HttpGet("GetMaterialList")]
        [AllowAnonymous]
        public async Task<IActionResult> GetSimpleMaterialList()
        {
            try
            {
                using (IDbConnection db = new SqlConnection(_connectionString))
                {
                    string sql = @"
                        SELECT Id, Name, Type, Unit, Remark
                        FROM Materials
                        WHERE IsActive = 1
                        ORDER BY Type, Name";

                    var result = await db.QueryAsync<SimpleMaterialModel>(sql);

                    return Ok(new { status = "succeed", data = result });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取物品列表失败");
                return StatusCode(500, new { status = "failed", message = "服务器内部错误" });
            }
        }

        // 创建物品
        [HttpPost("Create")]
        public async Task<IActionResult> Create([FromBody] MaterialCreateModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new { status = "failed", message = "请求数据无效" });
                }

                using (IDbConnection db = new SqlConnection(_connectionString))
                {
                    // 检查物品名称是否已存在
                    string checkSql = "SELECT COUNT(*) FROM Materials WHERE Name = @Name AND IsActive = 1";
                    int count = await db.QuerySingleAsync<int>(checkSql, new { model.Name });

                    if (count > 0)
                    {
                        return BadRequest(new { status = "failed", message = "物品名称已存在" });
                    }

                    string sql = @"
                        INSERT INTO Materials (Name, Type, Unit, Remark, IsActive, CreateTime, UpdateTime)
                        VALUES (@Name, @Type, @Unit, @Remark, @IsActive, GETDATE(), GETDATE());
                        SELECT CAST(SCOPE_IDENTITY() as int)";

                    int id = await db.QuerySingleAsync<int>(sql, model);

                    return Ok(new { status = "succeed", data = id });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建物品失败");
                return StatusCode(500, new { status = "failed", message = "服务器内部错误" });
            }
        }

        // 更新物品
        [HttpPost("Update")]
        public async Task<IActionResult> Update([FromBody] MaterialUpdateModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new { status = "failed", message = "请求数据无效" });
                }

                using (IDbConnection db = new SqlConnection(_connectionString))
                {
                    // 检查物品是否存在
                    string checkSql = "SELECT COUNT(*) FROM Materials WHERE Id = @Id";
                    int count = await db.QuerySingleAsync<int>(checkSql, new { model.Id });

                    if (count == 0)
                    {
                        return NotFound(new { status = "failed", message = "物品不存在" });
                    }

                    // 检查物品名称是否与其他物品重复
                    string duplicateCheckSql = "SELECT COUNT(*) FROM Materials WHERE Name = @Name AND Id != @Id AND IsActive = 1";
                    int duplicateCount = await db.QuerySingleAsync<int>(duplicateCheckSql, new { model.Name, model.Id });

                    if (duplicateCount > 0)
                    {
                        return BadRequest(new { status = "failed", message = "物品名称已存在" });
                    }

                    string sql = @"
                        UPDATE Materials
                        SET Name = @Name,
                            Type = @Type,
                            Unit = @Unit,
                            Remark = @Remark,
                            IsActive = @IsActive,
                            UpdateTime = GETDATE()
                        WHERE Id = @Id";

                    await db.ExecuteAsync(sql, model);

                    return Ok(new { status = "succeed" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新物品失败");
                return StatusCode(500, new { status = "failed", message = "服务器内部错误" });
            }
        }

        // 切换物品状态
        [HttpPost("ToggleStatus")]
        public async Task<IActionResult> ToggleStatus([FromBody] MaterialToggleStatusModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new { status = "failed", message = "请求数据无效" });
                }

                using (IDbConnection db = new SqlConnection(_connectionString))
                {
                    string sql = @"
                        UPDATE Materials
                        SET IsActive = @IsActive,
                            UpdateTime = GETDATE()
                        WHERE Id = @Id";

                    await db.ExecuteAsync(sql, model);

                    return Ok(new { status = "succeed" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "切换物品状态失败");
                return StatusCode(500, new { status = "failed", message = "服务器内部错误" });
            }
        }

        // 删除物品
        [HttpPost("Delete")]
        public async Task<IActionResult> Delete([FromBody] MaterialDeleteModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new { status = "failed", message = "请求数据无效" });
                }

                using (IDbConnection db = new SqlConnection(_connectionString))
                {
                    // 检查是否有相关的领用记录
                    string checkSql = "SELECT COUNT(*) FROM MaterialRequisitions WHERE MaterialId = @Id";
                    int count = await db.QuerySingleAsync<int>(checkSql, new { model.Id });

                    if (count > 0)
                    {
                        return BadRequest(new { status = "failed", message = "该物品存在领用记录，无法删除" });
                    }

                    string sql = "DELETE FROM Materials WHERE Id = @Id";
                    await db.ExecuteAsync(sql, new { model.Id });

                    return Ok(new { status = "succeed" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除物品失败");
                return StatusCode(500, new { status = "failed", message = "服务器内部错误" });
            }
        }
    }

    // 物品查询模型
    public class MaterialQueryModel
    {
        public string Search { get; set; }
        public int? Type { get; set; }
        public bool? Status { get; set; }
        public int? Page { get; set; }
        public int? PageSize { get; set; }
        public string SortField { get; set; }
        public string SortOrder { get; set; }
    }

    // 简单物品模型（用于下拉选择）
    public class SimpleMaterialModel
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public int Type { get; set; }
        public string Unit { get; set; }
        public string Remark { get; set; }
    }

    // 物品视图模型
    public class MaterialViewModel
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public int Type { get; set; }
        public string Unit { get; set; }
        public string Remark { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreateTime { get; set; }
        public DateTime UpdateTime { get; set; }
    }

    // 创建物品模型
    public class MaterialCreateModel
    {
        [Required(ErrorMessage = "物品名称不能为空")]
        [StringLength(100, MinimumLength = 2, ErrorMessage = "物品名称长度在2-100个字符")]
        public string Name { get; set; }

        [Required(ErrorMessage = "物品类型不能为空")]
        [Range(1, 3, ErrorMessage = "物品类型必须为1(劳保用品)、2(工具)或3(文具)")]
        public int Type { get; set; }

        [Required(ErrorMessage = "单位不能为空")]
        [StringLength(20, MinimumLength = 1, ErrorMessage = "单位长度在1-20个字符")]
        public string Unit { get; set; }

        [StringLength(500, ErrorMessage = "备注不能超过500个字符")]
        public string Remark { get; set; }

        public bool IsActive { get; set; } = true;
    }

    // 更新物品模型
    public class MaterialUpdateModel : MaterialCreateModel
    {
        [Required(ErrorMessage = "物品ID不能为空")]
        public int Id { get; set; }
    }

    // 切换状态模型
    public class MaterialToggleStatusModel
    {
        [Required(ErrorMessage = "物品ID不能为空")]
        public int Id { get; set; }

        [Required(ErrorMessage = "状态不能为空")]
        public bool IsActive { get; set; }
    }

    // 删除物品模型
    public class MaterialDeleteModel
    {
        [Required(ErrorMessage = "物品ID不能为空")]
        public int Id { get; set; }
    }
}
