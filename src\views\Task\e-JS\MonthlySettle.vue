<template>
  <div class="home">
    <h1 style="text-align: center">月度结算</h1>
    <div class="mb-2" style="display: flex;justify-content: end;">
      <el-button type="primary" round @click="fromVisible = true">月度结算</el-button>
    </div>
    <el-table :data="tableData" max-height="500" border>
      <el-table-column prop="mmonth" sortable label="结算月份"></el-table-column>
      <el-table-column prop="creator" label="结算工号"></el-table-column>
      <el-table-column prop="creatorName" label="结算人"></el-table-column>
      <el-table-column prop="createtime" sortable label="结算时间"></el-table-column>
    </el-table>
    <el-dialog :visible.sync="fromVisible">
      <el-form :model="form">
        <el-form-item label="结算此月" prop="mmonth">
          <el-date-picker
            v-model="form.mmonth"
            type="month"
            value-format="yyyy-MM"
            :picker-options="pickerOptions"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <el-button @click="fromVisible = false">取 消</el-button>
      <el-button type="primary" @click="savemen">确 定</el-button>
    </el-dialog>
  </div>
</template>

<script>
import service from "@/router/request";
export default {
  name: "monthlysettle",
  data() {
    return {
      tableData: [],
      form: { mmonth: '' },
      fromVisible: false,
      pickerOptions: {
        disabledDate: time => {
          const currentYear = new Date().getFullYear();
          const currentMonth = new Date().getMonth() + 1;
          const selectedYear = time.getFullYear();
          const selectedMonth = time.getMonth() + 1;
          return (
            selectedYear > currentYear ||
            (selectedYear === currentYear && selectedMonth > currentMonth)
          );
        }
      }
    };
  },
  mounted() {
    this.form.mmonth = this.$moment().subtract(1, 'month').format('yyyy-MM')
    this.selectSettlement();
  },
  methods: {
    selectSettlement() {
      service.post("/MonthlySettle/getMonthlySettle").then(res => {
        if (res.status == "succeed") {
          this.tableData = res.data;
        }
      });
    },

    savemen() {
      service
        .post("/MonthlySettle/addMonthlySettle?mmonth=" + this.form.mmonth)
        .then(res => {
          if ((res.status == "succeed")) {
            this.$message.success("结算成功");
            this.selectSettlement();
            this.fromVisible = false;
          } else this.$message.error(res.err);
        });
    }
  }
};
</script>
<style scoped>
.flex-container {
  display: flex;
  justify-content: center;

  height: 100vh;
}
</style>
