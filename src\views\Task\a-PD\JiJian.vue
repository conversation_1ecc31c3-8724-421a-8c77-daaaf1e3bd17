<template>
  <div class="home">
    <div ref="searchBarRef" class="search-bar">
      <div class="search-item">
        <span class="label">查询类别：</span>
        <el-select v-model="value" filterable placeholder="请选择">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </div>
      <el-input v-model="mokey" class="search-item" filterable placeholder="输入机型名称、规格、型号"></el-input>
      <div class="search-item">
        <span class="label">开始日期：</span>
        <el-date-picker
          v-model="start"
          placeholder="开始日期"
          format="yyyy 年 MM 月 dd 日"
          value-format="yyyy-MM-dd"
        ></el-date-picker>
      </div>
      <div class="search-item">
        <span class="label">结束日期：</span>
        <el-date-picker
          v-model="end"
          placeholder="结束日期"
          format="yyyy 年 MM 月 dd 日"
          value-format="yyyy-MM-dd"
        ></el-date-picker>
      </div>
      <el-button class="search-btn" type="primary" @click="search">搜索</el-button>
    </div>

    <el-table v-if="maxHeight" :data="currentPageData" border :max-height="maxHeight">
      <el-table-column prop="planno" label="计划单号" fixed width="200"></el-table-column>
      <el-table-column prop="planneddate" label="计划日期" min-width="160"></el-table-column>
      <el-table-column prop="billtypen" label="单据类型" width="100"></el-table-column>
      <el-table-column prop="mmono" label="有无资料" width="130">
        <template v-slot="scope">
          <span
            :style="{ color: scope.row.mmono == 0 ? 'red' : 'green' }"
            @click="onCellClick(scope.row, 'mmono', $event)"
            >{{ scope.row.mmono == 0 ? "无资料" : scope.row.mmono }}</span
          >
        </template>
      </el-table-column>
      <el-table-column prop="tasno" label="有无派单" width="130">
        <template v-slot="scope">
          <span
            :style="{ color: scope.row.tasno == 0 ? 'red' : 'green' }"
            @click="onCellClick(scope.row, 'tagno', $event)"
            >{{ scope.row.tasno == 0 ? "无派单" : scope.row.tasno }}</span
          >
        </template>
      </el-table-column>
      <el-table-column prop="prono" label="机型编码" min-width="130"></el-table-column>
      <el-table-column prop="proname" label="机型名称" width="180"></el-table-column>
      <el-table-column prop="prospecs" label="机型规格" width="150" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="mmomodel" label="机型型号" width="80"></el-table-column>
      <el-table-column prop="counit" label="单位" width="40"></el-table-column>
      <el-table-column prop="plannedqty" label="计划数量" width="40"></el-table-column>
      <el-table-column prop="sorderno" label="销售单号" width="110"></el-table-column>
      <el-table-column prop="remarks" label="备注" width="150" :show-overflow-tooltip="true"></el-table-column>
      <el-table-column prop="adstatus" label="计划状态" width="80"></el-table-column>
      <el-table-column prop="apno" label="装配计划单号" width="130"></el-table-column>
      <el-table-column prop="apcdate" label="装配计划创建日期" min-width="160"></el-table-column>
      <el-table-column prop="apdmdate" label="装配计划需求日期" min-width="160"></el-table-column>
      <el-table-column prop="adstatusn" label="装配状态" width="70"></el-table-column>
      <el-table-column prop="apcloseflag" label="装配结案" width="40"></el-table-column>
      <el-table-column prop="apwhqty" label="已入库数" width="40"></el-table-column>
      <el-table-column prop="apunwhqty" label="未入库数" width="40"></el-table-column>
      <el-table-column
        prop="apremarks"
        label="装配计划备注"
        width="150"
        :show-overflow-tooltip="true"
      ></el-table-column>
      <el-table-column prop="grohmname" label="巴组管理" width="70"></el-table-column>
      <el-table-column fixed="right" prop="groname" label="指派巴组" width="150"></el-table-column>
    </el-table>
    <el-pagination
      ref="paginationRef"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="[5, 10, 20, 40]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="totalData"
    ></el-pagination>
  </div>
</template>
<script>
import service from "@/router/request";

export default {
  name: "jijian",
  data() {
    const currentDate = new Date();
    const end = new Date(currentDate); // 设置当前日期为 end

    // 将 end 的月份减去 6
    end.setMonth(end.getMonth() - 12);

    return {
      start: end, // 设置 start 为 end 的前 6 个月
      end: currentDate, // 设置当前日期为 end
      options: [
        {
          value: "已审核",
          label: "已审核",
        },
        {
          value: "未审核",
          label: "未审核",
        },
        {
          value: "有资料",
          label: "有资料",
        },
        {
          value: "无资料",
          label: "无资料",
        },
        {
          value: "已派单",
          label: "已派单",
        },
        {
          value: "未派单",
          label: "未派单",
        },
        {
          value: "全部",
          label: "全部",
        },
      ],
      value: "全部",
      tableData: [],
      totalData: 0,
      currentPage: 1, //初始页
      pageSize: 10, //    每页的数据
      mokey: "",
      maxHeight: 0,
    };
  },

  computed: {
    // 根据当前页码和每页显示条数，计算出当前页要显示的数据
    currentPageData() {
      const startIndex = (this.currentPage - 1) * this.pageSize;
      const endIndex = this.currentPage * this.pageSize;
      return this.tableData.slice(startIndex, endIndex);
    },
  },

  mounted() {
    this.selectplan();
    this.computeMaxHeight();
  },

  methods: {
    handleSizeChange(size) {
      // 每页显示条数改变时，重新设置当前页码，并进行相应的处理
      this.pageSize = size;
      this.currentPage = 1;
    },
    handleCurrentChange(page) {
      // 当前页码改变时，重新设置当前页码，并进行相应的处理
      this.currentPage = page;
    },
    search() {
      this.currentPage = 1;
      this.selectplan();
    },
    //查询所有计划
    selectplan() {
      const loading = this.$loading({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      service
        .get("/PlanCheck/GetPlanItems", {
          params: {
            type: this.value,
            startdate: this.start,
            enddate: this.end,
            value: this.mokey,
          },
        })
        .then((res) => {
          if (res.status == "succeed") {
            this.tableData = res.data;
            this.totalData = res.data.length;
          }
        })
        .finally(() => {
          loading.close();
        });
    },

    // eslint-disable-next-line no-unused-vars
    onCellClick(row, columnName, event) {
      if (columnName === "mmono") {
        // 点击mmono单元格触发的操作
        const mmono = row.mmono;
        console.log(mmono);
        // 发送查询请求...
        if (mmono && mmono !== "0") {
          this.$router.push({ path: "/JijianZl", query: { mmono: mmono } });
        }
      } else if (columnName === "tagno") {
        // 点击tasno单元格触发的操作
        // eslint-disable-next-line no-unused-vars
        const tasno = row.tasno;
        // 发送查询请求...
        if (tasno && tasno !== "0") {
          this.$router.push({
            path: "/JijianPd",
            query: { rowData: encodeURIComponent(JSON.stringify(row)) },
          });
        } else this.$message.warning("当前机型暂未指派巴组！");
      }
    },

    computeMaxHeight() {
      let mainEl = document.getElementById("main");
      this.maxHeight =
        mainEl.clientHeight -
        this.$refs.searchBarRef.clientHeight -
        this.$refs.paginationRef.$el.clientHeight -
        20 -
        40;
    },
  },
};
</script>
<style>
.footer {
  z-index: 500;
  position: fixed;
  bottom: 0;
  width: 100%;
  --footer-height: 5px;
  line-height: var(--footer-height);
  color: #fff;
}
</style>
