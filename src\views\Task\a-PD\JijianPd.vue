<template>
  <div>
    <div class="mb-2" style="display: flex; justify-content: space-between; align-items: center">
      <el-button @click="goBack" icon="el-icon-back" circle></el-button>
      计件任务派工单
      <el-button type="primary" @click="debounce(submit, 500)()">提交</el-button>
    </div>

    <el-descriptions class="mb-2" :column="5" border>
      <el-descriptions-item label="任务编号">{{ pdData.tasno }}</el-descriptions-item>
      <el-descriptions-item label="计划工时">{{ pdData.pwhours }}</el-descriptions-item>
      <el-descriptions-item label="计划工资">{{ pdData.pwages }}</el-descriptions-item>
      <el-descriptions-item label="任务创建">{{ pdData.tascreater }}</el-descriptions-item>
      <el-descriptions-item label="创建时间">{{ pdData.tascreatedtime }}</el-descriptions-item>

      <el-descriptions-item label="机型名称">{{ btData.proname }}</el-descriptions-item>
      <el-descriptions-item label="机型型号">{{ btData.mmomodel }}</el-descriptions-item>
      <el-descriptions-item label="工程编码">{{ btData.prono }}</el-descriptions-item>

      <el-descriptions-item label="装配工单">{{ btData.apno }}</el-descriptions-item>
      <el-descriptions-item label="装配交期">{{ btData.apdmdate }}</el-descriptions-item>
      <el-descriptions-item label="装配备注">{{ btData.apremarks }}</el-descriptions-item>
      <!-- <el-descriptions-item label="派单交期">{{
        pdData.deadline
      }}</el-descriptions-item> -->

      <!-- <el-descriptions-item label="任务描述">{{
        pdData.tascontents
      }}</el-descriptions-item>
      <el-descriptions-item label="派单备注">{{
        pdData.tasremarks
      }}</el-descriptions-item> -->
    </el-descriptions>

    <div class="mb-2" style="text-align: center">
      <el-button icon="el-icon-plus" type="success" circle @click="handleAdd"></el-button>
      <el-button type="danger" icon="el-icon-minus" circle @click="deleteSelectedRows"></el-button>
    </div>

    <div>
      <el-table
        :row-class-name="tableRowClassName"
        :data="tableData"
        border
        @selection-change="handleSelectionChange2"
        :header-cell-style="{ textAlign: 'center', backgroundColor: '#c5e3ef' }"
        max-height="460"
      >
        <el-table-column type="selection" align="center"></el-table-column>
        <el-table-column type="index" label="序号" align="center" width="60"></el-table-column>
        <el-table-column prop="mpaname" label="部位" align="center"></el-table-column>
        <el-table-column prop="sasname" label="组件" align="center"></el-table-column>
        <el-table-column prop="ssuname" label="子件" align="center"></el-table-column>
        <el-table-column prop="wprno" label="工序" align="center"></el-table-column>
        <el-table-column prop="manhours" label="工时" align="center" min-width="60"></el-table-column>
        <el-table-column prop="mhprice" label="工时价" align="center"></el-table-column>
        <el-table-column prop="tasqty" label="数量" align="center">
          <template v-slot="scope">
            <el-input v-model="scope.row.tasqty">{{ scope.row.tasqty }}</el-input>
          </template>
        </el-table-column>
        <el-table-column prop="taswage" label="工价" align="center">
          <template v-slot="scope">{{
            (scope.row.taswage = scope.row.manhours * scope.row.mhprice * scope.row.tasqty)
          }}</template>
        </el-table-column>
        <el-table-column prop="tmename" label="装配分队" align="center" width="120">
          <template v-slot="scope">
            <el-link @click="teamClick(scope.row)" type="primary">{{ scope.row.tmename || "无" }}</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="tasdremarks" label="任务备注" align="center" type="textarea" width="180">
          <template v-slot="scope">
            <el-input type="textarea" v-model="scope.row.tasdremarks" autosize>{{ scope.row.tasdremarks }}</el-input>
          </template>
        </el-table-column>
        <el-table-column prop="tasdstartdate" label="任务开始日期" align="center" width="170">
          <template v-slot="scope">
            <el-date-picker
              style="width: 140px"
              v-model="scope.row.tasdstartdate"
              value-format="yyyy-MM-dd"
            ></el-date-picker>
          </template>
        </el-table-column>
        <el-table-column prop="tasddeldate" label="任务结束日期" align="center" width="170">
          <template v-slot="scope">
            <el-date-picker
              style="width: 140px"
              v-model="scope.row.tasddeldate"
              value-format="yyyy-MM-dd"
            ></el-date-picker>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-dialog title="信息" :visible.sync="popoverVisible" width="50%" :close-on-click-modal="false" destroy-on-close>
      <div class="mb-2">
        <el-select v-model="memberSearchKey" style="width: 100px" class="mr-1">
          <el-option v-for="item of memberSearchKeys" :key="item.value" v-bind="item"></el-option>
        </el-select>
        <el-input
          v-model="memberSearchValue"
          placeholder="请输入搜索关键字"
          @input="searchMember"
          style="width: auto"
        ></el-input>
      </div>
      <el-table
        :data="filteredDatagro"
        ref="teamTableRef"
        tooltip-effect="dark"
        style="width: auto"
        max-height="300"
        @selection-change="teamSelectionChange"
        @row-click="teamTableRowClick"
      >
        <el-table-column type="selection" width="50" :reserve-selection="true"></el-table-column>
        <el-table-column type="index" label="序号" width="55"></el-table-column>
        <el-table-column prop="groname" label="团队名称" width="110"></el-table-column>
        <el-table-column prop="supno" label="所属巴组"></el-table-column>
        <el-table-column prop="grohmname" label="队长姓名"></el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="popoverVisible = false">取 消</el-button>
        <el-button type="primary" @click="addDataToCell">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="信息" :visible.sync="fromVisible" width="70%" :close-on-click-modal="false" destroy-on-close>
      <div class="search-bar">
        <div class="search-item">
          <span class="lebel">部件信息：</span>
          <el-input
            v-model="searchText"
            placeholder="请输入部位名或工序"
            @input="handleInput"
            style="width: auto"
          ></el-input>
        </div>
      </div>
      <el-table
        ref="multipleTable"
        :data="filteredData"
        :row-key="getRowKey"
        tooltip-effect="dark"
        style="width: auto"
        max-height="400"
        @selection-change="handleSelectionChange"
        @row-click="memberTableRowClick"
        border
      >
        <el-table-column type="selection" width="40" :reserve-selection="true"></el-table-column>
        <el-table-column prop="mpaname" label="部位" width="110"></el-table-column>
        <el-table-column prop="sasname" label="子件" width="110"></el-table-column>
        <el-table-column prop="ssuname" label="组件" width="110"></el-table-column>
        <el-table-column prop="wprno" label="工序" width="110"></el-table-column>
        <el-table-column prop="manhours" label="工时" width="60"></el-table-column>
        <el-table-column prop="mhprice" label="工时价" width="70"></el-table-column>
        <el-table-column prop="swremarks" label="备注"></el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="fromVisible = false">取 消</el-button>
        <el-button type="primary" @click="savemen">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import service from "@/router/request";
import { debounce } from "@/utils/utils";

export default {
  name: "jijianpd",
  data() {
    return {
      popoverVisible: false,
      popoverData: {},

      //计件存储部件
      currentRowData: {},
      currentTeamRowData: {},

      fromVisible: false,
      //表头数据
      btData: {},
      pdData: {},
      //搜索关键字
      searchText: "",
      //搜索出的数据
      filteredData: [],
      filteredDatagro: [],
      //表身人员数据
      tableData: [],
      //选中行的数据
      multipleSelection: [],
      // 选中的行数据
      selectedRows: [],
      restaurants: [],
      memberSearchKey: "groname",
      memberSearchValue: "",
      memberSearchKeys: [
        { label: "队名", value: "groname" },
        { label: "巴组编号", value: "supno" },
        { label: "队长姓名", value: "grohmname" },
      ],
      isCheck: false,
      teamSelection: [],
    };
  },
  watch: {
    // 监听tableData数据的变化
    tableData: {
      handler(newValue, oldValue) {
        this.handleTableDataChange();
      },
      deep: true,
    },
  },

  computed: {
    a: () => b + c,
  },

  mounted() {
    const rowDataString = this.$route.query.rowData;
    if (rowDataString) {
      this.btData = JSON.parse(decodeURIComponent(rowDataString));
      this.isCheck = !!this.$route.query.isCheck;
      this.Getpd(this.btData.tasno);
      this.getMpa();
    }
    this.showPopoverData();
  },

  methods: {
    debounce,
    teamSelectionChange(val) {
      this.teamSelection = val;
    },

    searchMember() {
      this.filteredDatagro = this.popoverData.filter((item) =>
        item[this.memberSearchKey].includes(this.memberSearchValue)
      );
    },

    showPopoverData() {
      // 假设使用axios发送异步请求获取数据
      service
        .get("/Public/GetGroups")
        .then((res) => {
          if (res.status == "succeed") {
            this.popoverData = res.data;
            this.filteredDatagro = res.data;
          }
        })
        .catch((error) => {
          console.error(error);
        });
    },
    //点击分配团队
    teamClick(row) {
      this.popoverVisible = true;
      this.currentRowData = row;
    },
    //给部件分派团队
    addDataToCell(row) {
      if (!this.teamSelection?.length) this.$message.warning("请勾选团队");

      // this.$set(this.currentRowData, "tmename", row.groname);
      // this.$set(this.currentRowData, "tme_no", row.grono);
      this.$set(this.currentRowData, "tmename", this.teamSelection.map((item) => item.groname).join(","));
      this.$set(this.currentRowData, "tme_no", this.teamSelection.map((item) => item.grono).join(","));
      this.popoverVisible = false;
    },

    //监听tableData，改变计划工时和计划工资
    handleTableDataChange() {
      this.updatepwages();
      this.updatepwhours();
    },
    updatepwages() {
      const totalpwages = this.calculateTotal(this.tableData, "taswage");
      this.pdData.pwages = isNaN(totalpwages) ? "" : totalpwages;
    },
    updatepwhours() {
      const totalpwhours = this.calculateTotal(this.tableData, "manhours");
      this.pdData.pwhours = isNaN(totalpwhours) ? "" : totalpwhours;
    },
    calculateTotal(data, prop) {
      return data.reduce((acc, item) => {
        if (!isNaN(parseInt(item[prop], 10))) {
          return acc + parseInt(item[prop], 10);
        } else {
          return acc;
        }
      }, 0);
    },

    Getpd(tasno) {
      service.get("/JiJian/GetAssteam", { params: { tasno } }).then((res) => {
        if (res.status == "succeed") {
          this.pdData = res.data || {};
        }
      });
    },

    //部件明细
    getMpa() {
      service.post("/JiJian/GetAssteamdet?tas=" + this.btData.tasno).then((res) => {
        if (res.status == "succeed") {
          this.tableData = res.data;
          this.tableData.forEach((item) => (item.wprno = item.wprname));
        }
      });
    },

    //存储当前选中行信息.添加
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    //存储当前选中行信息.删除
    handleSelectionChange2(val) {
      this.selectedRows = val;
    },
    //搜索后表格勾选不取消
    getRowKey(row) {
      return row.cbano;
    },
    handleAdd() {
      this.searchText = "";
      (this.multipleSelection = []), // 新增数据的时候清空选中数据
        (this.fromVisible = true); // 打开弹窗
      this.loadAll();
      if (this.$refs.multipleTable) {
        this.$refs.multipleTable.clearSelection();
      }
    },

    //实现表格双击可编辑
    // eslint-disable-next-line no-unused-vars
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex % 4 == 1) {
        return "warning-row";
      } else if (rowIndex % 4 == 3) {
        return "success-row";
      }
      return "";
    },
    //查询出该产品资料所有部件
    loadAll() {
      service
        .post("/Chanpin/Getmpadetail", { mmo_no: this.btData.mmono })
        .then((res) => {
          if (res.status == "succeed") {
            let cbaNos = this.tableData.map((item) => item.cba_no);
            this.restaurants = res.data;
            this.filteredData = this.restaurants.filter((item) => !cbaNos.some((cbano) => item.cbano == cbano));
          }
        })
        .catch((error) => {
          console.error("Error fetching restaurants", error);
        });
    },
    //根据关键字查询成员
    handleInput() {
      let cbaNos = this.tableData.map((item) => item.cba_no);
      this.filteredData = this.restaurants.filter((item) => {
        return (
          !cbaNos.some((cbano) => item.cbano == cbano) &&
          (item.mpaname.toLowerCase().includes(this.searchText.toLowerCase()) ||
            item.wprno.toLowerCase().includes(this.searchText.toLowerCase()))
        );
      });
    },
    //将选中的部件添加到表身
    savemen() {
      // this.multipleSelection.isEdit = false;
      let auditFlag = false;
      this.multipleSelection.forEach((item) => {
        item.tadno = "";
        item.tas_no = this.pdData.tasno;
        if (item.swsflag == "1") {
          auditFlag = true;
          return this.$message.error(item.mpaname + "未审核");
        }
      });
      if (auditFlag) {
        return;
      }
      this.multipleSelection = this.multipleSelection.map((item) => ({
        tadno: item.tadno,
        tas_no: item.tas_no,
        mmo_no: item.mmono,
        cba_no: item.cbano,
        mpaname: item.mpaname,
        sasname: item.sasname,
        ssuname: item.ssuname,
        wprno: item.wprno,
        mhprice: item.mhprice,
        manhours: item.manhours,
        swremarks: item.swremarks,
        swsflag: item.swsflag,
        swsflagstatus: item.swsflagstatus,
        creater: item.creater,
        createdTime: item.createdTime,
        reviewer: item.reviewer,
        reviewTime: item.reviewTime,
        approver: item.approver,
        approvalTime: item.approvalTime,
        createtername: item.createtername,
        reviewername: item.reviewername,
        approvername: item.approvername,
      }));
      this.multipleSelection.forEach((item) => this.$set(item, "tasqty", 1));
      this.tableData = this.tableData.concat(this.multipleSelection);
      (this.multipleSelection = []), (this.fromVisible = false);
    },
    // 删除选中的行数据
    deleteSelectedRows() {
      const idArray = []; // 声明一个空数组用于存储找到的id

      this.selectedRows.forEach((row) => {
        if (row.tadno) {
          // 只有当找到匹配的索引时才将id添加到数组中
          idArray.push(row.tadno);
        }
      });
      let delTableData = () => {
        this.selectedRows.forEach((row) => {
          const index = this.tableData.indexOf(row);
          this.tableData.splice(index, 1);
        });
        this.$message.success("删除成功");
      };
      if (!this.selectedRows || this.selectedRows.length == 0) return this.$message.warning("未选中数据！");
      if (!idArray.length) return delTableData();
      service.post("/TaskHours/getTaskHoursList", { taskNos: idArray }).then((res) => {
        if (res.status == "succeed") {
          this.$confirm(
            `${res.data?.length ? "任务已产生日报，删除任务的同时会删除其所关联日报，" : ""}确定删除所选中任务吗？`,
            "提示",
            {
              confirmButtonText: "确认",
              cancelButtonText: "取消",
              type: "warning",
            }
          )
            .then(() => {
              service.post("/JiJian/Deleteassteamdet", { tad: idArray, tas: this.btData.tasno }).then((delRes) => {
                if (delRes.status == "succeed") {
                  delTableData();
                } else {
                  this.$message.error(delRes.err);
                }
              });
            })
            .catch(() => {});
        } else {
          this.$message.error(res.err);
        }
      });
    },
    //提交页面数据
    submit() {
      // if (this.tableData.some((item) => !item.tmename)) {
      //   this.$message.warning("存在未分配小队的情况");
      //   return;
      // }
      if (!this.tableData.length) return this.$message.warning("请指派任务");
      if (this.tableData.some((item) => !item.tasddeldate)) return this.$message.warning("存在未选择时间的情况");
      const loading = this.$loading({
        lock: true,
        text: "提交中.....",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      service
        .post("/JiJian/updateAssteam", {
          assteam: this.pdData,
          list: this.tableData,
        })
        .then((res) => {
          loading.close();
          if (res.status == "succeed") {
            this.getMpa();
            this.$message.success("添加成功");
          } else {
            this.$message.error(`添加失败：${res.message}`);
          }
        });
    },
    //返回
    goBack() {
      this.$router.back();
    },

    memberTableRowClick(row) {
      this.$refs.multipleTable.toggleRowSelection(row);
    },

    teamTableRowClick(row) {
      this.$refs.teamTableRef.toggleRowSelection(row);
    },
  },
};
</script>

<style scoped></style>
