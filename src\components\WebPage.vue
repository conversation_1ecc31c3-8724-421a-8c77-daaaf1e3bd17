<template>
  <div class="container">
    <XTopNavigationBar :title="title" />
    <iframe :src="src" frameborder="0"></iframe>
  </div>
</template>

<script>
import XTopNavigationBar from "@/components/XTopNavigationBar.vue";

export default {
  name: "WebPage",
  components: { XTopNavigationBar },
  props: {
    src: {
      type: String,
      default: "",
    },
    title: {
      type: String,
      default: "内嵌网页",
    },
  },
};
</script>

<style scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

iframe {
  flex: 1;
  width: 100%;
  height: 100%;
}
</style>
