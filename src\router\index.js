import store from "@/store";
import { isMobile } from "@/utils/utils";
import Vue from "vue";
import VueRouter from "vue-router";

Vue.use(VueRouter);

const routes = [
  //一级路由
  {
    path: "/",
    name: "主页面",
    component: () => import("../views/indexView.vue"),
    redirect: "/default",
    children: [
      {
        path: "/default",
        name: "首页",
        meta: {
          authRequired: true,
        },
        component: () => import("../views/DefaultView.vue"),
      },
      {
        path: "/memberData",
        name: "成员资料",
        meta: { authRequired: true },
        component: () => import("../views/Data/memberData.vue"),
      },
      {
        path: "/bazu",
        name: "巴组资料",
        meta: {
          authRequired: true,
        },
        component: () => import("../views/Data/BaZu.vue"),
      },
      {
        path: "/groupDetail",
        name: "groupDetail",
        meta: {
          authRequired: true,
        },
        component: () => import("../views/Data/GroupDetail.vue"),
      },
      {
        path: "/chanpin",
        name: "产品资料",
        meta: {
          authRequired: true,
        },
        component: () => import("../views/Data/ChanPin.vue"),
      },
      {
        path: "/chanpin1",
        name: "产品资料1",
        meta: {
          authRequired: true,
        },
        component: () => import("../views/Data/ChanPin1.vue"),
      },
      {
        path: "/xinxhou",
        name: "薪酬资料",
        meta: {
          authRequired: true,
        },
        component: () => import("../views/Data/XinChou.vue"),
      },
      {
        path: "/shenhe",
        name: "资料审核",
        meta: {
          authRequired: true,
        },
        component: () => import("../views/Data/ShenHe.vue"),
      },
      {
        path: "/shenheaudit",
        name: "资料审核详情",
        meta: {
          authRequired: true,
        },
        component: () => import("../views/Data/ShenHeAudit.vue"),
      },

      {
        path: "/PlanCheck",
        name: "计划查看",
        meta: {
          authRequired: true,
        },
        component: () => import("../views/Plan/PlanCheck.vue"),
      },

      {
        path: "/JiJian",
        name: "计件任务",
        meta: {
          authRequired: true,
        },
        component: () => import("../views/Task/a-PD/JiJian.vue"),
      },
      {
        path: "/performance",
        name: "绩效任务",
        meta: {
          authRequired: true,
        },
        component: () => import("../views/Task/a-PD/PerformanceTask.vue"),
      },
      {
        path: "/JijianZl",
        name: "计件资料详情",
        meta: {
          authRequired: true,
        },
        component: () => import("../views/Task/a-PD/JijianZl.vue"),
      },
      {
        path: "/JijianPd",
        name: "计件派单详情",
        meta: {
          authRequired: true,
        },
        component: () => import("../views/Task/a-PD/JijianPd.vue"),
      },
      {
        path: "/JiShi",
        name: "计时任务",
        meta: {
          authRequired: true,
        },
        component: () => import("../views/Task/a-PD/JiShi.vue"),
      },
      {
        path: "/LinShi",
        name: "临时任务",
        meta: {
          authRequired: true,
        },
        component: () => import("../views/Task/a-PD/LinShi.vue"),
      },

      {
        path: "/TaskCheck",
        name: "任务工单",
        meta: {
          authRequired: true,
        },
        component: () => import("../views/Task/b-CX/PlanSearch.vue"),
      },
      {
        path: "/taskDetail",
        name: "任务明细",
        meta: {
          authRequired: true,
        },
        component: () => import("../views/Task/b-CX/taskDetails.vue"),
      },
      {
        path: "/MachineTaskOverview",
        name: "机器任务总览",
        meta: {
          authRequired: true,
        },
        component: () => import("../views/Task/b-CX/MachineTaskOverview.vue"),
      },
      {
        path: "/XinTeamAccountZi",
        name: "",
        meta: {
          authRequired: true,
        },
        component: () => import("../views/Task/c-HC/TeamAccount.vue"),
      },
      {
        path: "/PersonalAccount",
        name: "工资结算",
        meta: {
          authRequired: true,
        },
        component: () => import("../views/Task/c-HC/PersonalAccount.vue"),
      },
      {
        path: "/personalWageDetails",
        name: "personalWageDetails",
        meta: {
          authRequired: true,
        },
        component: () => import("../views/Task/c-HC/PersonalWageDetails.vue"),
      },
      {
        path: "/MonthlyAssess",
        name: "月度评估",
        meta: {
          authRequired: true,
        },
        component: () => import("../views/Task/c-HC/MonthlyAssess.vue"),
      },
      {
        path: "/MonthlyAssessDetails",
        name: "monthlyAssessDetails",
        meta: {
          authRequired: true,
        },
        component: () => import("../views/Task/c-HC/MonthlyAssessDetails.vue"),
      },
      {
        path: "/TeamAccount",
        name: "团队核算",
        meta: {
          authRequired: true,
        },
        component: () => import("../views/Task/c-HC/TeamAccount.vue"),
      },
      {
        path: "/teamWageDetails",
        name: "teamWageDetails",
        meta: {
          authRequired: true,
        },
        component: () => import("../views/Task/c-HC/TeamWageDetails.vue"),
      },

      // {
      //   path: "/SalaryReport",
      //   name: "工资报表",
      //   meta: {
      //     authRequired: true,
      //   },
      //   component: () => import("../views/Task/d-TJ/SalaryReport.vue"),
      // },
      // {
      //   path: "/StatisticAnalysis",
      //   name: "统计分析",
      //   meta: {
      //     authRequired: true,
      //   },
      //   component: () => import("../views/Task/d-TJ/StatisticAnalysis.vue"),
      // },
      {
        path: "/PayrollStatistics",
        name: "薪资统计",
        meta: {
          authRequired: true,
        },
        component: () => import("../views/DataStatistic/PayrollStatistics/PayrollStatistics.vue"),
      },
      {
        path: "/MonthlySettle",
        name: "月度结算",
        meta: {
          authRequired: true,
        },
        component: () => import("../views/Task/e-JS/MonthlySettle.vue"),
      },

      {
        path: "/Privilege",
        name: "用户管理",
        meta: {
          authRequired: true,
        },
        component: () => import("../views/System/PrivilegeManage.vue"),
      },
      {
        path: "/role",
        name: "角色管理",
        meta: {
          authRequired: true,
        },
        component: () => import("../views/System/role.vue"),
      },
      {
        path: "/individualHoursSummary",
        name: "个人工时汇总",
        component: () => import("../views/DataStatistic/IndividualHoursSummary/IndividualHoursSummary.vue"),
      },
      {
        path: "/proNoIndividualHoursSummary",
        name: "机器生产数据看板",
        component: () => import("../views/DataStatistic/ProNoIndividualHoursSummary.vue"),
      },
      {
        path: "/individualHoursDetail",
        name: "工时明细",
        component: () => import("../views/DataStatistic/IndividualHoursSummary/HoursDetail.vue"),
      },
      {
        path: "/MoreLink",
        name: "更多链接",
        component: () => import("../views/MoreLink.vue"),
      },
      {
        path: "/CarouselConfiguration",
        name: "轮播配置",
        component: () => import("../views/System/CarouselConfiguration.vue"),
      },
      {
        path: "/LeanImprovement",
        name: "精益改善",
        meta: {
          authRequired: true,
        },
        component: () => import("../views/LeanImprovement/LeanImprovement.vue"),
      },
      {
        path: "/materialManagement",
        name: "物品管理",
        component: () => import("@/views/MaterialManagement/MaterialManagement.vue"),
      },
    ],
  },
  {
    path: "/login",
    name: "登录界面",
    component: () => import("../views/LoginView.vue"),
    meta: {
      authRequired: true,
    },
  },
  //
  { path: "/assemblyDep", name: "总装部", component: () => import("../views/Phone/AssemblyDep/Index.vue") },
  {
    path: "/announcement",
    name: "部门通告",
    component: () => import("../views/Phone/AssemblyDep/Announcement/Index.vue"),
  },
  {
    path: "/announcement",
    name: "公告",
    component: () => import("../views/Phone/AssemblyDep/Announcement/Announcement.vue"),
  },
  {
    path: "/addAnnouncement",
    name: "新增通告",
    component: () => import("../views/Phone/AssemblyDep/Announcement/Announcement.vue"),
  },
  {
    path: "/announcementList",
    name: "通告查看",
    component: () => import("../views/Phone/AssemblyDep/Announcement/AnnouncementList.vue"),
  },
  {
    path: "/announcementStats",
    name: "统计",
    component: () => import("../views/Phone/AssemblyDep/Announcement/AnnouncementStats.vue"),
  },
  {
    path: "/salarySheet",
    name: "工资条",
    component: () => import("../views/Phone/AssemblyDep/SalarySheet/SalarySheet.vue"),
  },
  {
    path: "/trainingCenter",
    name: "培训中心",
    component: () => import("../views/Phone/AssemblyDep/TrainingCenter/TrainingCenter.vue"),
  },
  {
    path: "/trainingMeeting",
    name: "培训会议",
    component: () => import("../views/Phone/AssemblyDep/TrainingCenter/TrainingMeeting/TrainingMeeting.vue"),
  },
  {
    path: "/trainingMaterials",
    name: "培训资料",
    component: () => import("../views/Phone/AssemblyDep/TrainingCenter/TrainingMaterials/TrainingMaterials.vue"),
  },
  {
    path: "/leanImprovementApp",
    name: "精益改善",
    component: () => import("../views/Phone/AssemblyDep/LeanImprovement/Index.vue"),
  },
  {
    path: "/improvementProposalPlatform",
    name: "全名改善提案平台",
    component: () =>
      import("../views/Phone/AssemblyDep/LeanImprovement/ImprovementProposalPlatform/ImprovementProposalPlatform.vue"),
  },
  {
    path: "/leanOperationsCommandRoom",
    name: "精益作战指挥室",
    component: () =>
      import("../views/Phone/AssemblyDep/LeanImprovement/LeanOperationsCommandRoom/LeanOperationsCommandRoom.vue"),
  },
  {
    path: "/myProposal",
    name: "我的提案",
    component: () => import("../views/Phone/AssemblyDep/LeanImprovement/MyProposal/MyProposal.vue"),
  },
  {
    path: "/departmentTransfer",
    name: "部门调动",
    component: () => import("../views/Phone/AssemblyDep/DepartmentTransfer/DepartmentTransfer.vue"),
  },
  {
    path: "/permissionTest",
    name: "权限测试",
    component: () => import("../views/Phone/AssemblyDep/PermissionTest.vue"),
  },
  {
    path: "/announcementDemo",
    name: "公告栏设置演示",
    component: () => import("../views/Phone/AssemblyDep/AnnouncementDemo.vue"),
  },
  {
    path: "/attendanceCenter",
    name: "考勤中心",
    component: () => import("../views/Phone/AssemblyDep/AttendanceCenter/AttendanceCenter.vue"),
  },
  {
    path: "/materialRequisition",
    name: "物料领用",
    component: () => import("../views/Phone/AssemblyDep/MaterialRequisition/Index.vue"),
  },
  {
    path: "/createRequisition",
    name: "新建领用单",
    component: () => import("../views/Phone/AssemblyDep/MaterialRequisition/CreateRequisition.vue"),
  },
  {
    path: "/editRequisition/:id",
    name: "编辑领用单",
    component: () => import("../views/Phone/AssemblyDep/MaterialRequisition/EditRequisition.vue"),
  },
  {
    path: "/myRequisitions",
    name: "我的领用单",
    component: () => import("../views/Phone/AssemblyDep/MaterialRequisition/MyRequisitions.vue"),
  },
  {
    path: "/requisitionManagement",
    name: "物料审核管理",
    component: () => import("../views/Phone/AssemblyDep/MaterialRequisition/RequisitionManagement.vue"),
  },
];

if (isMobile())
  routes.push(
    { path: "/dailyWorkReport", name: "工作日报", component: () => import("../views/Phone/Index.vue") },
    { path: "/dailyOverview", name: "日报总览", component: () => import("../views/Phone/DailyOverview.vue") },
    { path: "/groupMembers", name: "成员列表", component: () => import("../views/Phone/GroupMembers.vue") },
    { path: "/reportForDay", name: "每日日报", component: () => import("../views/Phone/ReportForDay.vue") },
    { path: "/entryHours", name: "工时录入", component: () => import("../views/Phone/DailyWorkReport.vue") }
  );
else
  routes[0].children.push(
    { path: "/dailyWorkReport", name: "日报审核", component: () => import("../views/Phone/Index.vue") },
    { path: "/dailyOverview", name: "日报总览", component: () => import("../views/Phone/DailyOverview.vue") },
    { path: "/groupMembers", name: "成员列表", component: () => import("../views/Phone/GroupMembers.vue") },
    { path: "/reportForDay", name: "每日日报", component: () => import("../views/Phone/ReportForDay.vue") },
    { path: "/entryHours", name: "工时录入", component: () => import("../views/Phone/DailyWorkReport.vue") }
  );

// const originalPush = VueRouter.prototype.push;
// VueRouter.prototype.push = function push(location) {
//   return originalPush.call(this, location).catch((err) => err);
// };

const router = new VueRouter({
  mode: "hash",
  // History 模式：需要浏览器支持 HTML5 的 History API，所有现代浏览器都支持，使用正常的 URL 地址，不带有 # 符号。
  // Hash 模式：兼容性较好，支持所有浏览器，包括古老的 IE，使用带有 # 符号和 hash 值的 URL 地址。
  routes,
});

router.beforeEach((to, from, next) => {
  if (to.meta.authRequired) {
    if (!store.state.user?.token && to.path !== "/login") {
      next({ path: "/login" });
    } else if (store.state.user?.token && to.path === "/login") next({ path: "/" });
    else next();
  } else {
    next();
  }
});

export default router;
