<template>
  <div class="home">
    <div ref="searchBarRef" class="search-bar">
      <div class="search-item">
        <span>查询月份：</span>
        <el-date-picker v-model="date" type="month" placeholder="选择月" value-format="yyyy-MM"></el-date-picker>
      </div>
      <div class="search-item">
        <el-input v-model="keyword" placeholder="姓名、工号、队名或巴组" />
      </div>
      <el-button class="search-btn" type="primary" @click="search">搜索</el-button>
      <el-button class="search-btn" type="warning" @click="recalculation">重算</el-button>
      <el-button class="search-btn" @click="exportData()">导出</el-button>
    </div>
    <!-- Table -->
    <el-table v-if="maxHeight" :data="tableData" border max-height="700" :max-height="maxHeight">
      <el-table-column v-for="config of tableConfig" :key="config.prop" v-bind="config"></el-table-column>
      <el-table-column label="操作" fixed="right">
        <template v-slot="scope">
          <el-link type="primary" @click="viewDetails(scope.row)">查看详情</el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      ref="paginationRef"
      style="width: 75%"
      @size-change="pageSizeChange"
      @current-change="pageNumChange"
      :current-page="paging.pageNum"
      :page-sizes="[5, 10, 20, 40]"
      :page-size="paging.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="paging.total"
    ></el-pagination>
  </div>
</template>

<script>
import service from "@/router/request";
import { xlsx } from "@/utils/utils";

export default {
  name: "personalaccount",
  data() {
    return {
      date: "",
      tableData: [],
      tableConfig: [
        { label: "姓名", prop: "memname", "min-width": "70" },
        { label: "工号", prop: "memno", "min-width": "75" },
        { label: "核算月份", prop: "mmonth", "min-width": "80" },
        { label: "所属巴组", prop: "groname" },
        { label: "巴长", prop: "grohmname" },
        { label: "所属团队", prop: "tmename", "min-width": "120" },
        { label: "队长", prop: "tmehmname" },
        { label: "团队任务工时", prop: "teamTaskManHours", "min-width": "70" },
        { label: "团队任务工资", prop: "teamTaskWage", "min-width": "70" },
        { label: "个人任务工时", prop: "individualTaskManHours", "min-width": "70" },
        { label: "个人任务工资", prop: "individualTaskWage", "min-width": "70" },
        { label: "合计工时", prop: "smhours" },
        { label: "合计工资", prop: "smwages" },
        { label: "平均工时价", prop: "pphs", "min-width": "70" },
      ],
      paging: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      keyword: "",
      maxHeight: 0,
    };
  },

  mounted() {
    this.computeMaxHeight();
    this.date = this.$moment().subtract(1, "months").format("yyyy-MM");
    this.getTeamAccount();
  },

  activated() {
    // this.date = this.$moment().subtract(1, "months").format("yyyy-MM");
    this.getTeamAccount();
  },

  methods: {
    //
    getTeamAccount() {
      service
        .get("/PersonalAccount/GetPeopleAccount", {
          params: { fuzzy: this.keyword, mmonth: this.date, ...this.paging },
        })
        .then((res) => {
          if (res.status == "succeed") {
            this.tableData = res.data?.list || [];
            this.paging.total = res.data?.total || [];
            this.tableData.forEach((item) => {
              item.teamTaskManHours = item.tachours;
              item.teamTaskWage = item.tacwages;
              item.individualTaskManHours = item.ttmhours + item.tpthours;
              item.individualTaskWage = item.ttmwages + item.tptwages;
            });
          }
        });
    },

    recalculation() {
      const loading = this.$loading({
        lock: true,
        text: "计算中.....",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      service.post("/PersonalAccount/calculatePersonalSalary", { month: this.date }).then((res) => {
        loading.close();
        if (res.status == "succeed") {
          this.getTeamAccount();
          this.$message.success("计算成功");
        } else {
          this.$message.error("计算失败：", res.err);
        }
      });
    },

    search() {
      this.paging.pageNum = 1;
      this.getTeamAccount();
    },

    pageSizeChange(val) {
      this.paging.pageSize = val;
      this.getTeamAccount();
    },

    pageNumChange(val) {
      this.paging.pageNum = val;
      this.getTeamAccount();
    },

    viewDetails(row) {
      this.$router.push({
        name: "personalWageDetails",
        query: { personalPayroll: encodeURIComponent(JSON.stringify(row)) },
      });
    },

    exportData() {
      service
        .get("/PersonalAccount/GetPeopleAccount", {
          params: { fuzzy: this.keyword, mmonth: this.date, pageNum: 1, pageSize: 9999999 },
        })
        .then((res) => {
          if (res.status == "succeed") {
            if (res.data?.list.length > 0) {
              res.data.list.forEach((item) => {
                item.teamTaskManHours = item.tachours;
                item.teamTaskWage = item.tacwages;
                item.individualTaskManHours = item.ttmhours + item.tpthours;
                item.individualTaskWage = item.ttmwages + item.tptwages;
              });
              let header = {};
              this.tableConfig.forEach((item) => (header[item.prop] = item.label));
              xlsx(res.data.list, header, "个人结算数据");
            } else {
              this.$message.warning("数据为空");
            }
          }
        });
    },

    computeMaxHeight() {
      let mainEl = document.getElementById("main");
      this.maxHeight =
        mainEl.clientHeight -
        this.$refs.searchBarRef.clientHeight -
        this.$refs.paginationRef.$el.clientHeight -
        20 -
        40;
    },
  },
};
</script>

<style>
.search-bar {
  margin-bottom: 20px;
}

.search-bar > div {
  display: flex;
  justify-content: center;
  margin-bottom: 10px;
}

.search-bar .search-item {
  margin-right: 10px;
}
</style>
