<template>
  <div>
    <div class="mb-2 top-bar">
      <el-button v-if="isCheck" @click="$router.back()" icon="el-icon-back" circle></el-button>
      <h3 style="position: absolute">计时任务派单</h3>
      <el-button style="margin-left: auto" type="primary" @click="debounce(submit, 500)()">提交</el-button>
    </div>

    <el-form style="width: 100%" label-width="150px" :model="formInline" :rules="rules" ref="jishiForm">
      <el-row>
        <el-col :span="8">
          <el-form-item label="任务内容">
            <!-- <el-input type="textarea" v-model="formInline.ttmcontent" placeholder="任务内容"></el-input> -->
            <el-select v-model="formInline.ttmcontent" placeholder="请选择任务内容">
              <el-option v-for="opt of contentOptions" :key="opt" :label="opt" :value="opt"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="开始时间" prop="ttmsdate">
            <el-date-picker v-model="formInline.ttmsdate" value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="结束时间" prop="ttmedate">
            <el-date-picker v-model="formInline.ttmedate" value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="合计工时">
            <el-input v-model="formInline.ttmphour" disabled placeholder="合计工时"></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="合计工价">
            <el-input v-model="formInline.ttmpwage" disabled placeholder="合计工价"></el-input>
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item label="备注">
            <el-input
              type="textarea"
              v-model="formInline.ttmremarks"
              placeholder="请输入当前任务备注"
              autosize
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="mb-2" style="text-align: center">
      <el-button icon="el-icon-plus" type="success" circle @click="handleAdd"></el-button>
      <el-button type="danger" icon="el-icon-minus" circle @click="deleteSelectedRows"></el-button>
    </div>

    <el-table
      :row-class-name="tableRowClassName"
      :data="tableDataMap"
      border
      @selection-change="handleSelectionChange2"
      :header-cell-style="{ textAlign: 'center', backgroundColor: '#c5e3ef' }"
    >
      <el-table-column type="selection" width="40"></el-table-column>
      <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
      <el-table-column label="所属巴组" prop="departmentname" align="center"></el-table-column>
      <el-table-column label="工程编码" align="center" width="280">
        <template v-slot="scope">
          <el-select
            v-model="scope.row.ttdprono"
            filterable
            remote
            reserve-keyword
            multiple
            placeholder="请输入工程编码"
            :remote-method="queryDepartments"
            :loading="loading"
            style="width: 250px"
          >
            <el-option v-for="(item, index) of option" :key="item + index" :label="item" :value="item"></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="所属团队" prop="groname" align="center" min-width="100"></el-table-column>
      <el-table-column prop="lastname" label="任务成员" align="center" width="80"></el-table-column>
      <el-table-column prop="loginid" label="工号" align="center" min-width="70"></el-table-column>
      <el-table-column prop="jobtitlename" label="职务" align="center"></el-table-column>
      <el-table-column prop="memrole" label="角色" align="center"></el-table-column>
      <el-table-column prop="ttdhour" label="工时" align="center" min-width="80">
        <template v-slot="scope">
          <el-input v-model="scope.row.ttdhour" @input="updatahour">{{ scope.row.ttdhour }}</el-input>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="houwage" label="工时价" align="center" min-width="40"></el-table-column>
      <el-table-column label="总价" align="center" min-width="60">
        <template v-slot="scope">{{ (scope.row.ttdwage = scope.row.ttdhour * scope.row.houwage) }}</template>
      </el-table-column> -->
      <el-table-column prop="ttdsmtype" label="结算类型" align="center" width="100">
        <template v-slot="scope">
          <el-select v-model="scope.row.ttdsmtype" placeholder="请选择">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="ttdremarks" label="任务备注" align="center" type="textarea" min-width="200">
        <template v-slot="scope">
          <el-input type="textarea" v-model="scope.row.ttdremarks" autosize>{{ scope.row.ttdremarks }}</el-input>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog title="添加人员" :visible.sync="fromVisible" width="80%" :close-on-click-modal="false" destroy-on-close>
      <div class="mb-2" style="text-align: center">
        <el-select v-model="memberSearchKey" @change="searchMember" style="width: 100px" class="mr-1">
          <el-option v-for="item of memberSearchKeys" :key="item.value" v-bind="item"></el-option>
        </el-select>
        <el-input
          v-model="memberSearchValue"
          placeholder="请输入搜索关键字"
          @input="searchMember"
          style="width: auto"
        ></el-input>
      </div>
      <el-table
        ref="multipleTable"
        :data="filteredData"
        :row-key="getRowKey"
        tooltip-effect="dark"
        style="width: auto"
        max-height="400"
        @selection-change="handleSelectionChange"
        @row-click="memberTableRowClick"
      >
        <el-table-column type="selection" width="55" :reserve-selection="true"></el-table-column>
        <el-table-column prop="lastname" label="姓名"></el-table-column>
        <el-table-column prop="loginid" label="工号"></el-table-column>

        <el-table-column prop="groname" label="所属巴组"></el-table-column>
        <el-table-column prop="tmename" label="所属小队"></el-table-column>
        <el-table-column prop="memrole" label="角色"></el-table-column>

        <!-- <el-table-column prop="departmentname" label="部门"></el-table-column>
        <el-table-column prop="jobtitlename" label="职务"></el-table-column> -->

        <el-table-column prop="houwage" label="时薪"></el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="fromVisible = false">取 消</el-button>
        <el-button type="primary" @click="savemen">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import service from "@/router/request";
import { debounce } from "@/utils/utils";

export default {
  name: "jishi",
  data() {
    const currentDate = new Date();
    const end = new Date(currentDate); // 设置当前日期为 end

    return {
      fromVisible: false,
      contentOptions: ["装配", "接电", "调试", "接气", "打包发货", "更新优化", "异常处理", "卫生清理", "出差", "其他"],
      options: [
        {
          value: "P",
          label: "个人",
        },
        {
          value: "T",
          label: "团队",
        },
      ],
      //表头表单数据
      formInline: {
        //任务内容
        ttmcontent: "",
        //合计工时
        ttmphour: 0,
        //合计工价
        ttmpwage: 0,
        //任务备注
        ttmremarks: "",
        // 开始时间
        ttmsdate: currentDate,
        // 结束时间
        ttmedate: end,
      },
      rules: {
        ttmcontent: [{ required: true, message: "内容不能为空", trigger: "change" }],
        ttmsdate: [{ required: true, message: "开始时间不能为空", trigger: "blur" }],
        ttmedate: [{ required: true, message: "结束时间不能为空", trigger: "blur" }],
      },
      //表身工程编码
      option: [],
      loading: false,
      fieldMapping: {
        ttdprono: "ttdprono",
        departmentname: "groname",
        loginid: "ttdexter",
        lastname: "ttdextername",
        jobtitlename: "ttdjobtitle",
        houwage: "ttdprice",
        ttdhour: "ttdhour",
        ttdwage: "ttdwage",
        ttdsmtype: "ttdsmtype",
        ttdremarks: "ttdremarks",
        groname: "tmename",
        memrole: "ttdrole",
        ttm_no: "ttm_no",
        ttdno: "ttdno",
        tmeno: "tmeno",
      },
      //搜索出的数据
      filteredData: [],
      //表身人员数据
      // tableData: [],
      tableDataMap: [],
      //选中行的数据
      multipleSelection: [],
      // 选中的行数据
      selectedRows: [],
      memberSearchKey: "departmentname",
      memberSearchValue: "",
      memberSearchKeys: [
        { label: "部门", value: "departmentname" },
        { label: "工号", value: "loginid" },
        { label: "姓名", value: "lastname" },
        { label: "小队", value: "groname" },
      ],
      isCheck: false,
    };
  },

  watch: {
    // 监听tableData数据的变化
    tableDataMap: {
      handler() {
        this.formInline.ttmpwage = "";
        const total = this.tableDataMap.reduce((acc, item) => {
          if (!isNaN(parseFloat(item.ttdhour, 10))) {
            return acc + parseFloat(item.ttdhour, 10) * parseFloat(item.houwage, 10);
          } else {
            return acc;
          }
        }, 0);

        if (isNaN(total)) {
          // 在终端输出无效的总价
          console.warn("Invalid total ttdwage:", total);
        } else {
          this.formInline.ttmpwage = total;
        }
      },
      deep: true, // 深度监听数组或对象的变化
    },
  },
  mounted() {
    this.$route.query.rowData && (this.formInline = JSON.parse(decodeURIComponent(this.$route.query.rowData)));
    this.isCheck = !!this.$route.query.isCheck;
    if (this.formInline.ttmno) this.getTimetaskmain();

    this.queryDepartments();
  },
  methods: {
    debounce,
    getTimetaskmain() {
      service.post("/TaskCheck/GetTimetaskmain", { ttm_tpt: this.formInline.ttmno }).then((res) => {
        if (res.status == "succeed") {
          this.tableDataMap = res.data || [];
          this.tableDataMap = this.tableDataMap.map((item) => {
            let newData = {};
            for (const key in this.fieldMapping) {
              newData[key] = item[this.fieldMapping[key]] == 0 ? 0 : item[this.fieldMapping[key]] || "";
            }
            return newData;
          });
          this.tableDataMap.forEach((item) => (item.ttdprono = item.ttdprono.split(",")));
        }
      });
    },

    searchMember() {
      this.filteredData = this.restaurants.filter((item) =>
        item[this.memberSearchKey].includes(this.memberSearchValue)
      );
    },

    //模糊查询工程编码
    queryDepartments(query) {
      this.loading = true;
      service
        .post("/JiShi/select_ttdprono", { fuzzy: query })
        .then((res) => {
          if (res.status == "succeed") {
            this.loading = false;
            this.option = res.data.list;
          }
        })
        .catch((error) => {
          console.error(error);
        });
    },

    //合计工时
    updatahour() {
      this.formInline.ttmphour = "";
      const total = this.tableDataMap.reduce((acc, item) => {
        if (typeof item.ttdhour === "string" && !isNaN(parseFloat(item.ttdhour, 10))) {
          return acc + parseFloat(item.ttdhour, 10);
        } else {
          return acc;
        }
      }, 0);
      if (isNaN(total)) {
        // 判断累加结果是否有效
        console.warn("Invalid total ttdhour:", total);
      } else {
        this.formInline.ttmphour = total;
      }
    },

    //存储当前选中行信息
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },

    //存储当前选中行信息
    handleSelectionChange2(val) {
      this.selectedRows = val;
    },
    //搜索后表格勾选不取消
    getRowKey(row) {
      return row.loginid;
    },
    handleAdd() {
      // 新增数据
      this.memberSearchValue = "";
      this.multipleSelection = [];
      this.fromVisible = true; // 打开弹窗
      this.loadAll();
      if (this.$refs.multipleTable) {
        this.$refs.multipleTable.clearSelection();
      }
    },

    //实现表格双击可编辑
    // eslint-disable-next-line no-unused-vars
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex % 4 == 1) {
        return "warning-row";
      } else if (rowIndex % 4 == 3) {
        return "success-row";
      }
      return "";
    },
    //查询出所有成员
    loadAll() {
      service
        .get("/Public/Getmember1")
        .then((res) => {
          if (res.status == "succeed") {
            this.restaurants = res.data;
            this.filteredData = res.data;
          }
        })
        .catch((error) => {
          console.error("Error fetching restaurants", error);
        });
    },

    //将选中的人员添加到表身
    savemen() {
      this.multipleSelection.forEach((item) => {
        this.$set(item, "ttdsmtype", "P");
        this.$set(item, "ttdhour", 0);
      });
      this.multipleSelection.forEach((item) => (item.ttdwage = 0));
      this.tableDataMap = this.tableDataMap.concat(this.multipleSelection);
      (this.multipleSelection = []), (this.fromVisible = false);
    },

    // 删除选中的行数据
    deleteSelectedRows() {
      if (!this.selectedRows || this.selectedRows.length == 0) return this.$message.warning("未选中数据！");
      let ttdnos = [];
      this.selectedRows.forEach((row) => row.ttdno && ttdnos.push(row.ttdno));
      let delTableData = () => {
        this.selectedRows.forEach((row) => {
          const index = this.tableDataMap.indexOf(row);
          this.tableDataMap.splice(index, 1);
        });
        this.$message.success("删除成功");
      };
      if (!ttdnos.length) return delTableData();
      service.post("/TaskHours/getTaskHoursList", { taskNos: ttdnos }).then((res) => {
        if (res.status == "succeed") {
          this.$confirm(
            `${res.data?.length ? "任务已产生日报，删除任务的同时会删除其所关联日报，" : ""}确定删除所选中任务吗？`,
            "提示",
            {
              confirmButtonText: "确认",
              cancelButtonText: "取消",
              type: "warning",
            }
          )
            .then(() => {
              service.post("/JiShi/deleteJiShi", ttdnos).then((delRes) => {
                if (delRes.status == "succeed") {
                  delTableData();
                } else {
                  this.$message.error(delRes.err);
                }
              });
            })
            .catch(() => {});
        } else {
          this.$message.error(res.err);
        }
      });
    },

    transformTableData(tableData) {
      return tableData.map((item) => {
        let newData = {};
        for (const key in this.fieldMapping) {
          newData[this.fieldMapping[key]] = item[key] == 0 ? item[key] : item[key] || "";
        }
        newData.ttdprono = newData.ttdprono.join(",");
        return newData;
      });
    },

    submit() {
      this.$refs.jishiForm.validate((valid) => {
        if (valid) {
          const requestData = this.transformTableData(this.tableDataMap);
          if (!requestData.length) return this.$message.warning("请指派任务");
          if (requestData.some((item) => !item.ttdprono)) return this.$message.warning("存在未选择工程编码的情况");
          if (requestData.some((item) => !item.ttdhour)) return this.$message.warning("存在未分配工时的情况");
          const loading = this.$loading({
            lock: true,
            text: "提交中.....",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          service
            .post(this.formInline.ttmno ? "/JiShi/updateJiShi" : "/JiShi/addJiShi", {
              task: this.formInline,
              list: requestData,
            })
            .then((res) => {
              loading.close();
              if (res.status == "succeed") {
                this.$message.success(this.formInline.ttmno ? "修改成功" : "添加成功");
                if (!this.formInline.ttmno) {
                  this.formInline = {};
                  this.tableDataMap = [];
                }
              } else {
                this.$message.error(res.message);
              }
            });
        }
      });
    },

    memberTableRowClick(row) {
      this.$refs.multipleTable.toggleRowSelection(row);
    },
  },
};
</script>

<style scoped>
.top-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.top-bar h3 {
  margin: 0;
}

.middle-bar {
  display: flex;
  justify-content: space-between;
}

.el-row {
  margin-bottom: 0;
}
</style>
