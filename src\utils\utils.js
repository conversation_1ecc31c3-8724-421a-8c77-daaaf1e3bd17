import * as XLSX from "xlsx";

/**
 * 数组导出为xlsx文件
 * @param {Object[]} json 数据
 * @param {Object} fields 字段映射
 * @param {string} filename 文件名
 */
export function xlsx(json, fields = null, filename = "欧克文件") {
  //导出xlsx
  fields &&
    json.forEach((item) => {
      for (let i in item) {
        if (fields.hasOwnProperty(i)) {
          item[fields[i]] = item[i];
        }
        delete item[i]; //删除原先的对象属性
      }
    });
  let sheetName = filename; //excel的文件名称
  let wb = XLSX.utils.book_new(); //工作簿对象包含一SheetNames数组，以及一个表对象映射表名称到表对象。XLSX.utils.book_new实用函数创建一个新的工作簿对象。
  let ws = XLSX.utils.json_to_sheet(json, fields ? { header: Object.values(fields) } : null); //将JS对象数组转换为工作表。
  wb.SheetNames.push(sheetName);
  wb.Sheets[sheetName] = ws;
  downloadXLSX(wb, filename);
}

export function xlsxMultiple(jsonArr, fields = null, filename = "欧克文件", sheetNames = ["欧克文件"]) {
  let wb = XLSX.utils.book_new();
  jsonArr.forEach((json, idx) => {
    fields &&
      json.forEach((item) => {
        for (let i in item) {
          if (fields.hasOwnProperty(i)) {
            item[fields[i]] = item[i];
          }
          delete item[i];
        }
      });
    let sheetName = sheetNames[idx];
    let ws = XLSX.utils.json_to_sheet(json, fields ? { header: Object.values(fields) } : null); //将JS对象数组转换为工作表。
    wb.SheetNames.push(sheetName);
    wb.Sheets[sheetName] = ws;
  });
  downloadXLSX(wb, filename);
}

export function downloadXLSX(wb, filename) {
  let wopts = {
    bookType: "xlsx",
    bookSST: false,
    type: "binary",
    cellStyles: true,
    defaultCellStyle: {
      font: { name: "Verdana", sz: 13, color: "FF00FF88" },
      fill: { fgColor: { rgb: "FFFFAA00" } },
    }, // 设置表格的样式
    showGridLines: false,
  }; //写入的样式
  let wbout = XLSX.write(wb, wopts);
  let blob = new Blob([s2ab(wbout)], { type: "application/octet-stream" });
  let a = document.createElement("a");
  a.href = URL.createObjectURL(blob);
  a.setAttribute("download", `${filename}.xlsx`);
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}

const s2ab = (s) => {
  var buf;
  if (typeof ArrayBuffer !== "undefined") {
    buf = new ArrayBuffer(s.length);
    var view = new Uint8Array(buf);
    for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xff;
    return buf;
  } else {
    buf = new Array(s.length);
    for (var i = 0; i != s.length; ++i) buf[i] = s.charCodeAt(i) & 0xff;
    return buf;
  }
};

export function isMobile() {
  // 定义一个正则表达式，用于匹配常见的移动设备的用户代理字符串
  const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;

  // 获取用户代理字符串
  const userAgent = navigator.userAgent;

  // 使用正则表达式测试用户代理字符串
  return mobileRegex.test(userAgent);
}

export const readExcelToJson = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (e) => {
      let data = new Uint8Array(e.target.result);
      let workbook = XLSX.read(data, { type: "array" });
      let worksheet = workbook.Sheets[workbook.SheetNames[0]];
      let jsonData = XLSX.utils.sheet_to_json(worksheet);
      // 获取表头信息作为一个数组
      const header = [];
      for (let cell in worksheet) {
        if (cell.length == 2 && cell[1] === "1" && worksheet[cell].v) {
          header.push(worksheet[cell].v);
        }
      }
      // 处理合并单元格的数据
      const mergedCells = worksheet["!merges"];
      if (mergedCells) {
        mergedCells.forEach((mergedCell) => {
          const { s, e } = mergedCell;
          const startRow = s.r - 1;
          const endRow = e.r - 1;
          const startCol = s.c;
          const endCol = e.c;

          // 将合并单元格的数据填充到每个单元格
          for (let i = startRow; i <= endRow; i++) {
            for (let j = startCol; j <= endCol; j++) {
              if (i !== startRow || j !== startCol) {
                jsonData[i][header[j]] = jsonData[startRow][header[startCol]];
              }
            }
          }
        });
      }

      resolve(jsonData);
    };

    reader.readAsArrayBuffer(file.raw);
  });
};

export function debounce(func, delay) {
  let timer;

  return function () {
    const context = this;
    const args = arguments;

    clearTimeout(timer);
    timer = setTimeout(() => {
      func.apply(context, args);
    }, delay);
  };
}

// 生成颜色数组（参数：需要生成的颜色数量）
export const generateColors = (count) => {
  const colors = [];
  const hueStep = 360 / count; // 色相均匀分布

  for (let i = 0; i < count; i++) {
    // 随机生成饱和度(60-90%)和亮度(40-70%)
    const s = 70 + Math.random() * 20;
    const l = 50 + Math.random() * 20;
    colors.push(`hsl(${i * hueStep}, ${s}%, ${l}%)`);
  }
  return colors;
};

export class LocalStorage {
  static prefix = "OK_PersonnelManagement_";

  static setItem(key, value) {
    localStorage.setItem(LocalStorage.prefix + key, value);
  }

  static getItem(key) {
    return localStorage.getItem(LocalStorage.prefix + key);
  }

  static removeItem(key) {
    localStorage.removeItem(LocalStorage.prefix + key);
  }
}

import { Message } from "element-ui";
export class MessageUtil {
  static success(message) {
    Message.success({
      message: message,
      duration: 2000,
      showClose: true,
      customClass: isMobile() ? "phone-message-tip" : "",
    });
  }

  static error(message) {
    Message.error({
      message: message,
      duration: 2000,
      showClose: true,
      customClass: isMobile() ? "phone-message-tip" : "",
    });
  }

  static warning(message) {
    Message.warning({
      message: message,
      duration: 2000,
      showClose: true,
      customClass: isMobile() ? "phone-message-tip" : "",
    });
  }

  static info(message) {
    Message.info({
      message: message,
      duration: 2000,
      showClose: true,
      customClass: isMobile() ? "phone-message-tip" : "",
    });
  }
}
