<template>
  <div>
    <div class="mb-2 top-bar">
      <el-button @click="$router.back()" size="medium" icon="el-icon-back" circle></el-button>
      <div>
        <el-button @click="printIframeContent" type="primary" plain>打印</el-button>
      </div>
    </div>
    <div style="overflow: hidden; min-width: 1000px">
      <div class="print-content">
        <h1>个人工资汇总</h1>
        <div class="mb-2" style="width: 100%">
          <el-descriptions :column="14" direction="vertical" border>
            <el-descriptions-item v-for="item of personalPayrollSummaryConfig" :key="item.value" :label="item.label">
              {{ item.value }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <h1 class="whole-node">计时任务明细</h1>
        <el-table
          :data="jiShiTaskTableData"
          border
          show-summary
          :summary-method="jiShiSummary"
          class="mb-2"
          :max-height="tableMaxHeight"
        >
          <el-table-column
            v-for="item of jiShiTaskTableConfig"
            :key="`jiShi-${item.prop}`"
            v-bind="item"
          ></el-table-column>
        </el-table>
        <h1 class="whole-node">临时任务明细</h1>
        <el-table :data="linShiTaskTableData" border class="mb-2" :max-height="tableMaxHeight" show-summary>
          <el-table-column
            v-for="item of linShiTaskTableConfig"
            :key="`linShi-${item.prop}`"
            v-bind="item"
          ></el-table-column>
        </el-table>
        <h1 class="whole-node">绩效任务明细</h1>
        <el-table :data="performanceTableData" border class="mb-2" :max-height="tableMaxHeight">
          <el-table-column
            v-for="item of performanceTableConfig"
            :key="`performance-${item.prop}`"
            v-bind="item"
          ></el-table-column>
        </el-table>
      </div>
    </div>

    <div style="width: 0; height: 0; overflow: hidden; position: absolute; top: 0; left: 0; z-index: 3">
      <div ref="printContent" id="printContent" class="print-content print">
        <h1 class="whole-node">个人工资汇总</h1>
        <table>
          <thead>
            <tr>
              <th
                v-for="(config, idx) of personalPayrollSummaryConfig"
                :key="config.prop"
                :style="{ width: `${config.width}px`, minWidth: `${config['min-width']}px` }"
              >
                {{ config.label }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td v-for="(config, idx) of personalPayrollSummaryConfig" :key="config.prop + idx">{{ config.value }}</td>
            </tr>
          </tbody>
        </table>
        <table>
          <tbody>
            <tr>
              <th colspan="1" style="width: 60px; padding: 20px; font-size: 14px">本人签字</th>
              <td colspan="1"></td>
              <th colspan="1" style="width: 60px; padding: 20px; font-size: 14px">巴长审核</th>
              <td colspan="1"></td>
              <th colspan="1" style="width: 60px; padding: 20px; font-size: 14px">部长审核</th>
              <td colspan="1"></td>
            </tr>
          </tbody>
        </table>
        <template v-if="jiShiTaskTableData && jiShiTaskTableData.length">
          <h1 class="whole-node">计时任务明细</h1>
          <table>
            <thead>
              <tr>
                <th
                  v-for="config of jiShiTaskTablePConfig"
                  :style="{ width: `${config.width}px`, minWidth: `${config['min-width']}px` }"
                >
                  {{ config.label }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, idx) of summaryData(jiShiTaskTableData, 'ttdextername')" :key="idx">
                <td v-for="config of jiShiTaskTablePConfig">{{ item[config.prop] }}</td>
              </tr>
            </tbody>
          </table>
        </template>
        <template v-if="linShiTaskTableData && linShiTaskTableData.length">
          <h1 class="whole-node">临时任务明细</h1>
          <table>
            <thead>
              <tr>
                <th v-for="config of linShiTaskTablePConfig">{{ config.label }}</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, idx) of summaryData(linShiTaskTableData, 'ttdextername')" :key="idx">
                <td
                  v-for="config of linShiTaskTablePConfig"
                  :style="{ width: `${config.width}px`, minWidth: `${config['min-width']}px` }"
                >
                  {{ item[config.prop] }}
                </td>
              </tr>
            </tbody>
          </table>
        </template>
        <template v-if="performanceTableData && performanceTableData.length">
          <h1 class="whole-node">绩效任务明细</h1>
          <table>
            <thead>
              <tr>
                <th v-for="config of performanceTableConfig">{{ config.label }}</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, idx) of summaryData(performanceTableData, 'loginid')" :key="idx">
                <td
                  v-for="config of performanceTableConfig"
                  :style="{ width: `${config.width}px`, minWidth: `${config['min-width']}px` }"
                >
                  {{ item[config.prop] }}
                </td>
              </tr>
            </tbody>
          </table>
        </template>
      </div>
    </div>

    <div style="height: 0; overflow: hidden; position: absolute; top: 0; left: 0; z-index: 3">
      <div ref="printContent" id="printContent1" class="print-content print">
        <h1 class="whole-node">个人工资汇总</h1>
        <table>
          <thead>
            <tr>
              <th
                v-for="(config, idx) of personalPayrollSummaryConfig"
                :key="config.prop"
                :style="{ width: `${config.width}px`, minWidth: `${config['min-width']}px` }"
              >
                {{ config.label }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td v-for="(config, idx) of personalPayrollSummaryConfig" :key="config.prop + idx">{{ config.value }}</td>
            </tr>
          </tbody>
        </table>
        <table>
          <tbody>
            <tr>
              <th colspan="1" style="width: 60px; padding: 20px; font-size: 14px">本人签字</th>
              <td colspan="1"></td>
              <th colspan="1" style="width: 60px; padding: 20px; font-size: 14px">巴长审核</th>
              <td colspan="1"></td>
              <th colspan="1" style="width: 60px; padding: 20px; font-size: 14px">部长审核</th>
              <td colspan="1"></td>
            </tr>
          </tbody>
        </table>
        <template v-if="jiShiTaskTableData && jiShiTaskTableData.length">
          <h1 class="whole-node">计时任务明细</h1>
          <table>
            <thead>
              <tr>
                <th
                  v-for="config of jiShiTaskTableP1Config"
                  :style="{ width: `${config.width}px`, minWidth: `${config['min-width']}px` }"
                >
                  {{ config.label }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, idx) of summaryData(jiShiTaskTableData, 'ttdextername')" :key="idx">
                <td v-for="config of jiShiTaskTableP1Config">{{ item[config.prop] }}</td>
              </tr>
            </tbody>
          </table>
        </template>
        <template v-if="linShiTaskTableData && linShiTaskTableData.length">
          <h1 class="whole-node">临时任务明细</h1>
          <table>
            <thead>
              <tr>
                <th v-for="config of linShiTaskTableP1Config">{{ config.label }}</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, idx) of summaryData(linShiTaskTableData, 'ttdextername')" :key="idx">
                <td
                  v-for="config of linShiTaskTableP1Config"
                  :style="{ width: `${config.width}px`, minWidth: `${config['min-width']}px` }"
                >
                  {{ item[config.prop] }}
                </td>
              </tr>
            </tbody>
          </table>
        </template>
        <template v-if="performanceTableData && performanceTableData.length">
          <h1 class="whole-node">绩效任务明细</h1>
          <table>
            <thead>
              <tr>
                <th v-for="config of performanceTableConfig">{{ config.label }}</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, idx) of summaryData(performanceTableData, 'loginid')" :key="idx">
                <td
                  v-for="config of performanceTableConfig"
                  :style="{ width: `${config.width}px`, minWidth: `${config['min-width']}px` }"
                >
                  {{ item[config.prop] }}
                </td>
              </tr>
            </tbody>
          </table>
        </template>
      </div>
    </div>
    <iframe id="frame" src="" frameborder="0" style="display: none"> </iframe>
  </div>
</template>

<script>
import service from "@/router/request";

export default {
  name: "personalwagedetails",
  data() {
    return {
      personalPayroll: {},
      personalPayrollSummaryConfig: [
        { label: "所属巴组", value: "", prop: "groname", width: "60" },
        { label: "巴长", value: "", prop: "grohmname" },
        { label: "团队名称", value: "", prop: "tmename" },
        { label: "队长", value: "", prop: "tmehmname" },
        { label: "姓名", value: "", prop: "memname" },
        { label: "核算月份", value: "", prop: "mmonth", width: "60" },
        { label: "团队任务工时", value: "", prop: "teamTaskManHours", labelStyle: "width: 75px", width: "45" },
        { label: "团队任务工资", value: "", prop: "teamTaskWage", labelStyle: "width: 75px", width: "45" },
        { label: "个人任务工时", value: "", prop: "individualTaskManHours", labelStyle: "width: 75px", width: "45" },
        { label: "个人任务工资", value: "", prop: "individualTaskWage", labelStyle: "width: 75px", width: "45" },
        { label: "绩效", value: "", prop: "pftwage", width: "30" },
        { label: "合计工时", value: "", prop: "smhours", width: "30" },
        { label: "合计工资", value: "", prop: "smwages", width: "30" },
        { label: "平均时价", value: 0, prop: "pphs" },
      ],
      jiShiTaskTableData: [], // 计时任务明细表
      jiShiTaskTableConfig: [
        { label: "姓名", prop: "ttdextername", width: "90" },
        { label: "任务开始", prop: "ttmsdate", width: "125" },
        { label: "任务结束", prop: "ttmedate", width: "125" },
        { label: "工程号", prop: "ttdprono", width: "140" },
        { label: "任务内容", prop: "ttmcontent", "min-width": "200" },
        { label: "预计工时", prop: "ttdhour", width: "80" },
        { label: "录入工时", prop: "hours", width: "80" },
        { label: "工时价", prop: "hourlyWage", width: "80" },
        { label: "预计工价", prop: "ttdwage", width: "90" },
        { label: "实际工价", prop: "wages", width: "80" },
        { label: "任务备注", prop: "ttdremarks", "min-width": "200" },
      ],
      linShiTaskTableData: [], // 临时任务明细表
      linShiTaskTableConfig: [
        { label: "姓名", prop: "ttdextername", width: "90" },
        { label: "任务开始", prop: "ttmsdate", width: "125" },
        { label: "任务结束", prop: "ttmedate", width: "125" },
        { label: "工程号", prop: "ttdprono", width: "140" },
        { label: "任务内容", prop: "ttmcontent", "min-width": "200" },
        { label: "数量", prop: "ttdhour", width: "80" },
        { label: "单价", prop: "ttdprice", width: "80" },
        { label: "工价", prop: "ttdwage", width: "80" },
        { label: "录入工时", prop: "hours", width: "80" },
        { label: "任务备注", prop: "ttdremarks", "min-width": "200" },
      ],
      performanceTableData: [],
      performanceTableConfig: [
        { label: "工号", prop: "loginid" },
        { label: "姓名", prop: "lastname" },
        { label: "绩效金额", prop: "basicPerformance" },
        { label: "工作天数", prop: "work_days" },
        { label: "考核天数", prop: "assessment_days" },
        { label: "考核分数", prop: "assessment_grade" },
        { label: "应得金额", prop: "performanceWage" },
        { label: "备注", prop: "remarks" },
      ],
      memberList: [],
      memberDialogTableVisible: false,
      memberTableCurrentRow: null,
      tableMaxHeight: "500",
      signatureAreaShow: false,

      jiShiTaskTablePConfig: [
        { label: "姓名", prop: "ttdextername", width: "40" },
        { label: "任务开始", prop: "ttmsdate", width: "70" },
        { label: "任务结束", prop: "ttmedate", width: "70" },
        { label: "工程号", prop: "ttdprono", width: "70" },
        { label: "任务内容", prop: "ttmcontent" },
        { label: "预计工时", prop: "ttdhour", width: "30" },
        { label: "录入工时", prop: "hours", width: "30" },
        { label: "工时价", prop: "hourlyWage", width: "30" },
        { label: "预计工价", prop: "ttdwage", width: "40" },
        { label: "实际工价", prop: "wages", width: "40" },
        { label: "任务备注", prop: "ttdremarks" },
      ],
      linShiTaskTablePConfig: [
        { label: "姓名", prop: "ttdextername", width: "40" },
        { label: "任务开始", prop: "ttmsdate", width: "70" },
        { label: "任务结束", prop: "ttmedate", width: "70" },
        { label: "工程号", prop: "ttdprono", width: "70" },
        { label: "任务内容", prop: "ttmcontent" },
        { label: "数量", prop: "ttdhour", width: "30" },
        { label: "单价", prop: "ttdprice", width: "30" },
        { label: "工价", prop: "ttdwage", width: "30" },
        { label: "录入工时", prop: "hours", width: "30" },
        { label: "任务备注", prop: "ttdremarks" },
      ],

      jiShiTaskTableP1Config: [
        { label: "姓名", prop: "ttdextername", width: "40" },
        { label: "任务开始", prop: "ttmsdate", width: "70" },
        { label: "任务结束", prop: "ttmedate", width: "70" },
        { label: "工程号", prop: "ttdprono", width: "80" },
        { label: "任务内容", prop: "ttmcontent" },
        { label: "预计工时", prop: "ttdhour", width: "30" },
        { label: "录入工时", prop: "hours", width: "30" },
        { label: "工时价", prop: "hourlyWage", width: "30" },
        { label: "预计工价", prop: "ttdwage", width: "30" },
        { label: "实际工价", prop: "wages", width: "40" },
        { label: "任务备注", prop: "ttdremarks" },
      ],
      linShiTaskTableP1Config: [
        { label: "姓名", prop: "ttdextername", width: "40" },
        { label: "任务开始", prop: "ttmsdate", width: "70" },
        { label: "任务结束", prop: "ttmedate", width: "70" },
        { label: "工程号", prop: "ttdprono", width: "80" },
        { label: "任务内容", prop: "ttmcontent" },
        { label: "数量", prop: "ttdhour", width: "30" },
        { label: "单价", prop: "ttdprice", width: "30" },
        { label: "工价", prop: "ttdwage", width: "30" },
        { label: "录入工时", prop: "hours", width: "30" },
        { label: "任务备注", prop: "ttdremarks" },
      ],
    };
  },

  mounted() {
    this.init();
  },

  methods: {
    init() {
      this.personalPayroll = JSON.parse(decodeURIComponent(this.$route.query.personalPayroll));
      this.personalPayrollSummaryConfig.forEach((item) => (item.value = this.personalPayroll[item.prop]));
      this.getJiShi();
      this.getLinShi();
      this.getPerformanceTask();
    },

    getJiShi() {
      service
        .post("/PersonalAccount/GetJiShi", {
          loginid: this.personalPayroll.memno,
          mmonth: this.personalPayroll.mmonth,
        })
        .then((res) => {
          if (res.status == "succeed") {
            this.jiShiTaskTableData = res.data;
            this.jiShiTaskTableData.forEach((row) => {
              row.ttdhour = parseFloat(row.ttdhour);
              row.ttdwage = parseFloat(row.ttdwage);
            });
          }
        });
    },

    getLinShi() {
      service
        .post("/PersonalAccount/GetLinShi", {
          loginid: this.personalPayroll.memno,
          mmonth: this.personalPayroll.mmonth,
        })
        .then((res) => {
          if (res.status == "succeed") {
            this.linShiTaskTableData = res.data;
            this.linShiTaskTableData.forEach((row) => {
              row.ttdhour = parseFloat(row.ttdhour);
              row.ttdprice = parseFloat(row.ttdprice);
              row.ttdwage = parseFloat(row.ttdwage);
            });
          }
        });
    },

    jiShiSummary({ columns, data }) {
      let fields = ["ttdhour", "ttdwage", "hours", "wages"];
      let sums = [];
      columns.forEach((column, index) => {
        if (index === 0) return (sums[index] = "合计");
        if (fields.some((item) => item == column.property)) {
          const values = data.map((item) => Number(item[column.property]));
          if (!values.every((value) => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0);
          } else {
            sums[index] = "";
          }
        }
      });
      return sums;
    },

    printIframeContent() {
      let printFrame = document.getElementById("frame");
      let style = `@page{size:auto;margin:15px}.print-content{display:flex;flex-direction:column;align-items:center;height:fit-content}table{width:100%}table,td,th{border:1px solid #000;border-collapse:collapse}th,td{text-align:left;padding:5px}th{font-weight:bold;font-size:14px;background-color:#c0c4cc;-webkit-print-color-adjust:exact}td{font-size:12px}tr{page-break-inside:avoid}.t1 td{font-size:12px;padding:15px 5px}.print-content{display:flex;flex-direction:column;align-items:center;height:fit-content} h1{font-size:18px;margin:0;text-align:center}`;
      let content =
        `<html><head><title>Print</title><style>${style}</style></head><body>` +
        document.getElementById("printContent").innerHTML +
        "</body></html>";
      printFrame.contentWindow.document.open();
      printFrame.contentWindow.document.write(content);
      printFrame.contentWindow.document.close();
      printFrame.contentWindow.focus();
      printFrame.contentWindow.print();
    },

    getPerformanceTask() {
      service
        .get("/Performance/getPerformanceTask", {
          params: {
            loginid: this.personalPayroll.memno,
            acc_month: this.personalPayroll.mmonth,
          },
        })
        .then((res) => {
          if (res.status == "succeed") {
            this.performanceTableData = res.data ? [res.data] : [];
            this.performanceTableData.forEach((row) => {
              row.performanceWage = parseFloat(row.performanceWage.toFixed(2));
            });
          } else {
            this.$message.error(res.err);
          }
        });
    },

    summaryData(data, prop) {
      let obj = {};
      if (data?.length) {
        for (let key in data[0]) {
          let s = data.reduce((val, item) => (val += +item[key]), 0);
          !isNaN(s) && (obj[key] = Math.round(s * 100) / 100);
        }
      }
      obj[prop] = "合计";
      return obj && [...data, obj];
    },
  },
};
</script>

<style scoped>
.print-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: fit-content;
}

table {
  width: 100%;
}

table,
td,
th {
  border: 1px solid #000;
  border-collapse: collapse;
}

th,
td {
  text-align: left;
  padding: 5px;
}

th {
  font-weight: bold;
  font-size: 14px;
  background-color: #c0c4cc;
  -webkit-print-color-adjust: exact;
}

td {
  font-size: 12px;
}

tr {
  page-break-inside: avoid;
}

.t1 td {
  font-size: 12px;
  padding: 15px 5px;
}

.print-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: fit-content;
}

.print h1 {
  font-size: 18px;
  margin: 0 !important;
}
@media print {
  table {
    width: 100%;
  }

  table,
  td,
  th {
    border: 1px solid #000;
    border-collapse: collapse;
  }

  th,
  td {
    text-align: left;
    padding: 5px;
  }

  th {
    font-weight: bold;
    font-size: 14px;
    background-color: #c0c4cc;
    -webkit-print-color-adjust: exact;
  }

  td {
    font-size: 10px;
  }

  tr {
    page-break-inside: avoid;
  }

  .t1 td {
    font-size: 12px;
    padding: 15px 5px;
  }

  .print-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: fit-content;
  }

  .print h1 {
    font-size: 18px;
    margin: 0 !important;
  }
}

.top-bar {
  display: flex;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 1;
}
</style>
