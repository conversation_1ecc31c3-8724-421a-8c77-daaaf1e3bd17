<template>
  <div>
    <div ref="searchBarRef" class="search-bar">
      <div class="search-item">
        <span class="label">工程号：</span>
        <el-input v-model="prono" placeholder="请输入工程号"></el-input>
      </div>
      <el-button class="search-btn" type="primary" @click="serach">搜索</el-button>
      <el-button @click="exportData" type="warning" class="search-btn">导出</el-button>
    </div>

    <!-- 合并后的图表容器 -->
    <div id="combinedChart" style="width: 100%; height: 600px"></div>

    <el-table v-if="tableMaxHeight" :data="tableData" border :max-height="tableMaxHeight">
      <el-table-column v-for="(config, idx) of tableConfig" :key="idx" v-bind="config"></el-table-column>
    </el-table>
    <el-pagination
      ref="pageRef"
      @size-change="onSizeChange"
      @current-change="onCurrentChange"
      :current-page="paging.pageNum"
      :page-sizes="[10, 20, 30, 40]"
      :page-size="paging.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="paging.total"
    >
    </el-pagination>
  </div>
</template>

<script>
import service from "@/router/request";
import { xlsx } from "@/utils/utils";
import * as echarts from "echarts";

export default {
  name: "ProNoIndividualHoursSummary",

  data() {
    return {
      prono: "",
      data: [],
      tableConfig: [
        { label: "任务类型", prop: "typeName", width: "60" },
        { label: "任务内容", prop: "taskName", "min-width": 160 },
        { label: "参与者", prop: "participant" },
        { label: "录入工时", prop: "hours" },
        { label: "工时价", prop: "hourlyWage" },
        { label: "工价", prop: "wage" },
        { label: "开始日期", prop: "startDate", width: "120" },
        { label: "结束日期", prop: "endDate", width: "120" },
      ],
      paging: {
        pageNum: 1,
        pageSize: 40,
        total: 0,
      },
      tableMaxHeight: 0,
      taskHoursForMonth: [],
    };
  },

  mounted() {
    this.computeTableMaxHeight();
  },

  computed: {
    tableData() {
      return this.data.slice(
        (this.paging.pageNum - 1) * this.paging.pageSize,
        this.paging.pageNum * this.paging.pageSize
      );
    },

    hoursData() {
      return this.data
        .reduce(
          (value, item) => {
            if (item.typeName === "计件") {
              value[0] += item.hours || 0;
            } else if (item.typeName === "计时") {
              value[1] += item.hours || 0;
            } else if (item.typeName === "临时") {
              value[2] += item.hours || 0;
            }
            return value;
          },
          [0, 0, 0]
        )
        .map((val) => parseFloat(val.toFixed(2)));
    },

    wageData() {
      return this.data
        .reduce(
          (value, item) => {
            if (item.typeName === "计件") {
              value[0] += item.wage || 0;
            } else if (item.typeName === "计时") {
              value[1] += item.wage || 0;
            } else if (item.typeName === "临时") {
              value[2] += item.wage || 0;
            }
            return value;
          },
          [0, 0, 0]
        )
        .map((val) => parseFloat(val.toFixed(2)));
    },
  },

  methods: {
    serach() {
      this.paging.pageNum = 1;
      this.getPieceTask();
      this.getMachineTaskHoursForMonth();
    },

    getPieceTask() {
      this.getProNoAllTask().then((list) => {
        this.data = list || [];
        this.paging.total = list.length || 0;
        if (!this.data.length) return this.$message.warning("暂无数据");
        this.data.forEach(
          (item) => (item.hourlyWage = item.typeName == "计时" ? item.hourlyWage : item.hourlyWage || "/")
        );

        this.drawCombinedChart();
      });
    },

    drawCombinedChart() {
      let myChart = echarts.init(document.getElementById("combinedChart"));

      const labelOption = {
        show: true,
        position: "insideBottom",
        distance: 15,
        align: "center",
        verticalAlign: "middle",
        rotate: 0,
        formatter: function (params) {
          if (params.seriesName === "工时") {
            return `${params.value} H`;
          } else if (params.seriesName === "工价") {
            return `￥${params.value}`;
          }
          return params.value;
        },
        fontSize: 16,
        rich: {
          name: {},
        },
      };

      const months = this.taskHoursForMonth.map((item) => item.month);
      const minMonth = months.reduce((min, curr) => (curr < min ? curr : min), months[0]);
      const maxMonth = months.reduce((max, curr) => (curr > max ? curr : max), months[0]);

      const generateMonths = (start, end) => {
        const result = [];
        let current = new Date(start + "-01");
        const endDate = new Date(end + "-01");
        while (current <= endDate) {
          result.push(current.toISOString().slice(0, 7));
          current.setMonth(current.getMonth() + 1);
        }
        return result;
      };
      const allMonths = generateMonths(minMonth, maxMonth);

      const typeMapping = { TAD: "计件", TTM: "计时", TPT: "临时" };
      const seriesData = { TAD: [], TTM: [], TPT: [] };

      allMonths.forEach((month) => {
        ["TAD", "TTM", "TPT"].forEach((type) => {
          const entry = this.taskHoursForMonth.find((item) => item.month === month && item.type === type);
          seriesData[type].push(entry ? entry.hours : 0);
        });
      });

      let option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: { data: ["工时", "工价", "计件", "计时", "临时"] },
        grid: [
          { top: "10%", bottom: "55%", containLabel: true },
          { top: "55%", bottom: "5%", containLabel: true },
        ],
        xAxis: [
          {
            type: "category",
            data: ["计件", "计时", "临时"],
            gridIndex: 0,
          },
          {
            type: "category",
            data: allMonths,
            gridIndex: 1,
          },
        ],
        yAxis: [
          { type: "value", name: "工时", gridIndex: 0 },
          { type: "value", name: "工价", gridIndex: 0 },
          { type: "value", name: "工时", gridIndex: 1 },
        ],
        series: [
          {
            name: "工时",
            data: this.hoursData,
            type: "bar",
            barGap: 0,
            emphasis: {
              focus: "series",
            },
            label: labelOption,
            xAxisIndex: 0,
            yAxisIndex: 0,
          },
          {
            name: "工价",
            data: this.wageData,
            type: "bar",
            barGap: 0,
            emphasis: {
              focus: "series",
            },
            label: labelOption,
            xAxisIndex: 0,
            yAxisIndex: 1,
          },
          {
            name: "计件",
            type: "bar",
            stack: "total",
            data: seriesData.TAD,
            xAxisIndex: 1,
            yAxisIndex: 2,
            label: { show: true, formatter: (params) => (params.value ? `${params.value} H` : "") },
          },
          {
            name: "计时",
            type: "bar",
            stack: "total",
            data: seriesData.TTM,
            xAxisIndex: 1,
            yAxisIndex: 2,
            label: { show: true, formatter: (params) => (params.value ? `${params.value} H` : "") },
          },
          {
            name: "临时",
            type: "bar",
            stack: "total",
            data: seriesData.TPT,
            xAxisIndex: 1,
            yAxisIndex: 2,
            label: { show: true, formatter: (params) => (params.value ? `${params.value} H` : "") },
          },
        ],
      };

      myChart.setOption(option);
    },

    onSizeChange(val) {
      this.paging.pageSize = val;
      this.serach();
    },

    onCurrentChange(val) {
      this.paging.pageNum = val;
      this.getPieceTask();
    },

    computeTableMaxHeight() {
      let main = document.getElementById("main");
      this.tableMaxHeight =
        main.clientHeight - this.$refs.searchBarRef.clientHeight - this.$refs.pageRef.$el.clientHeight - 20;
    },

    getProNoAllTask() {
      return new Promise((resolve, reject) => {
        const loading = this.$loading({
          lock: true,
          text: "导出中.....",
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.7)",
        });
        service
          .post("/DataSummary/getProNoAllTask", { prono: this.prono, pageNum: 1, pageSize: 9999999 })
          .then((res) => {
            if (res.status == "succeed") {
              if (!res.data.list?.length) {
                this.$message.warning("暂无数据");
                resolve([]);
              } else {
                res.data.list.forEach(
                  (item) => (item.hourlyWage = item.typeName == "计时" ? item.hourlyWage : item.hourlyWage || "/")
                );
                resolve(res.data.list);
              }
            } else {
              reject(new Error("请求失败"));
            }
          })
          .catch((error) => {
            reject(error);
          })
          .finally(() => loading.close());
      });
    },

    getMachineTaskHoursForMonth() {
      service.get("/DataSummary/getMachineTaskHoursForMonth", { params: { proNo: this.prono } }).then((res) => {
        if (res.status == "succeed") {
          this.taskHoursForMonth = res.data || [];
          this.drawCombinedChart();
        } else {
          this.$message.error(res.message);
        }
      });
    },

    exportData() {
      let map = {
        proNo: "工程号",
        typeName: "任务类型",
        taskName: "任务内容",
        participant: "参与者",
        hours: "录入工时",
        hourlyWage: "工时价",
        wage: "工价",
        startDate: "开始日期",
        endDate: "结束日期",
      };
      this.getProNoAllTask().then((data) => {
        if (data.length) {
          xlsx(data, map, `${this.prono}机器任务汇总`);
        }
      });
    },
  },
};
</script>

<style></style>
