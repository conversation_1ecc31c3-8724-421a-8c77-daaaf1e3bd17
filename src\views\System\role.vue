<template>
  <div>
    <div class="mb-2" style="display: flex;justify-content: end;">
      <el-button @click="roleDlgVisible = true" type="primary">添加角色</el-button>
    </div>
    <el-table :data="roleTableData" @row-click="(row) => currentRow = row">
      <el-table-column v-for="column of roleTableColumns" :key="column.prop" v-bind="column"></el-table-column>
      <el-table-column label="操作" width="350">
        <template v-slot="scope">
          <el-button type="success" @click="editMenuClick(scope.row)">编辑菜单权限</el-button>
          <el-button type="primary" @click="clickUpdate(scope.row)">修改</el-button>
          <el-button type="danger" @click="deleteRole(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 添加角色表单 -->
    <el-dialog :visible.sync="roleDlgVisible" title="角色" @close="resetRoleForm();roleForm.id = 0;">
      <el-form ref="roleForm" :model="roleForm" :rules="roleFormRules">
        <el-form-item label="角色名" prop="name">
          <el-input v-model="roleForm.name" placeholder="请输入角色名"></el-input>
        </el-form-item>
        <el-form-item label="角色描述" prop="description">
          <el-input v-model="roleForm.description" placeholder="请输入角色描述"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitRoleForm()">提交</el-button>
          <el-button @click="resetRoleForm()">重置</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 菜单 -->
    <el-dialog :visible.sync="menuDlgVisible" title="菜单">
      <el-tree
        ref="menuTreeRef"
        :data="menuData"
        show-checkbox
        node-key="id"
        :props="menuTreeProps"
      ></el-tree>
      <div style="display: flex;justify-content: center;margin-top: 20px;">
        <el-button @click="confirmMenu">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import service from "../../router/request";
export default {
  name: "Role",
  data() {
    return {
      roleTableData: [],
      roleTableColumns: [
        { label: "角色名", prop: "name" },
        { label: "描述", prop: "description" }
      ],
      roleDlgVisible: false,
      roleForm: {
        id: 0,
        name: "",
        description: ""
      },
      roleFormRules: {
        name: [{ required: true, message: "角色名不能为空" }],
        description: [{ required: true, message: "角色描述不能为空" }]
      },
      menuData: [],
      menuDlgVisible: false,
      menuTreeProps: { children: "child", label: "menu_name" },
      currentRow: null,
    };
  },
  created() {
    this.getRoles();
  },
  methods: {
    getRoles() {
      service.get("/Role/getRoles").then(res => {
        if (res.status == "succeed") {
          this.roleTableData = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },

    clickUpdate(row) {
      this.roleDlgVisible = true;
      this.$nextTick(() => (this.roleForm = { ...row }));
    },

    submitRoleForm() {
      this.$refs.roleForm.validate(valid => {
        if (valid) {
          service.post("/Role/addOrUpdateRole", this.roleForm).then(res => {
            if (res.status == "succeed") {
              this.getRoles();
              this.roleDlgVisible = false;
              this.$message.success(
                `角色${this.roleForm.id ? "修改" : "添加"}成功`
              );
            } else {
              this.$message.error(res.message);
            }
          });
        }
      });
    },

    resetRoleForm() {
      this.$refs.roleForm.resetFields();
    },

    deleteRole(row) {
      this.$confirm(`确定删除"${row.name}"角色吗？`, "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          service.post("/Role/deleteRole", row).then(res => {
            if (res.status == "succeed") {
              this.getRoles();
              this.$message.success("角色删除成功");
            } else {
              this.$message.error(res.message);
            }
          });
        })
        .catch(() => {});
    },

    getMenuTree(row) {
      service.get("/Role/getMenuForRole", { params: { id: row.id } }).then(res => {
        if (res.status == "succeed") {
          this.menuData = res.data;
          this.$refs.menuTreeRef.setCheckedKeys(this.getCheckedMenu(this.menuData));
        }
      });
    },

    getCheckedMenu(menuData) {
      let ids = [];
      menuData.forEach(item => {
        if (item.child?.length) {
          ids.push(...this.getCheckedMenu(item.child));
        } else {
          item.possess && ids.push(item.id);
        }
      });
      return ids;
    },

    editMenuClick(row) {
      this.getMenuTree(row);
      this.menuDlgVisible = true;
    },

    confirmMenu() {
      let menuCheckedKeys = [...this.$refs.menuTreeRef.getCheckedKeys(), ...this.$refs.menuTreeRef.getHalfCheckedKeys()];
      service.post("/Role/addMenuForRole", { roleId: this.currentRow.id, ids: menuCheckedKeys }).then(res => {
        if (res.status == "succeed") {
          this.$message.success('编辑成功');
        }
      });
    }
  }
};
</script>

<style></style>
