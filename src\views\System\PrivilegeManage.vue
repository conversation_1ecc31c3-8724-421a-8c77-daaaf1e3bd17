<template>
  <div>
    <h1>用户管理</h1>
    <div>
      <AddUser />
    </div>
    <el-table :data="menuList" style="width: 100%">
      <el-table-column prop="name" label="姓名"></el-table-column>
      <el-table-column prop="loginid" label="工号"></el-table-column>
      <el-table-column prop="role_name" label="角色">
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button size="small" type="primary" @click="updatarole(scope.row)"
            >修改角色</el-button
          >
          <el-button type="danger" size="small" @click="deleteMenu(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-dialog :visible.sync="show" title="修改角色">
      <el-form :model="form">
        <el-form-item label="当前角色">
          <el-select v-model="userRow.role_id">
            <el-option
              v-for="item in option"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div>
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="save">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import service from "@/router/request";
import { mapGetters } from "vuex";

import AddUser from "./AddUser.vue";
export default {
  components: { AddUser },
  name: "privilegemanage",
  data() {
    return {
      menuList: [],
      show: false,
      option: [],
      form: {
        thenrole: "",
      },
      userRow: {},
    };
  },
  computed: {},

  mounted() {
    this.getUser();
  },
  methods: {
    cancel() {
      this.show = false; // 关闭对话框
    },
    //获取系统成员
    getUser() {
      service.get("/Privilege/userlist").then((res) => {
        if (res.status == "succeed") {
          this.menuList = res.data;
        }
      });
    },
    handleEdit() {
      this.show = true;
    },
    deleteMenu(row) {
      const loginid = row.loginid;
      // 弹出确认弹窗
      this.$confirm("确定要删除该用户吗", "提示", {
        type: "warning",
      })
        .then(() => {
          service
            .post("/Privilege/DeleteUser", {
              loginid: loginid,
            })
            .then((res) => {
              if (res.status == "succeed") {
                // 请求成功的处理逻辑
                // 根据后端返回的数据，做出相应操作
                this.$message.success("删除用户成功");
                this.getUser();
              }
            });
        })
        .catch(() => {
          // 用户点击了取消按钮
          console.log("取消删除用操作");
        });
    },
    updatarole(row) {
      this.userRow = row;
      this.show = true;

      service.get("/Role/getRoles").then((res) => {
        if (res.status == "succeed") {
          this.option = res.data;
        }
      });
    },
    save() {
      service.post("/Privilege/Updateuserrole", this.userRow).then((res) => {
        if (res.status == "succeed") {
          this.$message.success("更新角色成功");
        } else {
          this.$message.warning("更新角色失败");
        }
        this.show = false;
      });
    },
  },
};
</script>
