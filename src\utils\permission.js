import store from "@/store";

// enum RoleId {
//     Manager = 2,
//     AuditManager = 8,
//     GroupLeader = 1
// }

export function havePermissionId(loginId) {
  return store.state.user.loginId == loginId;
}

export function havePermissionRole(roleId) {
  let memberRole = store.state.user?.roleId || store.state.user?.roleId || 0;
  if (roleId.some) return roleId.some((id) => id == memberRole);
  return memberRole == roleId;
}
