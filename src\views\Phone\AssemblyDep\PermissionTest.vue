<template>
  <div class="permission-test-container">
    <TopNavigationBar title="权限测试"></TopNavigationBar>

    <div class="test-content">
      <div class="permission-info">
        <h3>当前权限信息</h3>
        <div class="info-item">
          <span class="label">用户角色ID:</span>
          <span class="value">{{ currentRoleId }}</span>
        </div>
        <div class="info-item">
          <span class="label">是否有权限(8):</span>
          <span class="value" :class="{ 'has-permission': hasPermission, 'no-permission': !hasPermission }">
            {{ hasPermission ? "有权限" : "无权限" }}
          </span>
        </div>
      </div>

      <div class="test-buttons">
        <h3>权限测试按钮</h3>
        <el-button type="primary" @click="testAction" :disabled="!hasPermission" class="test-btn">
          需要权限的操作
        </el-button>

        <el-button type="success" @click="simulateRoleChange(8)" class="test-btn"> 模拟设置角色为8 </el-button>

        <el-button type="warning" @click="simulateRoleChange(1)" class="test-btn"> 模拟设置角色为1 </el-button>

        <el-button type="info" @click="simulateRoleChange(0)" class="test-btn"> 模拟清除角色 </el-button>
      </div>

      <div class="permission-grid">
        <h3>权限控制网格</h3>
        <div class="grid-container">
          <div
            v-for="(item, index) in testItems"
            :key="index"
            class="grid-item"
            :class="{ disabled: item.requirePermission && !hasPermission }"
            @click="handleItemClick(item)"
          >
            <div class="item-icon">{{ item.icon }}</div>
            <div class="item-label">{{ item.label }}</div>
            <div v-if="item.requirePermission && !hasPermission" class="lock-icon">🔒</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TopNavigationBar from "@/components/XTopNavigationBar.vue";

export default {
  name: "PermissionTest",
  components: {
    TopNavigationBar,
  },
  data() {
    return {
      testItems: [
        { label: "普通功能", icon: "📄", requirePermission: false },
        { label: "管理功能", icon: "⚙️", requirePermission: true },
        { label: "审核功能", icon: "✅", requirePermission: true },
        { label: "查看功能", icon: "👁️", requirePermission: false },
      ],
    };
  },
  computed: {
    hasPermission() {
      // 直接访问 store 状态，确保响应式
      const memberRole = this.$store.state.user?.roleId || this.$store.state.user?.roleId || 0;
      return memberRole === 8;
    },

    currentRoleId() {
      return this.$store.state.user?.roleId || this.$store.state.user?.roleId || 0;
    },
  },
  methods: {
    testAction() {
      if (this.hasPermission) {
        this.$message.success("权限验证通过，操作成功！");
      } else {
        this.$message.error("权限不足，操作失败！");
      }
    },

    simulateRoleChange(roleId) {
      // 模拟角色变更
      const currentPerson = this.$store.state.user || {};
      const updatedInfo = {
        ...currentPerson.info,
        role_id: roleId,
        roleId: roleId,
      };

      this.$store.commit("updatePersonInfo", updatedInfo);
      this.$message.info(`角色已模拟设置为: ${roleId}`);
    },

    handleItemClick(item) {
      if (item.requirePermission && !this.hasPermission) {
        this.$message.warning("权限不足，无法访问此功能");
        return;
      }

      this.$message.success(`点击了: ${item.label}`);
    },
  },
};
</script>

<style scoped>
.permission-test-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: env(safe-area-inset-bottom, 20px);
}

.test-content {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.permission-info,
.test-buttons,
.permission-grid {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.permission-info h3,
.test-buttons h3,
.permission-grid h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  font-weight: 500;
  color: #666;
}

.value {
  font-weight: 600;
  color: #333;
}

.value.has-permission {
  color: #67c23a;
}

.value.no-permission {
  color: #f56c6c;
}

.test-btn {
  margin: 8px 8px 8px 0;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.grid-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 100px;
}

.grid-item:hover {
  background: #e9ecef;
  transform: translateY(-2px);
}

.grid-item.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.grid-item.disabled:hover {
  background: #f8f9fa;
  transform: none;
}

.item-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.item-label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  text-align: center;
}

.lock-icon {
  position: absolute;
  top: 8px;
  right: 8px;
  font-size: 12px;
  opacity: 0.7;
}

@media (max-width: 768px) {
  .test-content {
    padding: 16px;
  }

  .permission-info,
  .test-buttons,
  .permission-grid {
    padding: 16px;
    margin-bottom: 16px;
  }

  .grid-container {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .grid-item {
    padding: 16px;
    min-height: 80px;
  }
}
</style>
