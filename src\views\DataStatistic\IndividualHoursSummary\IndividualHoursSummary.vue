<template>
  <div>
    <div class="search-bar">
      <div class="search-item">
        <span class="label">成员：</span>
        <el-select v-model="searchParams.loginIds" multiple filterable placeholder="请选择成员">
          <el-option
            v-for="(member, idx) of memberList"
            :key="idx"
            :label="member.lastname"
            :value="member.loginid"
          ></el-option>
        </el-select>
      </div>
      <div class="search-item">
        <span class="label">查询月份：</span>
        <el-date-picker v-model="searchParams.month" type="month" placeholder="选择月" value-format="yyyy-MM">
        </el-date-picker>
      </div>
      <el-button class="search-btn" type="primary" @click="getIndividualHoursSummary">搜索</el-button>
      <el-button @click="exportDataMultiple" class="search-btn" type="warning">导出</el-button>
    </div>

    <el-table :data="tableData" border show-summary>
      <el-table-column label="工程号" prop="proNo"></el-table-column>
      <el-table-column label="内容" prop="content"></el-table-column>
      <el-table-column label="工时" prop="hours">
        <el-link slot-scope="scope" @click="toDetail(scope.row)">{{ scope.row.hours }}</el-link>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import service from "@/router/request";
import { xlsx, xlsxMultiple } from "@/utils/utils";

export default {
  name: "IndividualHoursSummary",

  data() {
    return {
      memberList: [],
      searchParams: {
        loginIds: "",
        month: "",
      },
      tableData: [],
      tableConfig: [
        { label: "工程号", prop: "proNo" },
        { label: "内容", prop: "content" },
        { label: "工时", prop: "hours" },
      ],
    };
  },

  mounted() {
    this.getMember();
  },

  methods: {
    getMember() {
      service.get("/public/Getmember1").then((res) => {
        if (res.status == "succeed") {
          this.memberList = res.data;
        }
      });
    },

    getIndividualHoursSummary() {
      if (!this.searchParams.loginIds?.length) return this.$message.warning("请选择成员");
      if (!this.searchParams.month) return this.$message.warning("请选择月份");
      service.post("/DataSummary/getIndividualHoursSummaryMultiple", this.searchParams).then((res) => {
        if (res.status == "succeed") {
          this.tableData = res.data[0].data || [];
        }
      });
    },

    // exportData() {
    //   if (!this.tableData?.length) return this.$message.warning("暂无数据");
    //   let map = {
    //     proNo: "工程号",
    //     content: "内容",
    //     hours: "工时",
    //   };
    //   const loading = this.$loading({
    //     lock: true,
    //     text: "导出中.....",
    //     spinner: "el-icon-loading",
    //     background: "rgba(0, 0, 0, 0.7)",
    //   });
    //   xlsx(this.tableData, map, "个人工时汇总");
    //   loading.close();
    // },

    async exportDataMultiple() {
      // if (!this.tableData?.length) return this.$message.warning("暂无数据");
      if (!this.searchParams.loginIds?.length) return this.$message.warning("请选择成员");
      if (!this.searchParams.month) return this.$message.warning("请选择月份");
      const loading = this.$loading({
        lock: true,
        text: "导出中.....",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      const res = await service.post("/DataSummary/getIndividualHoursSummaryMultiple", this.searchParams);
      let map = { proNo: "工程号", content: "内容", hours: "工时" };
      xlsxMultiple(
        res.data.map((item) => item.data),
        map,
        "个人工时汇总",
        res.data.map((item) => item.name)
      );
      loading.close();
    },

    toDetail(row) {
      this.$router.push({
        path: "individualHoursDetail",
        query: { data: encodeURIComponent(JSON.stringify({ ...this.searchParams, ...row })) },
      });
    },
  },
};
</script>
