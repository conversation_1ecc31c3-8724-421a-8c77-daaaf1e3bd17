<template>
  <div>
    <div class="neirong">
      <el-button @click="$router.back()" icon="el-icon-back" circle></el-button>
      <h1 style="text-align: center">组别内容修改</h1>
      <div class="mb-2" style="display: flex; justify-content: end">
        <el-button @click="openTeamDlg" type="primary" plain size="medium">新增小队</el-button>
      </div>
      <el-descriptions class="mb-2" :column="2" border>
        <el-descriptions-item label="巴组名称">
          {{ groupInfo.groname }}
        </el-descriptions-item>
        <el-descriptions-item label="管理姓名">
          {{ groupInfo.grohmname }}
        </el-descriptions-item>
        <el-descriptions-item :span="2" label="描述">
          {{ groupInfo.groremarks }}
        </el-descriptions-item>
      </el-descriptions>
      <el-table :data="teamList" border>
        <el-table-column type="expand">
          <template v-slot="props">
            <el-table size="mini" :data="props.row.members">
              <el-table-column type="index" label="编号"></el-table-column>
              <el-table-column label="姓名" prop="memname"></el-table-column>
              <el-table-column label="工号" prop="memno"></el-table-column>
              <el-table-column label="职务" prop="jobtitlename"></el-table-column>
              <el-table-column label="角色" prop="memrole"></el-table-column>
              <el-table-column label="分配比" prop="distratio"></el-table-column>
              <el-table-column label="备注" prop="memremarks"></el-table-column>
              <el-table-column label="操作" width="80">
                <template v-slot:default="scope">
                  <el-button @click="deleteMember(scope.row)" type="danger" plain size="small">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </el-table-column>
        <el-table-column label="队名" prop="groname"></el-table-column>
        <el-table-column label="团队描述" prop="groremarks"></el-table-column>
        <el-table-column label="队长" prop="grohmname"></el-table-column>
        <el-table-column label="操作" width="250">
          <template v-slot:default="scope">
            <el-button @click="openMemberDlg(scope.row)" type="primary" plain size="small">新增成员</el-button>
            <el-button @click="openTeamEditDlg(scope.row)" type="warning" size="small" plain>编辑</el-button>
            <el-button @click="deleteTeam(scope.row)" type="danger" plain size="small">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-dialog title="添加成员" :visible.sync="memberDlg" width="80%">
      <el-select v-model="memberSearchKey" style="width: 100px" class="mr-1">
        <el-option v-for="item of memberSearchKeys" :key="item.value" v-bind="item"></el-option>
      </el-select>
      <el-input
        class="mb-2"
        v-model="memberSearchValue"
        placeholder="请输入搜索关键字"
        @input="searchMember"
        style="width: auto"
      ></el-input>
      <el-table
        ref="memberTable"
        :data="filteredMemberList"
        :row-key="(row) => row.loginid"
        tooltip-effect="dark"
        style="width: auto"
        max-height="500"
        @selection-change="(selection) => (memberSelection = selection)"
        @row-click="(row) => !row.groname && $refs.memberTable.toggleRowSelection(row)"
        v-loading="memberLoading"
      >
        <el-table-column
          type="selection"
          width="55"
          :selectable="(row) => !row.groname"
          :reserve-selection="true"
        ></el-table-column>
        <el-table-column prop="lastname" label="姓名"></el-table-column>
        <el-table-column prop="loginid" label="工号"></el-table-column>
        <el-table-column prop="sex" label="性别"></el-table-column>
        <el-table-column prop="departmentname" label="部门"></el-table-column>
        <el-table-column prop="jobtitlename" label="职务"></el-table-column>
        <el-table-column prop="memrole" label="角色（必选）" width="160">
          <template v-slot="scope">
            <el-select v-model="scope.row.memrole" filterable placeholder="请选择">
              <el-option
                v-for="item in roleOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="distratio" label="分配比（必填）" width="220">
          <template v-slot="scope">
            <el-input v-model="scope.row.distratio" @click.native.stop placeholder="请输入分配比（没有就填0）">
              {{ scope.row.distratio }}
            </el-input>
          </template>
        </el-table-column>
      </el-table>

      <div slot="footer" class="dialog-footer">
        <el-button @click="closeMemberDlg">取 消</el-button>
        <el-button type="primary" @click="addMember">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="小队" :visible.sync="teamDlg" width="40%" :close-on-click-modal="false">
      <el-form
        :model="teamForm"
        label-width="100px"
        style="padding-right: 50px"
        :rules="teamFormRules"
        ref="teamFormRef"
      >
        <el-form-item label="队名" prop="groname">
          <el-input v-model="teamForm.groname" placeholder="队名"></el-input>
        </el-form-item>
        <el-form-item label="巴组编号" prop="supNo">
          <el-input :value="(teamForm.supNo = groupInfo.grono)" placeholder="巴组编号" readonly></el-input>
        </el-form-item>
        <el-form-item label="队长" prop="grohmname">
          <el-autocomplete
            value-key="lastname"
            v-model="teamForm.grohmname"
            :fetch-suggestions="querySearchAsync"
            placeholder="请输入内容"
            @select="(item) => (teamForm.groheadman = item.loginid)"
            @focus="getMemberList"
          ></el-autocomplete>
        </el-form-item>
        <el-form-item label="队长工号" prop="groheadman">
          <el-input :value="teamForm.groheadman" placeholder="队长工号" readonly></el-input>
        </el-form-item>
        <el-form-item label="团队描述" prop="groremarks">
          <el-input v-model="teamForm.groremarks" placeholder="团队描述"></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="closeTeamDlg">取 消</el-button>
        <el-button type="primary" @click="addTeam">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import service from "@/router/request";

export default {
  data: () => ({
    groupInfo: {},
    teamList: [],

    teamDlg: false,
    teamForm: {
      groname: "",
      supNo: "",
      groheadman: "",
      grohmname: "",
      groremarks: "",
    },
    teamFormRules: {
      groname: [{ required: true, message: "队名不能为空", trigger: "blur" }],
      grohmname: [{ required: true, message: "队长不能为空", trigger: "blur" }],
    },

    currentTeam: null,
    memberDlg: false,
    memberSearchKey: "departmentname",
    memberSearchValue: "",
    memberSearchKeys: [
      { label: "部门", value: "departmentname" },
      { label: "工号", value: "loginid" },
      { label: "姓名", value: "lastname" },
    ],
    memberLoading: false,
    filteredMemberList: [],
    memberList: [],
    memberSelection: [],
    roleOptions: [
      // 用于存储下拉选项的内容
      { label: "队长", value: "队长" },
      { label: "主装", value: "主装" },
      { label: "主调", value: "主调" },
      { label: "物料", value: "物料" },
      { label: "组员", value: "组员" },
      { label: "学徒", value: "学徒" },
    ],
  }),

  mounted() {
    this.groupInfo = JSON.parse(decodeURIComponent(this.$route.query.groupInfo || "{}"));
    this.getTeams();
  },

  methods: {
    getTeams() {
      service.get("/Bazu/bazuTeamMember", { params: { grono: this.groupInfo.grono } }).then((res) => {
        if (res.status == "succeed") {
          this.teamList = res.data;
        }
      });
    },

    openTeamDlg() {
      this.teamDlg = true;
    },

    closeTeamDlg() {
      this.$refs.teamFormRef.resetFields();
      this.teamDlg = false;
    },

    querySearchAsync(queryString, cb) {
      let results = queryString
        ? this.memberList.filter((item) => item.lastname.toLowerCase().indexOf(queryString.toLowerCase()) === 0)
        : this.memberList;

      clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        cb(results);
      }, 300);
    },

    addTeam() {
      this.$refs.teamFormRef.validate((valid) => {
        if (valid) {
          service
            .post(this.teamForm.grono ? "/Bazu/UpdateTeam" : "/Bazu/AddTeam", this.teamForm)
            .then((res) => {
              if (res.status == "succeed") {
                this.getTeams();
                this.$message.success("保存成功！");
                this.closeTeamDlg();
              }
            })
            .catch((error) => this.$message.error("网络异常"));
        }
      });
    },

    deleteTeam(row) {
      if (row.members.length) return this.$message.warning("小组内存在成员不能删除");

      this.$confirm("确定要删除该小组吗?", "提示", { type: "warning" })
        .then(async () => {
          const res = await service
            .post("Bazu/DeleteTeam", { grono: row.grono })
            .catch(() => this.$message.error("网络异常"));
          if (res.status == "succeed") {
            this.getTeams();
            this.$message.success("删除小队成功");
          } else {
            this.$message.error(`删除小队失败: ${res.err}`);
          }
        })
        .catch(() => {});
    },

    openMemberDlg(row) {
      this.getMemberList();
      this.memberDlg = true;
      this.currentTeam = row;
    },

    closeMemberDlg() {
      this.memberDlg = false;
      this.memberSelection = [];
    },

    getMemberList() {
      this.memberList = [];
      this.filteredMemberList = [];
      this.memberLoading = true;
      service
        .get("/Public/Getmember")
        .then((res) => {
          if (res.status == "succeed") {
            this.memberList = res.data || [];
            this.filteredMemberList = res.data || [];
          } else {
            this.$message.error("获取数据失败");
          }
        })
        .catch((error) => {
          this.$message.error("获取数据失败");
        })
        .finally(() => (this.memberLoading = false));
    },

    searchMember() {
      this.filteredMemberList = this.memberList.filter((item) =>
        item[this.memberSearchKey].includes(this.memberSearchValue)
      );
    },

    addMember() {
      // let a;
      // if ((a = this.memberSelection.find((item) => item.groname)))
      //   return this.$message.error(`${a.lastname}已经在${a.groname}`);
      if (this.memberSelection.length == 0) return this.$message.warning("请选中成员");
      if (this.memberSelection.some((item) => !item.memrole)) return this.$message.warning("存在未选择角色的情况");
      if (this.memberSelection.some((item) => !item.distratio)) return this.$message.warning("存在未分配分配比的情况");

      service
        .post("/Bazu/AddTeamMember", {
          memberlist: this.memberSelection.map((item) => ({
            memname: item.lastname,
            memno: item.loginid,
            jobtitlename: item.jobtitlename,
            memrole: item.memrole,
            distratio: item.distratio,
          })),
          tmeno: this.currentTeam.grono,
        })
        .then((res) => {
          if (res.status == "succeed") {
            this.$message.success("保存成功！");
            this.closeMemberDlg();
            this.getTeams();
          } else {
            this.$message.error(res.message);
          }
        });
    },

    deleteMember(row) {
      this.$confirm("确定要删除该成员吗?", "提示", { type: "warning" })
        .then(async () => {
          const res = await service
            .post("/Bazu/DeleteTeamMember", { memno: row.memno, tmeno: row.tmeno })
            .catch((error) => this.$message.error("网络异常"));
          if (res.status == "succeed") {
            this.getTeams();
            this.$message.success("删除成功");
          } else {
            this.$message.error("删除失败");
          }
        })
        .catch(() => {});
    },

    openTeamEditDlg(row) {
      this.teamDlg = true;
      this.teamForm = row;
    },
  },
};
</script>
