<template>
  <div class="bg-gray-50 min-h-screen">
    <TopNavigationBar title="全名改善提案平台"></TopNavigationBar>
    <div class="container mx-auto py-6">
      <!-- 页面标题 -->
      <div class="mb-6 text-center">
        <h1 class="text-[clamp(1.5rem,3vw,2rem)] font-bold text-gray-800">全名改善提案平台</h1>
        <p class="text-gray-600 mt-2">提交您的改善建议，共同推动公司进步</p>
      </div>

      <!-- 表单卡片 -->
      <div class="bg-white rounded-xl shadow-lg mb-6 transition-all duration-300 hover:shadow-xl">
        <el-form :model="formData" :rules="rules" ref="proposalForm" label-width="80px" class="p-6">
          <!-- 基本信息 -->
          <div class="mb-8">
            <h2 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <i class="fa fa-user-circle-o text-blue-500 mr-2"></i>
              基本信息
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2">
              <el-form-item label="姓名" prop="proposer">
                <el-input
                  v-model="formData.proposer"
                  placeholder="请输入您的姓名"
                  clearable
                  @blur="validateField('proposer')"
                  disabled
                ></el-input>
              </el-form-item>

              <el-form-item label="部门" prop="department">
                <el-input
                  v-model="formData.department"
                  placeholder="请输入您的部门"
                  clearable
                  @blur="validateField('department')"
                  disabled
                ></el-input>
              </el-form-item>

              <el-form-item label="职位" prop="jobTitle">
                <el-input v-model="formData.jobTitle" placeholder="请输入您的职位" clearable disabled></el-input>
              </el-form-item>

              <el-form-item label="联系方式" prop="contact">
                <el-input
                  v-model="formData.contact"
                  placeholder="请输入您的联系方式"
                  clearable
                  @blur="validateField('contact')"
                ></el-input>
                <div class="text-xs text-gray-500 mt-1">支持手机号码或邮箱</div>
              </el-form-item>
            </div>
          </div>

          <!-- 提案信息 -->
          <div class="mb-8">
            <h2 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <i class="fa fa-lightbulb-o text-yellow-500 mr-2"></i>
              提案信息
            </h2>

            <el-form-item label="提案标题" prop="title">
              <el-input
                v-model="formData.title"
                placeholder="请输入提案标题"
                clearable
                @blur="validateField('title')"
              ></el-input>
            </el-form-item>

            <el-form-item label="提案标签" prop="tags">
              <el-select v-model="formData.tags" multiple placeholder="请选择提案类型" @change="validateField('tags')">
                <el-option v-for="opt in IMPROVEMENT_TAGS" v-bind="opt"></el-option>
              </el-select>
              <div class="text-xs text-gray-500 mt-1">可多选，最多选择3个标签</div>
            </el-form-item>

            <el-form-item label="紧急程度" prop="urgency">
              <el-select v-model="formData.urgency" placeholder="请选择紧急程度" @change="validateField('urgency')">
                <el-option label="正常" :value="1"></el-option>
                <el-option label="重要" :value="2"></el-option>
                <el-option label="紧急" :value="3"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="现状描述" prop="currentSituation">
              <el-input
                type="textarea"
                v-model="formData.currentSituation"
                placeholder="请描述当前存在的问题或不足"
                :rows="4"
                show-word-limit
                maxlength="1000"
                @blur="validateField('currentSituation')"
              ></el-input>
              <div class="text-xs text-gray-500 mt-1">请详细描述现状，至少10个字符</div>
            </el-form-item>

            <el-form-item label="改善建议" prop="suggestion">
              <el-input
                type="textarea"
                v-model="formData.suggestion"
                placeholder="请详细描述您的改善建议"
                :rows="6"
                show-word-limit
                maxlength="2000"
                @blur="validateField('suggestion')"
              ></el-input>
              <div class="text-xs text-gray-500 mt-1">请提供具体的改善措施和方案，至少20个字符</div>
            </el-form-item>

            <el-form-item label="预期效果" prop="expectedEffect">
              <el-input
                type="textarea"
                v-model="formData.expectedEffect"
                placeholder="请描述实施该建议后的预期效果"
                :rows="4"
                show-word-limit
                maxlength="1000"
                @blur="validateField('expectedEffect')"
              ></el-input>
            </el-form-item>

            <el-form-item label="实施难度" prop="difficulty">
              <el-radio-group v-model="formData.difficulty">
                <el-radio :label="1">简单</el-radio>
                <el-radio :label="2">中等</el-radio>
                <el-radio :label="3">困难</el-radio>
              </el-radio-group>
            </el-form-item>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <el-form-item label="实施部门" prop="implementationDepartment">
                <el-select
                  v-model="formData.implementationDepartment"
                  placeholder="请选择实施部门"
                  filterable
                  class="w-full"
                >
                  <el-option v-for="dept in departmentList" :key="dept.value" :label="dept.label" :value="dept.value" />
                </el-select>
                <div class="text-xs text-gray-500 mt-1">选择负责实施的部门</div>
              </el-form-item>

              <el-form-item label="预期完成日期" prop="finishDate">
                <el-date-picker
                  v-model="formData.finishDate"
                  type="date"
                  placeholder="请选择完成日期"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  class="w-full"
                />
                <div class="text-xs text-gray-500 mt-1">预计完成改善的日期</div>
              </el-form-item>
            </div>
          </div>

          <!-- 附件上传 -->
          <div class="mb-8">
            <h2 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <i class="fa fa-paperclip text-green-500 mr-2"></i>
              附件上传
            </h2>
            <el-form-item label="上传附件">
              <el-upload
                ref="uploadRef"
                action="#"
                accept=".jpg,.jpeg,.png,.pdf,.doc,.docx,.xls,.xlsx"
                multiple
                :auto-upload="false"
                class="upload-demo"
                :before-remove="beforeRemove"
                :on-change="handleFileChange"
                :on-preview="handlePreview"
              >
                <el-button size="small" type="primary"> <i class="fa fa-plus mr-1"></i>选择文件 </el-button>
                <div slot="tip" class="el-upload__tip text-xs text-gray-500 mt-2">
                  支持 JPG, PNG, PDF, Word, Excel 等格式，单个文件不超过10MB
                </div>
              </el-upload>
            </el-form-item>
          </div>

          <!-- 提交按钮 -->
          <div class="flex justify-center pt-4 border-t border-gray-100">
            <el-button
              type="primary"
              size="large"
              @click="submitForm"
              class="w-full md:w-auto flex items-center justify-center"
              :loading="submitting"
            >
              <i class="fa fa-paper-plane mr-2"></i>
              <span>{{ submitting ? "提交中..." : "提交提案" }}</span>
            </el-button>
          </div>
        </el-form>
      </div>

      <!-- 提案提交成功提示 -->
      <el-dialog
        :visible.sync="showSuccessMessage"
        title="提交成功"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        width="30%"
      >
        <template #content>
          <div class="text-center py-4">
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
              <i class="fa fa-check text-green-500 text-2xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">提案提交成功！</h3>
            <p class="text-gray-500 mb-6">感谢您的宝贵建议，我们将尽快审核处理。</p>
            <div class="text-sm text-gray-600 mb-4">
              <p>
                提案编号: <span class="font-semibold text-gray-800">{{ proposalNumber }}</span>
              </p>
              <p>
                提交时间: <span class="font-semibold text-gray-800">{{ submitTime }}</span>
              </p>
            </div>
          </div>
        </template>
        <template #footer>
          <div class="text-center">
            <el-button type="primary" @click="closeSuccessMessage" class="px-6"> 返回列表 </el-button>
          </div>
        </template>
      </el-dialog>

      <el-dialog :visible.sync="previewDialogVisible" title="文件预览" width="80%">
        <FilePreview
          :file-url="previewFileUrl"
          :file-name="previewFileName"
          @close="previewDialogVisible = false"
        ></FilePreview>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import TopNavigationBar from "@/components/XTopNavigationBar.vue";
import { debounce, MessageUtil } from "@/utils/utils";
import FilePreview from "@/components/XFilePreview.vue";
import service from "@/router/request";
import { IMPROVEMENT_TAGS } from "@/utils/constants";

export default {
  components: { TopNavigationBar, FilePreview },
  data() {
    return {
      IMPROVEMENT_TAGS,
      formData: {
        proposer: "",
        department: "",
        jobTitle: "",
        contact: "",

        title: "",
        tags: [],
        urgency: 1,
        currentSituation: "",
        suggestion: "",
        expectedEffect: "",
        difficulty: 1,
        implementationDepartment: "",
        finishDate: "",
        attachments: [],
      },
      rules: {
        proposer: [{ required: true, message: "请输入姓名", trigger: "blur" }],
        department: [{ required: true, message: "请输入部门", trigger: "blur" }],
        contact: [
          { required: true, message: "请输入联系方式", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              const phoneReg = /^1[3-9]\d{9}$/;
              const emailReg = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

              if (phoneReg.test(value) || emailReg.test(value)) {
                callback();
              } else {
                callback(new Error("请输入正确的手机号码或邮箱"));
              }
            },
            trigger: "blur",
          },
        ],
        title: [
          { required: true, message: "请输入提案标题", trigger: "blur" },
          { min: 5, message: "提案标题至少5个字符", trigger: "blur" },
        ],
        tags: [
          { required: true, message: "请选择提案类型", trigger: "change" },
          {
            validator: (rule, value, callback) => {
              if (value && value.length > 3) {
                callback(new Error("最多选择3个标签"));
              } else {
                callback();
              }
            },
            trigger: "change",
          },
        ],
        urgency: [{ required: true, message: "请选择紧急程度", trigger: "change" }],
        currentSituation: [
          { required: true, message: "请描述现状", trigger: "blur" },
          { min: 10, message: "现状描述至少10个字符", trigger: "blur" },
        ],
        suggestion: [
          { required: true, message: "请输入改善建议", trigger: "blur" },
          { min: 20, message: "改善建议至少20个字符", trigger: "blur" },
        ],
        expectedEffect: [
          { required: true, message: "请描述预期效果", trigger: "blur" },
          { min: 10, message: "预期效果描述至少10个字符", trigger: "blur" },
        ],
      },
      uploadFiles: [], // 待上传文件列表
      uploadedFiles: [], // 已上传文件列表
      uploading: false,
      submitting: false,
      showSuccessMessage: false,
      showErrorMessage: false,
      errorMessage: "",
      proposalNumber: "",
      submitTime: "",
      uploadUrl: "https://example.com/upload", // 替换为实际的上传API
      saveDraftTimer: null,

      previewDialogVisible: false,
      previewFileName: "",
      previewFileUrl: "",

      departmentList: [
        { value: "生产部", label: "生产部" },
        { value: "质量部", label: "质量部" },
        { value: "技术部", label: "技术部" },
        { value: "设备部", label: "设备部" },
        { value: "安全部", label: "安全部" },
        { value: "物流部", label: "物流部" },
        { value: "采购部", label: "采购部" },
        { value: "人事部", label: "人事部" },
      ],
    };
  },
  created() {
    // 尝试加载草稿
    this.loadDraft();

    // 自动保存草稿
    this.startAutoSave();
    this.init();
  },
  beforeDestroy() {
    // 清除定时器
    if (this.saveDraftTimer) {
      clearInterval(this.saveDraftTimer);
    }
  },
  methods: {
    init() {
      // 初始化表单数据
      this.formData.proposer = this.$store.state.user.name || "";
      this.formData.department = this.$store.state.user.departmentName || "无";
      this.formData.jobTitle = this.$store.state.user.jobTitleName || "无";
      this.formData.contact = this.$store.state.user.mobile || "";

      this.getDepartments();
    },

    getDepartments() {
      service.get("/Department/GetDepartments").then((res) => {
        if (res.status == "succeed") {
          this.departmentList = res.data?.map((item) => ({ value: item.name, label: item.name }));
        }
      });
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return "0 Bytes";

      const k = 1024;
      const sizes = ["Bytes", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));

      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    },

    // 验证单个字段
    validateField(field) {
      this.$refs.proposalForm.validateField(field);
    },

    // 上传前检查
    beforeUpload(file) {
      const isJPG = file.type === "image/jpeg";
      const isPNG = file.type === "image/png";
      const isPDF = file.type === "application/pdf";
      const isDOC = file.type === "application/msword";
      const isDOCX = file.type === "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
      const isXLS = file.type === "application/vnd.ms-excel";
      const isXLSX = file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";

      const isAllowed = isJPG || isPNG || isPDF || isDOC || isDOCX || isXLS || isXLSX;

      if (!isAllowed) {
        MessageUtil.warning("上传文件格式不正确，仅支持 JPG, PNG, PDF, Word, Excel 格式");
        return false;
      }

      const isLt10M = file.size / 1024 / 1024 < 50;
      if (!isLt10M) {
        MessageUtil.warning("上传文件大小不能超过 50MB");
        return false;
      }

      return true;
    },

    handlePreview(file) {
      this.previewFileUrl = file.url || URL.createObjectURL(file.raw);
      this.previewFileName = file.name;
      this.previewDialogVisible = true;
    },

    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`, "", { customClass: "phone-message-box" });
    },

    handleFileChange(file, fileList) {
      this.formData.attachments = fileList;
    },

    // 提交上传
    async submitUpload() {
      // 筛选已上传和未上传的文件
      const uploadedFiles = this.formData.attachments.filter((f) => f.url);
      const unuploadedFiles = this.formData.attachments.filter((f) => !f.url);

      let urls = uploadedFiles.map((f) => f.url);

      if (unuploadedFiles.length > 0) {
        const formData = new FormData();

        for (const file of unuploadedFiles) {
          if (file.raw && this.beforeUpload(file.raw)) formData.append("files", file.raw || file);
          else throw new Exception();
        }

        formData.append("type", "proposal"); // 假设上传类型为提案

        const res = await service.post("/Upload/UploadFile", formData);

        if (res.status === "succeed" && Array.isArray(res.data)) {
          urls = urls.concat(res.data);
        } else {
          throw new Exception(res.message);
        }
      }

      return urls.join(",");
    },

    // 开始自动保存草稿
    startAutoSave() {
      // 使用防抖处理，避免频繁保存
      const debouncedSave = debounce(this.saveDraft, 3000);

      // 监听表单数据变化
      this.$watch(
        "formData",
        () => {
          debouncedSave();
        },
        { deep: true }
      );
    },

    // 保存草稿
    saveDraft() {
      try {
        // 排除不需要保存的字段
        const draftData = {
          ...this.formData,
          attachments: [...this.uploadedFiles],
        };

        localStorage.setItem("proposal_draft", JSON.stringify(draftData));
      } catch (error) {
        console.error("保存草稿失败:", error);
      }
    },

    // 加载草稿
    loadDraft() {
      try {
        const draft = localStorage.getItem("proposal_draft");
        if (draft) {
          const draftData = JSON.parse(draft);
          this.formData = {
            ...this.formData,
            ...draftData,
            tags: draftData.tags || [],
            urgency: draftData.urgency || 1,
            difficulty: draftData.difficulty || 1,
          };

          // 恢复已上传文件
          this.uploadedFiles = draftData.attachments || [];

          MessageUtil.info("已恢复上次保存的草稿");
        }
      } catch (error) {
        console.error("加载草稿失败:", error);
      }
    },

    // 清除草稿
    clearDraft() {
      localStorage.removeItem("proposal_draft");
    },

    // 提交表单
    async submitForm() {
      this.$refs.proposalForm.validate(async (valid) => {
        if (valid) {
          this.submitting = true;
          let attachments = "";
          try {
            attachments = await this.submitUpload();
          } catch (e) {
            this.submitting = false;
            return e.message && MessageUtil.warning(`文件上传失败: ${e.message}`);
          }
          let result = {
            ...this.formData,
            tags: this.formData.tags.join(","),
            proposer: this.$store.state.user.loginId,
            attachments,
          };
          console.log("提交的提案数据:", result);
          let url = result.id ? "/ImprovementProposal/UpdateProposal" : "/ImprovementProposal/AddProposal";

          service
            .post(url, result)
            .then((res) => {
              this.clearDraft();
              MessageUtil.success("提案提交成功");
            })
            .catch((e) => {
              MessageUtil.error("提案提交失败: " + e);
            })
            .finally(() => {
              this.submitting = false;
            });
        } else {
          MessageUtil.error("表单填写不完整，请检查");
          return false;
        }
      });
    },

    // 关闭成功提示
    closeSuccessMessage() {
      this.showSuccessMessage = false;
      // 重置表单
      this.resetForm();
      // 跳转到提案列表页面（实际项目中实现）
      this.$router.push({ name: "ProposalList" });
    },

    // 重置表单
    resetForm() {
      this.$refs.proposalForm.resetFields();
      this.uploadFiles = [];
      this.uploadedFiles = [];
      this.formData.attachments = [];
    },
  },
};
</script>

<style scoped>
.upload-demo {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: center;
}
</style>
