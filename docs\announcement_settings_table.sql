-- 公告栏设置表
-- 用于存储公告栏的滚动速度和高度设置

-- 创建表
CREATE TABLE announcement_settings (
    id INT IDENTITY(1,1) PRIMARY KEY,
    scroll_speed INT NOT NULL DEFAULT 20,           -- 滚动速度（px/s）
    scroll_height INT NOT NULL DEFAULT 120,         -- 滚动区域高度（px）
    created_at DATETIME2 NOT NULL DEFAULT GETDATE(), -- 创建时间
    updated_at DATETIME2 NOT NULL DEFAULT GETDATE(), -- 更新时间
    updated_by INT NULL,                            -- 更新人ID
    
    -- 约束
    CONSTRAINT CK_announcement_settings_scroll_speed 
        CHECK (scroll_speed >= 10 AND scroll_speed <= 50),
    CONSTRAINT CK_announcement_settings_scroll_height 
        CHECK (scroll_height >= 80 AND scroll_height <= 200)
);

-- 添加注释
EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'公告栏设置表，存储滚动速度和高度配置', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'announcement_settings';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'主键ID', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'announcement_settings',
    @level2type = N'COLUMN', @level2name = N'id';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'滚动速度，单位px/s，范围10-50', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'announcement_settings',
    @level2type = N'COLUMN', @level2name = N'scroll_speed';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'滚动区域高度，单位px，范围80-200', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'announcement_settings',
    @level2type = N'COLUMN', @level2name = N'scroll_height';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'创建时间', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'announcement_settings',
    @level2type = N'COLUMN', @level2name = N'created_at';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'更新时间', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'announcement_settings',
    @level2type = N'COLUMN', @level2name = N'updated_at';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'更新人ID，关联用户表', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'announcement_settings',
    @level2type = N'COLUMN', @level2name = N'updated_by';

-- 插入默认设置
INSERT INTO announcement_settings (scroll_speed, scroll_height, created_at, updated_at)
VALUES (20, 120, GETDATE(), GETDATE());

-- 创建更新时间触发器
CREATE TRIGGER tr_announcement_settings_update
ON announcement_settings
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    
    UPDATE announcement_settings 
    SET updated_at = GETDATE()
    FROM announcement_settings a
    INNER JOIN inserted i ON a.id = i.id;
END;

-- 查询示例
-- 获取当前设置
SELECT 
    id,
    scroll_speed,
    scroll_height,
    created_at,
    updated_at,
    updated_by
FROM announcement_settings
ORDER BY id DESC;

-- 更新设置示例
-- UPDATE announcement_settings 
-- SET scroll_speed = 25, scroll_height = 150, updated_by = 1
-- WHERE id = 1;

-- 重置为默认设置示例
-- UPDATE announcement_settings 
-- SET scroll_speed = 20, scroll_height = 120, updated_by = 1
-- WHERE id = 1;
