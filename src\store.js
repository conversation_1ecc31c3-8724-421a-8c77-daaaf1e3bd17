import { Message, MessageBox } from "element-ui";
import Vue from "vue";
import Vuex from "vuex";
import service from "./router/request";
import router from "./router";
import { LocalStorage } from "./utils/utils.js";

Vue.use(Vuex);

const store = new Vuex.Store({
  state: {
    //当前选中标签数组
    editableTabs: [
      {
        title: "首页",
        name: "首页",
        index: "/default",
      },
    ],
    //当前选中标签
    editableTabsValue: "",
    user: {},
    indexViewInstance: null,
  },

  mutations: {
    addEditableTabs(state, tab) {
      //根据tab.name 查询 editableTabs 是否已经存在
      let idx = state.editableTabs.findIndex((item) => item.name === tab.name);
      if (idx === -1) {
        //不存在则可以添加
        state.editableTabs.push({
          title: tab.name,
          name: tab.name,
          index: tab.path,
        });
      }
      //表示前一个标签，删除标签时，要显示的标签
      state.editableTabsValue = tab.name;
    },

    // 更新用户信息
    updatePersonInfo(state, personInfo) {
      // 使用 Vue.set 确保响应式更新
      Vue.set(state, "user", {
        ...state.user,
        ...personInfo,
      });
    },
  },

  actions: {
    //退出账号
    logout({ state }, isLogout = true) {
      let handler = () => {
        state.user = {}; // 清空用户信息
        router.push("/login");
        const homeTab = state.editableTabs.find((tab) => tab.name === "首页"); // 获取首页标签页
        state.editableTabs = [homeTab]; // 只保留首页标签
        state.editableTabsValue = "首页"; // 设置当前标签页为首页
        // state.indexViewInstance?.menu_data.forEach((item) => state.indexViewInstance.$refs.menuRef.close(item.path));

        const stateToSave = { ...state, indexViewInstance: null };
        LocalStorage.setItem("store", JSON.stringify(stateToSave));
      };
      isLogout
        ? MessageBox.confirm("确定要退出当前账号？")
            .then(() => {
              service.post("/Logout/logout").then((res) => {
                if (res.status == "succeed") {
                  handler();
                } else {
                  // Message.error("登出失敗", res.err);
                }
              });
              // if (this.$vnode) {
              //     this.$vnode.parent.componentInstance.cache = {};
              // }
            })
            .catch(() => {
              // 用户点击取消按钮时的操作
              // 可以不做任何操作，或者根据需要添加逻辑
            })
        : handler();
    },
  },
});

// 从本地存储中恢复状态
try {
  const savedState = JSON.parse(LocalStorage.getItem("store"));
  if (savedState) {
    store.replaceState(Object.assign(store.state, savedState));
  }
} catch (error) {
  console.error("Failed to load state from local storage:", error);
}

export default store;
